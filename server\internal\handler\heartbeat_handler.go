package handler

import (
	"fmt"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

// HeartbeatHandler 心跳处理器
type HeartbeatHandler struct {
	authService      *service.AuthService
	heartbeatService *service.HeartbeatService
	updateService    *service.UpdateService
}

// NewHeartbeatHandler 创建新的心跳处理器
func NewHeartbeatHandler(authService *service.AuthService, heartbeatService *service.HeartbeatService, updateService *service.UpdateService) *HeartbeatHandler {
	return &HeartbeatHandler{
		authService:      authService,
		heartbeatService: heartbeatService,
		updateService:    updateService,
	}
}

// HandleHeartbeatDirect 直接处理心跳请求（用于二进制协议）
func (h *HeartbeatHandler) HandleHeartbeatDirect(token string, clientVersion string) (*model.User, int, bool, error) {
	// 打印日志，便于排查
	logger.Debug("收到心跳请求: Token=%s", token)

	// 验证token
	user, err := h.authService.ValidateToken(token)
	if err != nil {
		logger.Error("Token验证失败: %v, Token=%s", err, token)
		return nil, 0, false, fmt.Errorf("token验证失败: %v", err)
	}

	// 检查权限降级情况（防止时间回调攻击）
	// 只有登录时有高级权限的用户，在权限降级为0时才需要强制重新登录
	if user.Permission > 0 {
		permissionService := service.NewPermissionService(h.authService.GetDB(), h.authService.GetCache())
		currentPermissionLevel, err := permissionService.GetPermissionLevel(user.UID, "client")
		if err != nil {
			logger.Error("权限检查失败: QQ号=%d, UID=%d, Error=%v", user.Uin, user.UID, err)
			return nil, 0, false, fmt.Errorf("权限检查失败: %v", err)
		}

		// 如果登录时有高级权限，但现在权限降级为0，则拒绝心跳强制重新登录
		if currentPermissionLevel == 0 {
			logger.Warn("用户权限已降级，强制重新登录: QQ号=%d, UID=%d, 登录时权限=%d, 当前权限=%d",
				user.Uin, user.UID, user.Permission, currentPermissionLevel)
			return nil, 0, false, fmt.Errorf("权限已过期，请重新登录")
		}

		logger.Debug("权限检查通过: QQ号=%d, 登录时权限=%d, 当前权限=%d",
			user.Uin, user.Permission, currentPermissionLevel)
	}

	// 使用用户的UIN作为设备标识，只更新HeartbeatService中的心跳时间（纯内存操作）
	deviceID := fmt.Sprintf("%d", user.Uin)
	if h.heartbeatService != nil {
		if err := h.heartbeatService.UpdateHeartbeat(deviceID); err != nil {
			return nil, 0, false, fmt.Errorf("更新心跳时间失败: %v", err)
		}
	} else {
		return nil, 0, false, fmt.Errorf("心跳服务未初始化")
	}

	// 获取当前在线用户数量
	onlineCount := 0
	if h.heartbeatService != nil {
		onlineCount = h.heartbeatService.GetOnlineUserCount()
	}

	// 检查是否需要强制更新
	forceUpdate := false
	if h.updateService != nil && clientVersion != "" {
		// 直接比较客户端版本与最新版本
		forceUpdate = h.updateService.IsVersionOutdated("bns-helper", clientVersion)
	}

	logger.Info("心跳成功: QQ号=%d, 登录权限: %d, 强制更新: %v", user.Uin, user.Permission, forceUpdate)
	return user, onlineCount, forceUpdate, nil
}
