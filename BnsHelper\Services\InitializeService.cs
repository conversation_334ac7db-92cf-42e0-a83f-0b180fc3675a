﻿using Microsoft.Web.WebView2.Wpf;
using System.IO;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Data.Models;
using Xylia.Preview.UI.Controls;
using Xylia.Preview.UI.GameUI.Scene.Game_Tooltip;
using Xylia.Preview.UI.GameUI.Scene.Game_Tooltip2;

namespace Xylia.BnsHelper.Services;
/// <summary>
/// Provides initialization logic for registering application settings, tooltips, and configuration properties.
/// </summary>
/// <remarks>This service is responsible for setting up application-wide configurations, such as night mode
/// settings,  tooltip templates for specific types, and WebView2 user data folder properties. It is intended to be used
/// during the application's startup process to ensure all necessary components are properly initialized.</remarks>
internal class InitializeService : IService
{
	public void Register()
	{
		// skin
		SettingHelper.Default.NightMode = SettingHelper.Default.NightMode;

		// tooltip
		BnsTooltipHolder.RegisterTemplate<ItemTooltipPanel>(typeof(Item));
		BnsTooltipHolder.RegisterTemplate<ItemGraphReceipeTooltipPanel>(typeof(ItemGraph));

		// configuration
		if (Application.Current.Resources["EvergreenWebView2CreationProperties"] is CoreWebView2CreationProperties properties)
		{
			var UserData = Directory.CreateDirectory(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Xylia"));
			properties.UserDataFolder = UserData.FullName;
		}
	}
}
