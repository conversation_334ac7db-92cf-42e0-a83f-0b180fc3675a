# 公告系统测试指南

## 1. 数据库准备

### 1.1 创建公告表
执行以下SQL脚本创建公告相关表：

```bash
mysql -u username -p database_name < server/tools.bnszs.com_tp6/bns_announcement_system.sql
```

### 1.2 验证表创建
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'bns_announcement%';

-- 查看表结构
DESCRIBE bns_announcement;
DESCRIBE bns_announcement_version;

-- 查看示例数据
SELECT id, title, type, status, priority, created_at FROM bns_announcement;
SELECT * FROM bns_announcement_version;
```

## 2. Go服务端测试

### 2.1 启动服务
```bash
cd server
go run cmd/main.go
```

### 2.2 检查服务日志
确认以下日志信息：
- 数据库连接成功
- Redis缓存连接成功
- 公告表迁移成功
- UDP服务器启动成功

## 3. PHP管理后台测试

### 3.1 访问管理后台
打开浏览器访问：`http://your-domain/manage/admin/announcement`

### 3.2 测试功能
1. **查看公告列表**
   - 检查统计信息显示
   - 验证搜索过滤功能
   - 测试分页功能

2. **添加公告**
   - 访问：`/manage/admin/announcement/add`
   - 填写公告信息
   - 测试预览功能
   - 保存公告

3. **编辑公告**
   - 点击编辑按钮
   - 修改公告内容
   - 保存更改

4. **公告操作**
   - 发布/下线公告
   - 删除公告
   - 批量操作

## 4. API接口测试

### 4.1 PHP API测试（HTTP）
```bash
# 获取公告列表
curl -X GET "http://your-domain/api/announcement/list?client_type=desktop"

# 检查更新
curl -X GET "http://your-domain/api/announcement/checkUpdate?cache_version=1"

# 获取公告详情
curl -X GET "http://your-domain/api/announcement/detail?id=1"

# 获取统计信息
curl -X GET "http://your-domain/api/announcement/stats?client_type=desktop"

# 健康检查
curl -X GET "http://your-domain/api/announcement/health"
```

### 4.2 Go UDP API测试
使用UDP客户端测试工具发送二进制消息：

```
消息类型：
- 0x0A: 获取公告列表
- 0x0B: 检查公告更新  
- 0x0C: 获取公告详情
- 0x0D: 获取公告统计
```

## 5. 缓存机制测试

### 5.1 Redis缓存验证
```bash
# 连接Redis
redis-cli

# 查看公告相关缓存
KEYS announcement:*
KEYS announcements:*

# 查看缓存内容
GET announcement:version
GET "announcements:active:desktop:"
```

### 5.2 缓存更新测试
1. 获取公告列表（应该缓存数据）
2. 在管理后台修改公告
3. 再次获取公告列表（应该返回新数据）
4. 检查版本号是否更新

## 6. 客户端缓存测试

### 6.1 HTTP缓存头测试
```bash
# 首次请求
curl -v "http://your-domain/api/announcement/list"

# 带If-Modified-Since头的请求
curl -v -H "If-Modified-Since: Wed, 21 Oct 2015 07:28:00 GMT" \
     "http://your-domain/api/announcement/list"
```

### 6.2 版本号缓存测试
```bash
# 检查当前版本
curl "http://your-domain/api/announcement/checkUpdate?cache_version=0"

# 使用当前版本号请求
curl "http://your-domain/api/announcement/list?cache_version=1"
```

## 7. 性能测试

### 7.1 并发测试
```bash
# 使用ab工具进行并发测试
ab -n 1000 -c 10 "http://your-domain/api/announcement/list"
```

### 7.2 缓存性能测试
1. 清空缓存
2. 第一次请求（从数据库查询）
3. 后续请求（从缓存获取）
4. 比较响应时间

## 8. 错误处理测试

### 8.1 数据库错误
1. 停止数据库服务
2. 发送API请求
3. 检查错误响应

### 8.2 缓存错误
1. 停止Redis服务
2. 发送API请求
3. 检查是否降级到数据库查询

### 8.3 无效请求测试
```bash
# 无效的公告ID
curl "http://your-domain/api/announcement/detail?id=99999"

# 无效的客户端类型
curl "http://your-domain/api/announcement/list?client_type=invalid"
```

## 9. 日志检查

### 9.1 Go服务日志
检查 `server/logs/server.log` 文件：
- 公告请求处理日志
- 缓存操作日志
- 错误日志

### 9.2 PHP日志
检查PHP错误日志：
- 管理后台操作日志
- API请求日志
- 数据库操作日志

## 10. 预期结果

### 10.1 成功指标
- [ ] 数据库表创建成功
- [ ] Go服务启动无错误
- [ ] 管理后台功能正常
- [ ] API接口返回正确数据
- [ ] 缓存机制工作正常
- [ ] 版本控制功能正常
- [ ] 错误处理正确

### 10.2 性能指标
- API响应时间 < 100ms（缓存命中）
- API响应时间 < 500ms（数据库查询）
- 并发处理能力 > 100 req/s
- 缓存命中率 > 90%

## 11. 故障排除

### 11.1 常见问题
1. **表不存在错误**
   - 检查SQL脚本是否执行成功
   - 验证数据库连接配置

2. **缓存连接失败**
   - 检查Redis服务状态
   - 验证Redis配置

3. **API返回空数据**
   - 检查公告状态和时间范围
   - 验证客户端类型过滤

4. **版本号不更新**
   - 检查数据库触发器
   - 验证缓存清理逻辑

### 11.2 调试命令
```bash
# 检查Go服务状态
ps aux | grep main

# 检查端口占用
netstat -tulpn | grep :8080

# 检查数据库连接
mysql -u username -p -e "SELECT 1"

# 检查Redis连接
redis-cli ping
```
