{layout name="manage/template" /}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">群组管理 - {$config.name}</h5>
                    <a href="/manage/admin/update-config" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> 返回配置列表
                    </a>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>应用信息：</strong>
                        {$config.name} - 版本 {$config.version}
                    </div>
                    
                    {if isset($groups) && count($groups) > 0}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>群组ID</th>
                                    <th>状态</th>
                                    <th>描述</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="groups" id="group"}
                                <tr>
                                    <td>{$group.id}</td>
                                    <td>
                                        <code>{$group.group_id}</code>
                                    </td>
                                    <td>
                                        {if $group.is_active}
                                        <span class="badge bg-success">启用</span>
                                        {else/}
                                        <span class="badge bg-danger">禁用</span>
                                        {/if}
                                    </td>
                                    <td>{$group.description|default='无描述'}</td>
                                    <td>{$group.created_at}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="toggleGroup({$group.id})" title="切换状态">
                                                <i class="fas fa-toggle-on"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteGroup({$group.id})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                    {else/}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无群组数据</h5>
                        <p class="text-muted">当前应用还没有配置任何白名单群组</p>
                    </div>
                    {/if}
                    
                    <!-- 添加群组表单 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">添加新群组</h6>
                        </div>
                        <div class="card-body">
                            <form id="addGroupForm">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="group_id" class="form-label">群组ID <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="group_id" name="group_id" 
                                                   required placeholder="输入群组ID">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="description" class="form-label">描述</label>
                                            <input type="text" class="form-control" id="description" name="description" 
                                                   placeholder="群组描述（可选）">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary d-block w-100">
                                                <i class="fas fa-plus"></i> 添加
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- 批量导入 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">批量导入群组</h6>
                        </div>
                        <div class="card-body">
                            <form id="batchImportForm">
                                <div class="mb-3">
                                    <label for="batch_groups" class="form-label">群组ID列表</label>
                                    <textarea class="form-control" id="batch_groups" name="batch_groups" rows="4" 
                                              placeholder="每行一个群组ID，或用逗号分隔"></textarea>
                                    <div class="form-text">支持每行一个群组ID，或用逗号分隔多个群组ID</div>
                                </div>
                                <div class="mb-3">
                                    <label for="batch_description" class="form-label">统一描述</label>
                                    <input type="text" class="form-control" id="batch_description" name="batch_description" 
                                           placeholder="为所有导入的群组设置统一描述（可选）">
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-upload"></i> 批量导入
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 切换群组状态
function toggleGroup(id) {
    $.ajax({
        url: '/manage/admin/update-config/toggle-group',
        method: 'POST',
        data: { id: id },
        success: function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，请重试', {icon: 2});
        }
    });
}

// 删除群组
function deleteGroup(id) {
    layer.confirm('确定要删除这个群组吗？', {
        icon: 3,
        title: '确认删除'
    }, function(index) {
        $.ajax({
            url: '/manage/admin/update-config/delete-group',
            method: 'POST',
            data: { id: id },
            success: function(res) {
                if (res.code === 1) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 添加单个群组
document.getElementById('addGroupForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('app_name', '{$appName}');
    
    const groupId = formData.get('group_id');
    if (!groupId.trim()) {
        layer.msg('请输入群组ID', {icon: 2});
        return;
    }
    
    $.ajax({
        url: '/manage/admin/update-config/add-group',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，请重试', {icon: 2});
        }
    });
});

// 批量导入群组
document.getElementById('batchImportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('app_name', '{$appName}');
    
    const batchGroups = formData.get('batch_groups');
    if (!batchGroups.trim()) {
        layer.msg('请输入要导入的群组ID', {icon: 2});
        return;
    }
    
    $.ajax({
        url: '/manage/admin/update-config/batch-import-groups',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，请重试', {icon: 2});
        }
    });
});
</script>
