package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
)

// AnnouncementService 公告服务
type AnnouncementService struct {
	db    *gorm.DB
	cache cache.Cache
}

// NewAnnouncementService 创建公告服务实例
func NewAnnouncementService(db *gorm.DB, cache cache.Cache) *AnnouncementService {
	service := &AnnouncementService{
		db:    db,
		cache: cache,
	}

	// 注意：Redis订阅监听由AnnouncementPushService负责
	// 这里不再重复启动订阅服务

	return service
}

// 注意：Redis订阅功能已移至AnnouncementPushService
// 此方法已废弃，保留注释作为参考

// GetActiveAnnouncements 获取有效的公告列表
func (s *AnnouncementService) GetActiveAnnouncements(clientType, clientVersion string) ([]model.Announcement, error) {
	// 构建缓存键
	cacheKey := fmt.Sprintf("announcements:active:%s:%s", clientType, clientVersion)
	
	// 尝试从缓存获取
	var cachedAnnouncements []model.Announcement
	if err := s.cache.Get(cacheKey, &cachedAnnouncements); err == nil {
		logger.Debug("从缓存获取公告列表，数量: %d", len(cachedAnnouncements))
		return cachedAnnouncements, nil
	}
	
	// 从数据库查询
	var announcements []model.Announcement
	query := s.db.Where("status = ?", 1) // 已发布状态
	
	// 时间范围过滤
	now := time.Now()
	query = query.Where("(start_time IS NULL OR start_time <= ?)", now)
	query = query.Where("(end_time IS NULL OR end_time >= ?)", now)
	
	// 客户端类型过滤
	if clientType != "all" {
		query = query.Where("target_client IN ('all', ?)", clientType)
	}
	
	// 版本要求过滤（简单实现，实际可能需要更复杂的版本比较）
	if clientVersion != "" {
		query = query.Where("(version_requirement IS NULL OR version_requirement = '' OR version_requirement <= ?)", clientVersion)
	}
	
	err := query.Order("priority DESC, created_at DESC").Find(&announcements).Error
	if err != nil {
		logger.Error("查询公告失败: %v", err)
		return nil, err
	}
	
	// 缓存结果（5分钟）
	if data, err := json.Marshal(announcements); err == nil {
		s.cache.Set(cacheKey, string(data), 5*time.Minute)
	}
	
	logger.Info("从数据库获取公告列表，数量: %d", len(announcements))
	return announcements, nil
}

// GetCurrentVersion 获取当前公告版本号
func (s *AnnouncementService) GetCurrentVersion() (int64, error) {
	// 尝试从缓存获取
	var cachedVersion int64
	if err := s.cache.Get("announcement:version", &cachedVersion); err == nil {
		return cachedVersion, nil
	}
	
	// 从数据库获取
	var versionRecord model.AnnouncementVersion
	err := s.db.Where("id = ?", 1).First(&versionRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建初始版本记录
			versionRecord = model.AnnouncementVersion{
				ID:           1,
				Version:      1,
				LastModified: time.Now(),
			}
			if err := s.db.Create(&versionRecord).Error; err != nil {
				logger.Error("创建版本记录失败: %v", err)
				return 1, err
			}
		} else {
			logger.Error("查询版本记录失败: %v", err)
			return 1, err
		}
	}
	
	// 缓存版本号（1分钟）
	s.cache.Set("announcement:version", strconv.FormatInt(versionRecord.Version, 10), time.Minute)
	
	return versionRecord.Version, nil
}

// GetLastModified 获取最后修改时间
func (s *AnnouncementService) GetLastModified() (time.Time, error) {
	var versionRecord model.AnnouncementVersion
	err := s.db.Where("id = ?", 1).First(&versionRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return time.Now(), nil
		}
		return time.Time{}, err
	}
	
	return versionRecord.LastModified, nil
}

// HasUpdate 检查是否有更新
func (s *AnnouncementService) HasUpdate(clientVersion int64) (bool, error) {
	currentVersion, err := s.GetCurrentVersion()
	if err != nil {
		return false, err
	}
	
	return currentVersion > clientVersion, nil
}

// GetAnnouncementByID 根据ID获取公告
func (s *AnnouncementService) GetAnnouncementByID(id uint64) (*model.Announcement, error) {
	var announcement model.Announcement
	err := s.db.Where("id = ? AND status = ?", id, 1).First(&announcement).Error
	if err != nil {
		return nil, err
	}
	
	// 检查是否有效
	if !announcement.IsActive() {
		return nil, fmt.Errorf("公告已失效")
	}
	
	return &announcement, nil
}

// IncrementViewCount 增加查看次数
func (s *AnnouncementService) IncrementViewCount(id uint64) error {
	return s.db.Model(&model.Announcement{}).Where("id = ?", id).UpdateColumn("view_count", gorm.Expr("view_count + 1")).Error
}

// GetStats 获取公告统计信息
func (s *AnnouncementService) GetStats(clientType string) (*model.AnnouncementStats, error) {
	announcements, err := s.GetActiveAnnouncements(clientType, "")
	if err != nil {
		return nil, err
	}
	
	stats := &model.AnnouncementStats{
		Total: len(announcements),
	}
	
	for _, announcement := range announcements {
		switch announcement.Type {
		case 1:
			stats.Normal++
		case 2:
			stats.Important++
		case 3:
			stats.Urgent++
		}
	}
	
	return stats, nil
}

// ClearCache 清除公告相关缓存
func (s *AnnouncementService) ClearCache() error {
	// 清除版本缓存
	s.cache.Delete("announcement:version")
	
	// 清除公告列表缓存（使用模式匹配）
	// 注意：这里需要根据具体的缓存实现来清除匹配的键
	// 简单实现：清除常见的缓存键
	clientTypes := []string{"all", "desktop", "mobile"}
	for _, clientType := range clientTypes {
		for _, version := range []string{"", "1.0.0", "2.0.0"} {
			cacheKey := fmt.Sprintf("announcements:active:%s:%s", clientType, version)
			s.cache.Delete(cacheKey)
		}
	}
	
	logger.Info("已清除公告缓存")
	return nil
}

// UpdateVersion 更新版本号（当公告有变更时调用）
func (s *AnnouncementService) UpdateVersion() error {
	// 更新数据库中的版本号
	err := s.db.Model(&model.AnnouncementVersion{}).Where("id = ?", 1).Updates(map[string]interface{}{
		"version":       gorm.Expr("version + 1"),
		"last_modified": time.Now(),
	}).Error
	
	if err != nil {
		logger.Error("更新版本号失败: %v", err)
		return err
	}
	
	// 清除缓存
	s.ClearCache()
	
	logger.Info("公告版本号已更新")
	return nil
}

// CreateAnnouncement 创建公告（管理后台使用）
func (s *AnnouncementService) CreateAnnouncement(announcement *model.Announcement) error {
	err := s.db.Create(announcement).Error
	if err != nil {
		return err
	}
	
	// 如果是发布状态，更新版本号
	if announcement.Status == 1 {
		s.UpdateVersion()
	}
	
	return nil
}

// UpdateAnnouncement 更新公告（管理后台使用）
func (s *AnnouncementService) UpdateAnnouncement(announcement *model.Announcement) error {
	err := s.db.Save(announcement).Error
	if err != nil {
		return err
	}
	
	// 更新版本号
	s.UpdateVersion()
	
	return nil
}

// DeleteAnnouncement 删除公告（管理后台使用）
func (s *AnnouncementService) DeleteAnnouncement(id uint64) error {
	err := s.db.Delete(&model.Announcement{}, id).Error
	if err != nil {
		return err
	}

	// 更新版本号
	s.UpdateVersion()

	return nil
}

// GetAnnouncementList 获取公告列表响应
func (s *AnnouncementService) GetAnnouncementList(req *model.AnnouncementRequest) (*model.AnnouncementListResponse, error) {
	// 获取当前版本和最后修改时间
	currentVersion, err := s.GetCurrentVersion()
	if err != nil {
		return nil, err
	}

	lastModified, err := s.GetLastModified()
	if err != nil {
		return nil, err
	}

	// 检查客户端缓存是否有效
	if req.CacheVersion > 0 && req.CacheVersion >= currentVersion {
		// 返回空响应，表示无需更新
		return &model.AnnouncementListResponse{
			Version:       currentVersion,
			LastModified:  lastModified.Format(time.RFC3339),
			Announcements: []model.Announcement{},
			Count:         0,
		}, nil
	}

	// 获取有效公告
	announcements, err := s.GetActiveAnnouncements(req.ClientType, req.ClientVersion)
	if err != nil {
		return nil, err
	}

	// 异步增加查看次数
	go func() {
		for _, announcement := range announcements {
			s.IncrementViewCount(announcement.ID)
		}
	}()

	return &model.AnnouncementListResponse{
		Version:       currentVersion,
		LastModified:  lastModified.Format(time.RFC3339),
		Announcements: announcements,
		Count:         len(announcements),
	}, nil
}
