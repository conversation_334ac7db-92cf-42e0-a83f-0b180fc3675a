package service

import (
	"fmt"
	"time"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// GatewayInfoService 游戏入口信息服务
// 提供客户端所需的游戏入口信息，包括：
// - 游戏可执行文件路径
// - 下载链接
// - 版本检查
// - 强制更新标志
type GatewayInfoService struct {
	db    *gorm.DB
	cache cache.Cache
}

// NewGatewayInfoService 创建游戏入口信息服务
func NewGatewayInfoService(db *gorm.DB, cache cache.Cache) *GatewayInfoService {
	return &GatewayInfoService{
		db:    db,
		cache: cache,
	}
}

// GatewayInfoRequest 入口信息请求
type GatewayInfoRequest struct {
	ClientVersion string `json:"client_version"`
	AppType       uint8  `json:"app_type"`
}

// GatewayInfoResponse 入口信息响应
type GatewayInfoResponse struct {
	ExecutablePath string `json:"ExecutablePath"`          // 游戏可执行文件路径
	DownloadURL    string `json:"DownloadURL"`             // 下载链接
	Checksum       string `json:"Checksum,omitempty"`      // 文件校验和（SHA256）
	ErrorCode      uint32 `json:"error_code,omitempty"`    // 错误码
	ErrorMessage   string `json:"error_message,omitempty"` // 错误信息
}

// GetGatewayInfo 获取游戏入口信息
func (s *GatewayInfoService) GetGatewayInfo(req *GatewayInfoRequest) (*GatewayInfoResponse, error) {
	logger.Debug("获取游戏入口信息: ClientVersion=%s, AppType=%d", req.ClientVersion, req.AppType)

	// 从缓存获取配置
	cacheKey := fmt.Sprintf("gateway_info:app_%d", req.AppType)
	var cachedResponse GatewayInfoResponse
	if err := s.cache.Get(cacheKey, &cachedResponse); err == nil {
		logger.Debug("从缓存获取入口信息: AppType=%d", req.AppType)
		return &cachedResponse, nil
	}

	// 从数据库获取配置
	var config model.UpdateConfig
	err := s.db.Where("app_type = ?", req.AppType).First(&config).Error
	if err == gorm.ErrRecordNotFound {
		logger.Warn("未找到应用配置: AppType=%d", req.AppType)
		return &GatewayInfoResponse{
			ErrorCode:    404,
			ErrorMessage: "应用配置不存在",
		}, nil
	}
	if err != nil {
		logger.Error("查询应用配置失败: AppType=%d, Error=%v", req.AppType, err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}

	// 检查是否需要更新
	needsUpdate := s.checkVersionUpdate(req.ClientVersion, config.Version)
	
	response := &GatewayInfoResponse{}
	
	if needsUpdate {
		// 需要更新，返回下载信息
		response.ExecutablePath = config.ExecutablePath
		response.DownloadURL = config.DownloadURL
		response.Checksum = config.Checksum
		logger.Info("客户端需要更新: ClientVersion=%s, ServerVersion=%s", req.ClientVersion, config.Version)
	} else {
		// 不需要更新，返回游戏路径
		response.ExecutablePath = config.ExecutablePath
		logger.Debug("客户端版本最新: ClientVersion=%s", req.ClientVersion)
	}

	// 缓存响应（5分钟）
	if err := s.cache.Set(cacheKey, response, 5*time.Minute); err != nil {
		logger.Warn("缓存入口信息失败: %v", err)
	}

	// 记录请求统计
	s.recordRequestStats(req.AppType)

	return response, nil
}

// checkVersionUpdate 检查版本是否需要更新
func (s *GatewayInfoService) checkVersionUpdate(clientVersion, serverVersion string) bool {
	if clientVersion == "" || serverVersion == "" {
		return false
	}
	
	// 简单的版本比较，可以根据需要实现更复杂的版本比较逻辑
	return clientVersion != serverVersion
}

// GetForceUpdateFlag 获取强制更新标志
func (s *GatewayInfoService) GetForceUpdateFlag(appType uint8) bool {
	var config model.UpdateConfig
	err := s.db.Where("app_type = ?", appType).First(&config).Error
	if err != nil {
		logger.Warn("获取强制更新标志失败: AppType=%d, Error=%v", appType, err)
		return false
	}
	
	return config.ForceUpdate
}

// UpdateConfig 更新应用配置
func (s *GatewayInfoService) UpdateConfig(appType uint8, executablePath, downloadURL, version, checksum string, forceUpdate bool) error {
	config := &model.UpdateConfig{
		AppType:        appType,
		ExecutablePath: executablePath,
		DownloadURL:    downloadURL,
		Version:        version,
		Checksum:       checksum,
		ForceUpdate:    forceUpdate,
		UpdatedAt:      time.Now(),
	}

	// 使用 UPSERT 操作
	err := s.db.Save(config).Error
	if err != nil {
		logger.Error("更新应用配置失败: AppType=%d, Error=%v", appType, err)
		return err
	}

	// 清除相关缓存
	cacheKey := fmt.Sprintf("gateway_info:app_%d", appType)
	if err := s.cache.Delete(cacheKey); err != nil {
		logger.Warn("清除配置缓存失败: %v", err)
	}

	logger.Info("应用配置更新成功: AppType=%d, Version=%s, ForceUpdate=%v", appType, version, forceUpdate)
	return nil
}

// GetAllConfigs 获取所有应用配置
func (s *GatewayInfoService) GetAllConfigs() ([]*model.UpdateConfig, error) {
	var configs []*model.UpdateConfig
	err := s.db.Find(&configs).Error
	if err != nil {
		logger.Error("获取所有应用配置失败: Error=%v", err)
		return nil, err
	}
	return configs, nil
}

// recordRequestStats 记录请求统计
func (s *GatewayInfoService) recordRequestStats(appType uint8) {
	today := time.Now().Format("2006-01-02")
	statsKey := fmt.Sprintf("gateway_info_stats:%s:app_%d", today, appType)
	
	// 增加请求计数
	if _, err := s.cache.Increment(statsKey, 1); err != nil {
		logger.Warn("记录入口信息请求统计失败: %v", err)
	} else {
		// 设置过期时间为7天
		if err := s.cache.Expire(statsKey, 7*24*time.Hour); err != nil {
			logger.Warn("设置统计数据过期时间失败: %v", err)
		}
	}
}

// GetRequestStats 获取请求统计
func (s *GatewayInfoService) GetRequestStats(date string, appType uint8) (int64, error) {
	statsKey := fmt.Sprintf("gateway_info_stats:%s:app_%d", date, appType)
	
	var count int64
	if err := s.cache.Get(statsKey, &count); err != nil {
		return 0, nil // 没有统计数据返回0
	}
	
	return count, nil
}

// ClearCache 清除所有缓存
func (s *GatewayInfoService) ClearCache() error {
	// 这里可以实现更精确的缓存清理逻辑
	// 目前简单地记录日志
	logger.Info("清除游戏入口信息缓存")
	return nil
}

// HealthCheck 健康检查
func (s *GatewayInfoService) HealthCheck() error {
	// 检查数据库连接
	sqlDB, err := s.db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %v", err)
	}
	
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接检查失败: %v", err)
	}
	
	// 检查缓存连接
	if err := s.cache.HealthCheck(); err != nil {
		return fmt.Errorf("缓存连接检查失败: %v", err)
	}
	
	return nil
}
