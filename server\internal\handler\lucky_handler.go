package handler

import (
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// LuckyHandler 签到抽奖处理器
type LuckyHandler struct {
	luckyService      *service.LuckyService
	authService       *service.AuthService
	permissionService *service.PermissionService
}

// NewLuckyHandler 创建签到抽奖处理器
func NewLuckyHandler(luckyService *service.LuckyService, authService *service.AuthService, permissionService *service.PermissionService) *LuckyHandler {
	return &LuckyHandler{
		luckyService:      luckyService,
		authService:       authService,
		permissionService: permissionService,
	}
}

// HandleLuckyDrawDirect 直接处理签到抽奖请求
func (h *LuckyHandler) HandleLuckyDrawDirect(token string, clientIP string, deviceFingerprint string) ([]byte, error) {
	// 验证Token并获取用户信息
	user, err := h.authService.ValidateToken(token)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLuckyDrawResponse(false, binary.ErrorCodeUnauthorized, "Token验证失败", "", 0)
	}

	// 使用设备指纹
	finalDeviceFingerprint := deviceFingerprint
	if finalDeviceFingerprint == "" {
		// 如果没有提供设备指纹，尝试从用户的最新设备历史记录中获取
		latestFingerprint, err := h.authService.GetLatestDeviceFingerprint(int64(user.Uin))
		if err == nil && latestFingerprint != "" {
			finalDeviceFingerprint = latestFingerprint
		}
	}

	// 创建抽奖请求
	drawReq := &service.DrawRequest{
		UID:               user.UID,
		IP:                clientIP,
		DeviceFingerprint: finalDeviceFingerprint,
	}

	// 执行抽奖
	response, err := h.luckyService.Draw(drawReq)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLuckyDrawResponse(false, binary.ErrorCodeServerError, err.Error(), "", 0)
	}

	// 检查签到是否成功，如果成功则刷新权限信息
	networkHandler := binary.NewNetworkHandler()
	if response.Code == 1 { // 签到成功
		// 签到成功后，强制刷新权限信息（因为可能获得了新的权限奖励）
		// 先清除权限缓存，确保获取最新的权限状态
		if err := h.permissionService.ClearUserPermissionCache(user.UID, "client"); err != nil {
			logger.Warn("清除用户权限缓存失败: UID=%d, Error=%v", user.UID, err)
		}

		// 重新计算权限并返回
		finalPermission := h.authService.GetFinalPermission(user.Permission, int64(user.Uin))
		permissionExpiration, err := h.luckyService.GetUserPermissionExpiration(user.UID, "client")
		if err != nil {
			logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
			permissionExpiration = 0 // 无权限
		}

		logger.Debug("签到成功，返回刷新后的权限信息: UID=%d, Permission=%d, Expiration=%d",
			user.UID, finalPermission, permissionExpiration)

		// 返回包含权限信息的响应
		return networkHandler.CreateLuckyDrawResponseWithPermission(true, 0, "", response.Message, uint32(response.Count), finalPermission, permissionExpiration)
	}

	// 创建普通成功响应（不包含权限信息）
	return networkHandler.CreateLuckyDrawResponse(true, 0, "", response.Message, uint32(response.Count))
}

// HandleLuckyStatusDirect 直接处理获取签到状态请求
func (h *LuckyHandler) HandleLuckyStatusDirect(token string) ([]byte, error) {
	// 验证Token并获取用户信息
	user, err := h.authService.ValidateToken(token)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLuckyStatusResponse(false, binary.ErrorCodeUnauthorized, "Token验证失败", 0, 0)
	}

	// 获取用户签到状态
	status, err := h.luckyService.GetUserStatus(user.UID)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLuckyStatusResponse(false, binary.ErrorCodeServerError, err.Error(), 0, 0)
	}

	// 创建成功响应（使用UserStatusResponse中的字段）
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateLuckyStatusResponse(
		true, 0, "",
		uint32(status.Point),  // 签到总天数
		status.AvailableCount, // 今日可用次数
	)
}

// HandleCDKeyActivate 处理CDKEY激活请求
func (h *LuckyHandler) HandleCDKeyActivate(token string, cdkey string, isVerified bool, clientIP string) ([]byte, error) {
	// 验证Token并获取用户信息
	user, err := h.authService.ValidateToken(token)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateCDKeyActivateResponse(false, binary.ErrorCodeUnauthorized, "Token验证失败", nil)
	}

	// 调用LuckyService的CDKEY激活功能
	result, err := h.luckyService.ActivateCDKey(cdkey, user.UID, false, isVerified)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateCDKeyActivateResponse(false, binary.ErrorCodeServerError, err.Error(), nil)
	}

	// 创建响应
	networkHandler := binary.NewNetworkHandler()
	if result.RequireGroupCheck {
		// 第一阶段：返回需要验证的群号
		return networkHandler.CreateCDKeyActivateResponse(true, 0, "", result.Group)
	} else {
		// 第二阶段：激活成功，返回更新后的权限信息
		// 重新计算用户的最终权限
		finalPermission := h.authService.GetFinalPermission(user.Permission, int64(user.Uin))

		// 通过LuckyService获取权限过期时间
		permissionExpiration, err := h.luckyService.GetUserPermissionExpiration(user.UID, "client")
		if err != nil {
			logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
			permissionExpiration = 0 // 无权限
		}

		return networkHandler.CreateCDKeyActivateSuccessResponse(finalPermission, permissionExpiration)
	}
}
