package handler

import (
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/utils"
)

// GatewayInfoHandler 游戏入口信息处理器
type GatewayInfoHandler struct {
	gatewayInfoService *service.GatewayInfoService
	responseHelper     *utils.ResponseHelper
}

// NewGatewayInfoHandler 创建游戏入口信息处理器
func NewGatewayInfoHandler(gatewayInfoService *service.GatewayInfoService) *GatewayInfoHandler {
	return &GatewayInfoHandler{
		gatewayInfoService: gatewayInfoService,
		responseHelper:     utils.NewResponseHelper(),
	}
}

// HandleGatewayInfoRequest 处理游戏入口信息请求
func (h *GatewayInfoHandler) HandleGatewayInfoRequest(clientVersion string, appType uint8, clientIP string) ([]byte, error) {
	logger.Debug("处理游戏入口信息请求: ClientVersion=%s, AppType=%d, ClientIP=%s", clientVersion, appType, clientIP)

	// 创建请求对象
	req := &service.GatewayInfoRequest{
		ClientVersion: clientVersion,
		AppType:       appType,
	}

	// 获取入口信息
	response, err := h.gatewayInfoService.GetGatewayInfo(req)
	if err != nil {
		logger.Error("获取游戏入口信息失败: %v", err)
		return h.responseHelper.CreateServerError("获取入口信息失败")
	}

	// 如果响应包含错误
	if response.ErrorCode != 0 {
		logger.Warn("游戏入口信息请求失败: ErrorCode=%d, ErrorMessage=%s", response.ErrorCode, response.ErrorMessage)
		return h.createGatewayInfoErrorResponse(response.ErrorCode, response.ErrorMessage)
	}

	// 创建成功响应
	return h.createGatewayInfoResponse(response)
}

// createGatewayInfoResponse 创建游戏入口信息响应
func (h *GatewayInfoHandler) createGatewayInfoResponse(response *service.GatewayInfoResponse) ([]byte, error) {
	networkHandler := binary.NewNetworkHandler()
	
	// 创建响应数据
	responseData := map[string]interface{}{
		"ExecutablePath": response.ExecutablePath,
		"DownloadURL":    response.DownloadURL,
	}
	
	// 如果有校验和，添加到响应中
	if response.Checksum != "" {
		responseData["Checksum"] = response.Checksum
	}

	return networkHandler.CreateGatewayInfoResponse(responseData)
}

// createGatewayInfoErrorResponse 创建游戏入口信息错误响应
func (h *GatewayInfoHandler) createGatewayInfoErrorResponse(errorCode uint32, errorMessage string) ([]byte, error) {
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateErrorResponse(errorCode, errorMessage)
}

// HandleForceUpdateCheck 处理强制更新检查
func (h *GatewayInfoHandler) HandleForceUpdateCheck(appType uint8) bool {
	return h.gatewayInfoService.GetForceUpdateFlag(appType)
}

// HandleConfigUpdate 处理配置更新（管理接口）
func (h *GatewayInfoHandler) HandleConfigUpdate(appType uint8, executablePath, downloadURL, version, checksum string, forceUpdate bool) error {
	logger.Info("更新应用配置: AppType=%d, Version=%s, ForceUpdate=%v", appType, version, forceUpdate)
	
	return h.gatewayInfoService.UpdateConfig(appType, executablePath, downloadURL, version, checksum, forceUpdate)
}

// HandleGetAllConfigs 处理获取所有配置（管理接口）
func (h *GatewayInfoHandler) HandleGetAllConfigs() (interface{}, error) {
	configs, err := h.gatewayInfoService.GetAllConfigs()
	if err != nil {
		logger.Error("获取所有配置失败: %v", err)
		return nil, err
	}
	
	return configs, nil
}

// HandleGetStats 处理获取统计信息（管理接口）
func (h *GatewayInfoHandler) HandleGetStats(date string, appType uint8) (interface{}, error) {
	stats, err := h.gatewayInfoService.GetRequestStats(date, appType)
	if err != nil {
		logger.Error("获取统计信息失败: Date=%s, AppType=%d, Error=%v", date, appType, err)
		return nil, err
	}
	
	return map[string]interface{}{
		"date":     date,
		"app_type": appType,
		"requests": stats,
	}, nil
}

// HandleClearCache 处理清除缓存（管理接口）
func (h *GatewayInfoHandler) HandleClearCache() error {
	logger.Info("清除游戏入口信息缓存")
	return h.gatewayInfoService.ClearCache()
}

// HandleHealthCheck 处理健康检查
func (h *GatewayInfoHandler) HandleHealthCheck() error {
	return h.gatewayInfoService.HealthCheck()
}
