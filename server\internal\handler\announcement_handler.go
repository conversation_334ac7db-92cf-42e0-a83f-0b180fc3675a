package handler

import (
	"encoding/json"

	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

// AnnouncementHandler 公告处理器
type AnnouncementHandler struct {
	announcementService *service.AnnouncementService
	authService         *service.AuthService
}

// NewAnnouncementHandler 创建公告处理器实例
func NewAnnouncementHandler(announcementService *service.AnnouncementService, authService *service.AuthService) *AnnouncementHandler {
	return &AnnouncementHandler{
		announcementService: announcementService,
		authService:         authService,
	}
}

// HandleAnnouncementList 处理获取公告列表请求
func (h *AnnouncementHandler) HandleAnnouncementList(data []byte, userID uint64) ([]byte, error) {
	logger.Debug("处理公告列表请求，用户ID: %d", userID)
	
	// 解析请求数据
	var req model.AnnouncementRequest
	if err := json.Unmarshal(data, &req); err != nil {
		logger.Error("解析公告请求失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_LIST", "请求格式错误")
	}
	
	// 设置默认值
	if req.ClientType == "" {
		req.ClientType = "desktop"
	}
	
	// 获取公告列表
	response, err := h.announcementService.GetAnnouncementList(&req)
	if err != nil {
		logger.Error("获取公告列表失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_LIST", "获取公告失败")
	}
	
	// 构建响应
	responseData := map[string]interface{}{
		"type":    "ANNOUNCEMENT_LIST",
		"success": true,
		"data":    response,
	}
	
	responseBytes, err := json.Marshal(responseData)
	if err != nil {
		logger.Error("序列化公告响应失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_LIST", "响应序列化失败")
	}
	
	logger.Info("成功返回公告列表，用户ID: %d，公告数量: %d", userID, response.Count)
	return responseBytes, nil
}

// HandleAnnouncementCheck 处理检查公告更新请求
func (h *AnnouncementHandler) HandleAnnouncementCheck(data []byte, userID uint64) ([]byte, error) {
	logger.Debug("处理公告更新检查请求，用户ID: %d", userID)
	
	// 解析请求数据
	var req struct {
		CacheVersion int64 `json:"cache_version"`
	}
	if err := json.Unmarshal(data, &req); err != nil {
		logger.Error("解析公告检查请求失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_CHECK", "请求格式错误")
	}
	
	// 获取当前版本
	currentVersion, err := h.announcementService.GetCurrentVersion()
	if err != nil {
		logger.Error("获取当前版本失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_CHECK", "检查更新失败")
	}
	
	// 获取最后修改时间
	lastModified, err := h.announcementService.GetLastModified()
	if err != nil {
		logger.Error("获取最后修改时间失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_CHECK", "检查更新失败")
	}
	
	// 检查是否有更新
	hasUpdate := currentVersion > req.CacheVersion
	
	// 构建响应
	response := model.AnnouncementUpdateResponse{
		HasUpdate:      hasUpdate,
		CurrentVersion: currentVersion,
		LastModified:   lastModified.Format("2006-01-02 15:04:05"),
		CacheVersion:   req.CacheVersion,
	}
	
	responseData := map[string]interface{}{
		"type":    "ANNOUNCEMENT_CHECK",
		"success": true,
		"data":    response,
	}
	
	responseBytes, err := json.Marshal(responseData)
	if err != nil {
		logger.Error("序列化公告检查响应失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_CHECK", "响应序列化失败")
	}
	
	logger.Info("公告更新检查完成，用户ID: %d，有更新: %v", userID, hasUpdate)
	return responseBytes, nil
}

// HandleAnnouncementDetail 处理获取公告详情请求
func (h *AnnouncementHandler) HandleAnnouncementDetail(data []byte, userID uint64) ([]byte, error) {
	logger.Debug("处理公告详情请求，用户ID: %d", userID)
	
	// 解析请求数据
	var req struct {
		ID uint64 `json:"id"`
	}
	if err := json.Unmarshal(data, &req); err != nil {
		logger.Error("解析公告详情请求失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_DETAIL", "请求格式错误")
	}
	
	if req.ID == 0 {
		return h.createErrorResponse("ANNOUNCEMENT_DETAIL", "公告ID不能为空")
	}
	
	// 获取公告详情
	announcement, err := h.announcementService.GetAnnouncementByID(req.ID)
	if err != nil {
		logger.Error("获取公告详情失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_DETAIL", "公告不存在或已失效")
	}
	
	// 增加查看次数
	go func() {
		h.announcementService.IncrementViewCount(req.ID)
	}()
	
	// 构建响应
	responseData := map[string]interface{}{
		"type":    "ANNOUNCEMENT_DETAIL",
		"success": true,
		"data":    announcement,
	}
	
	responseBytes, err := json.Marshal(responseData)
	if err != nil {
		logger.Error("序列化公告详情响应失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_DETAIL", "响应序列化失败")
	}
	
	logger.Info("成功返回公告详情，用户ID: %d，公告ID: %d", userID, req.ID)
	return responseBytes, nil
}

// HandleAnnouncementStats 处理获取公告统计请求
func (h *AnnouncementHandler) HandleAnnouncementStats(data []byte, userID uint64) ([]byte, error) {
	logger.Debug("处理公告统计请求，用户ID: %d", userID)
	
	// 解析请求数据
	var req struct {
		ClientType string `json:"client_type"`
	}
	if err := json.Unmarshal(data, &req); err != nil {
		// 使用默认值
		req.ClientType = "desktop"
	}
	
	// 获取统计信息
	stats, err := h.announcementService.GetStats(req.ClientType)
	if err != nil {
		logger.Error("获取公告统计失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_STATS", "获取统计失败")
	}
	
	// 构建响应
	responseData := map[string]interface{}{
		"type":    "ANNOUNCEMENT_STATS",
		"success": true,
		"data":    stats,
	}
	
	responseBytes, err := json.Marshal(responseData)
	if err != nil {
		logger.Error("序列化公告统计响应失败: %v", err)
		return h.createErrorResponse("ANNOUNCEMENT_STATS", "响应序列化失败")
	}
	
	logger.Info("成功返回公告统计，用户ID: %d", userID)
	return responseBytes, nil
}

// createErrorResponse 创建错误响应
func (h *AnnouncementHandler) createErrorResponse(requestType, message string) ([]byte, error) {
	errorResponse := map[string]interface{}{
		"type":    requestType,
		"success": false,
		"error":   message,
	}
	
	return json.Marshal(errorResponse)
}

// PushAnnouncementUpdate 推送公告更新通知（当有新公告时）
func (h *AnnouncementHandler) PushAnnouncementUpdate() {
	logger.Info("开始推送公告更新通知")
	
	// 获取当前版本
	currentVersion, err := h.announcementService.GetCurrentVersion()
	if err != nil {
		logger.Error("获取当前版本失败: %v", err)
		return
	}
	
	// 构建推送消息
	pushData := map[string]interface{}{
		"type":    "ANNOUNCEMENT_UPDATE_PUSH",
		"version": currentVersion,
		"message": "有新的公告发布，请刷新查看",
	}
	
	_, err = json.Marshal(pushData)
	if err != nil {
		logger.Error("序列化推送消息失败: %v", err)
		return
	}
	
	// 获取所有在线用户并推送
	onlineUsers := h.authService.GetOnlineUsers()
	pushCount := 0
	
	for userID := range onlineUsers {
		// 这里需要实现向特定用户推送消息的功能
		// 由于UDP是无连接的，需要通过其他方式实现推送
		// 可以考虑在用户下次请求时返回更新通知
		logger.Debug("向用户 %d 推送公告更新通知", userID)
		pushCount++
	}
	
	logger.Info("公告更新通知推送完成，推送用户数: %d", pushCount)
}
