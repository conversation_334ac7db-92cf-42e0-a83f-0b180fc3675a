package binary

import (
	"encoding/binary"
	"fmt"
)

// UpdateConfigRequest 更新配置请求
type UpdateConfigRequest struct {
	Token   string `json:"token"`   // 用户Token（可选）
	AppType uint8  `json:"app_type"` // 应用类型（1字节）
	Version string `json:"version"` // 当前版本
}

// UpdateConfigResponse 更新配置响应
type UpdateConfigResponse struct {
	ExecutablePath string  `json:"ExecutablePath"`          // 可执行文件路径
	DownloadURL    string  `json:"DownloadURL"`             // 下载链接
	Checksum       string  `json:"Checksum,omitempty"`      // 文件校验和（SHA256）
	Groups         []int64 `json:"Groups,omitempty"`        // 白名单群组列表
	ErrorCode      uint32  `json:"error_code,omitempty"`    // 错误码
	ErrorMessage   string  `json:"error_message,omitempty"` // 错误信息
}

// DecodeUpdateConfigRequest 解码更新配置请求
func DecodeUpdateConfigRequest(data []byte) (*UpdateConfigRequest, error) {
	req := &UpdateConfigRequest{}
	offset := 0
	var err error

	// 读取AppType字段（1字节）
	if offset >= len(data) {
		return nil, fmt.Errorf("insufficient data for AppType")
	}
	req.AppType = data[offset]
	offset++

	// 读取Version字段（使用统一的ReadString方法）
	req.Version, offset, err = ReadString(data, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to read Version: %w", err)
	}

	return req, nil
}

// EncodeUpdateConfigResponse 编码更新配置响应
func EncodeUpdateConfigResponse(resp *UpdateConfigResponse) []byte {
	buf := make([]byte, 0, 1024) // 预分配缓冲区
	offset := 0

	// 错误码（4字节，小端序）
	errorCodeBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(errorCodeBytes, resp.ErrorCode)
	buf = append(buf, errorCodeBytes...)
	offset += 4

	// 如果有错误，只返回错误信息
	if resp.ErrorCode != 0 {
		if resp.ErrorMessage != "" {
			// 使用WriteString格式：4字节长度前缀 + 字符串内容
			msgBytes := make([]byte, 4+len(resp.ErrorMessage))
			binary.LittleEndian.PutUint32(msgBytes[0:4], uint32(len(resp.ErrorMessage)))
			copy(msgBytes[4:], resp.ErrorMessage)
			buf = append(buf, msgBytes...)
		}
		return buf
	}

	// 成功时的字段顺序：DownloadURL → ExecutablePath → Checksum → Groups
	// Groups字段总是发送，不管是否需要更新

	// 1. DownloadURL（只有需要更新时才发送）
	if resp.DownloadURL != "" {
		urlBytes := make([]byte, 4+len(resp.DownloadURL))
		binary.LittleEndian.PutUint32(urlBytes[0:4], uint32(len(resp.DownloadURL)))
		copy(urlBytes[4:], resp.DownloadURL)
		buf = append(buf, urlBytes...)
	} else {
		// 如果没有DownloadURL，发送空字符串
		emptyBytes := make([]byte, 4)
		binary.LittleEndian.PutUint32(emptyBytes[0:4], 0)
		buf = append(buf, emptyBytes...)
	}

	// 2. ExecutablePath（只有需要更新时才发送）
	if resp.ExecutablePath != "" {
		pathBytes := make([]byte, 4+len(resp.ExecutablePath))
		binary.LittleEndian.PutUint32(pathBytes[0:4], uint32(len(resp.ExecutablePath)))
		copy(pathBytes[4:], resp.ExecutablePath)
		buf = append(buf, pathBytes...)
	} else {
		// 如果没有ExecutablePath，发送空字符串
		emptyBytes := make([]byte, 4)
		binary.LittleEndian.PutUint32(emptyBytes[0:4], 0)
		buf = append(buf, emptyBytes...)
	}

	// 3. Checksum（只有需要更新时才发送）
	if resp.Checksum != "" {
		checksumBytes := make([]byte, 4+len(resp.Checksum))
		binary.LittleEndian.PutUint32(checksumBytes[0:4], uint32(len(resp.Checksum)))
		copy(checksumBytes[4:], resp.Checksum)
		buf = append(buf, checksumBytes...)
	} else {
		// 如果没有Checksum，发送空字符串
		emptyBytes := make([]byte, 4)
		binary.LittleEndian.PutUint32(emptyBytes[0:4], 0)
		buf = append(buf, emptyBytes...)
	}

	// 4. Groups（总是发送）
	// 先发送数组长度（4字节）
	groupCountBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(groupCountBytes, uint32(len(resp.Groups)))
	buf = append(buf, groupCountBytes...)

	// 然后发送每个群组ID（每个8字节，int64）
	for _, groupID := range resp.Groups {
		groupBytes := make([]byte, 8)
		binary.LittleEndian.PutUint64(groupBytes, uint64(groupID))
		buf = append(buf, groupBytes...)
	}

	return buf
}