package service

import (
	"udp-server/server/pkg/logger"
)

// UpdateInitService 更新配置初始化服务
type UpdateInitService struct {
	updateService *UpdateService
}

// NewUpdateInitService 创建更新配置初始化服务
func NewUpdateInitService(updateService *UpdateService) *UpdateInitService {
	return &UpdateInitService{
		updateService: updateService,
	}
}

// InitializeCache 初始化缓存
func (s *UpdateInitService) InitializeCache() error {
	logger.Info("Initializing update config cache...")
	
	// 预加载常用应用的配置到缓存
	apps := []string{"bns-helper", "bns-preview-tools"}
	
	for _, app := range apps {
		_, err := s.updateService.GetUpdateConfig(app, "")
		if err != nil {
			logger.Error("Failed to initialize cache for app %s: %v", app, err)
			// 继续处理其他应用，不因为单个应用失败而停止
			continue
		}
		logger.Info("Cache initialized for app: %s", app)
	}
	
	logger.Info("Update config cache initialization completed")
	return nil
}

// RefreshAllCache 刷新所有缓存
func (s *UpdateInitService) RefreshAllCache() error {
	logger.Info("Refreshing all update config cache...")
	
	err := s.updateService.RefreshAllCache()
	if err != nil {
		logger.Error("Failed to refresh all cache: %v", err)
		return err
	}
	
	logger.Info("All update config cache refreshed successfully")
	return nil
}
