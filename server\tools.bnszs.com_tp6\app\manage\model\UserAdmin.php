<?php
namespace app\manage\model;

use think\Exception;
use think\Model;
use think\facade\Db;
use think\facade\Session;

class UserAdmin extends Model
{
    protected $table = 'bns_useradmin';

    /**
     * 管理员登录
     */
    public static function Login($name, $password) {
        try {
            $admin = static::where("username", $name)->where("isAction", 1)->find();
            if($admin && crypt($password, 'bnszs') == $admin['password']) {
                return $admin;
            }
        } catch (\Exception $e) {
            // 数据库连接失败时的处理
        }

        throw new Exception("用户名或密码错误");
    }

    /**
     * 获取管理员菜单项
     */
    public static function GetItems() {
        // 只使用原生PHP session
        $admin = $_SESSION['admin'] ?? null;
        if(!$admin) {
            // 如果没有管理员session，返回空数组
            return [];
        }

        try {
            // 首先尝试简化的查询，直接获取所有启用的菜单项
            $menuItems = Db::query("SELECT * FROM `bns_useradminitem` WHERE isAction = 1 AND isNavigation = 1 ORDER BY sort DESC");

            if (!empty($menuItems)) {
                return $menuItems;
            }

            // 如果没有数据，尝试原始的权限查询
            $result = Db::query("SELECT * from `bns_useradminitem` WHERE isAction =1 and isNavigation = 1 and ID in (SELECT DISTINCT SUBSTRING_INDEX(SUBSTRING_INDEX(a.power,',',b.help_topic_id + 1),',',-1)
                    FROM
                    (SELECT GROUP_CONCAT(REPLACE(power,'/',',')) AS power FROM `bns_useradmin` b WHERE b.uid = ".$admin.") a
                    JOIN
                    mysql.help_topic b
                    ON b.help_topic_id < (LENGTH(a.power) - LENGTH(REPLACE(a.power,',','')) + 1)) order by sort desc");

            if (!empty($result)) {
                return $result;
            }

        } catch (\Exception $e) {
            // 记录错误日志
            error_log("获取管理员菜单失败: " . $e->getMessage());
        }

        // 如果数据库查询失败，返回默认菜单数据
        return [
            [
                'ID' => 2,
                'itemName' => '自定义列表',
                'url' => '/manage/admin/profiles',
                'icon' => 'am-icon-bars',
                'isAction' => 1,
                'isNavigation' => 1,
                'sort' => 100
            ],
            [
                'ID' => 5,
                'itemName' => 'CDkey生成',
                'url' => '/manage/admin/cdkey',
                'icon' => 'am-icon-key',
                'isAction' => 1,
                'isNavigation' => 1,
                'sort' => 90
            ],
            [
                'ID' => 6,
                'itemName' => '用户管理',
                'url' => '/manage/admin/users',
                'icon' => 'am-icon-users',
                'isAction' => 1,
                'isNavigation' => 1,
                'sort' => 80
            ],
            [
                'ID' => 14,
                'itemName' => '风控查看',
                'url' => '/manage/admin/risk',
                'icon' => 'am-icon-eye',
                'isAction' => 1,
                'isNavigation' => 1,
                'sort' => 70
            ],
            [
                'ID' => 15,
                'itemName' => '公告管理',
                'url' => '/manage/admin/announcement',
                'icon' => 'am-icon-bullhorn',
                'isAction' => 1,
                'isNavigation' => 1,
                'sort' => 75
            ],
            [
                'ID' => 16,
                'itemName' => '风控管理',
                'url' => '/manage/admin/risk/events',
                'icon' => 'am-icon-shield',
                'isAction' => 1,
                'isNavigation' => 1,
                'sort' => 60
            ],
            [
                'ID' => 17,
                'itemName' => '在线统计',
                'url' => '/manage/admin/online',
                'icon' => 'am-icon-line-chart',
                'isAction' => 1,
                'isNavigation' => 1,
                'sort' => 50
            ]
        ];
    }
}
