﻿using CommunityToolkit.Mvvm.ComponentModel;
using CUE4Parse.Utils;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Services.ApiEndpoints;
internal partial class BnsApiEndpoint(EPublisher publisher, string name, string lobbyGate, string infoGate, string? np = null, string? lime = null) : ObservableObject
{
	#region Properties
	public string Name => name;
	public EPublisher Publisher => publisher;
	public AddressEndPoint? NpStsApi => np;
	public AddressEndPoint? LobbyGate => lobbyGate;
	public AddressEndPoint? InfoGate => infoGate;
	public AddressEndPoint? LimeApi => lime;

	public override string ToString() => name;
	public string Map => $"\"{NpStsApi}\";\"{LobbyGate}\";\"{InfoGate}\";\"{LimeApi}\"";

	[ObservableProperty] bool _canStartUp;
	[ObservableProperty] string? _latency;
	#endregion

	#region Methods
	public void UpdateLatency() => Task.Run(() =>
	{
		var status = LobbyGate?.Telnet();
		if (status == true)
		{
			CanStartUp = Publisher is EPublisher.ZTX or EPublisher.ZNCG;
			Latency = StringHelper.Get("MainWindow_Latency", name, LobbyGate?.Ping());
		}
		else
		{
			CanStartUp = false;
			Latency = StringHelper.Get("MainWindow_Latency2", name);
		}
	});
	#endregion

	#region Data
	public static BnsApiEndpoint Tencent19 = new(EPublisher.Tencent, "电信一区", "*************:10900", "*************:12100");
	public static BnsApiEndpoint Tencent20 = new(EPublisher.Tencent, "电信二区 / 新区", "***************:10900", "***************:12100");
	public static BnsApiEndpoint Tencent30 = new(EPublisher.Tencent, "联通区", "220.194.117.214:10900", "220.194.117.214:12100");
	public static BnsApiEndpoint Tencent90 = new(EPublisher.Tencent, "南天国实验室", "101.91.21.44:10900", "101.91.21.44:12100");

	public static BnsApiEndpoint Ztx10 = new(EPublisher.ZTX, "拾忆朝华", "129.211.250.102:10900", "129.211.250.102:12100", "prod10-np-sts-api.bnsneo.qq.com:6600", "https://prod10-lime-api.bnsneo.qq.com:1443");
	public static BnsApiEndpoint Ztx11 = new(EPublisher.ZTX, "永恒序章", "129.211.251.111:10900", "129.211.251.111:12100", "prod11-np-sts-api.bnsneo.qq.com:6600", "https://prod11-lime-api.bnsneo.qq.com:1443");
	public static BnsApiEndpoint Ztx12 = new(EPublisher.ZTX, "重逢无限", "129.211.251.77:10900", "129.211.251.77:12100", "prod12-np-sts-api.bnsneo.qq.com:6600", "https://prod12-lime-api.bnsneo.qq.com:1443");
	public static BnsApiEndpoint Ztx13 = new(EPublisher.ZTX, "乘风而归", "129.211.253.198:10900", "129.211.253.198:12100", "prod13-np-sts-api.bnsneo.qq.com:6600", "https://prod13-lime-api.bnsneo.qq.com:1443");
	public static BnsApiEndpoint Ztx90 = new(EPublisher.ZTX, "南天国无影团", "119.45.67.33:10900", "119.45.67.33:12100", "staging-np-sts-api.bnsneo.qq.com:6600", "https://staging-lime-api.bnsneo.qq.com:1443");

    public static BnsApiEndpoint NeoTX1 = new(EPublisher.ZNCG, "决战巅峰", "***********:10900", "***********:12100", "prod20-np-sts-api.bnsneo.qq.com:6600", "https://prod20000-lime-api.bnsneo.qq.com:1443");
    public static BnsApiEndpoint NeoTX9 = new(EPublisher.ZNCG, "南天国黄沙团", "*************:10900", "*************:12100", "staging93-np-sts-api.bnsneo.qq.com:6600", "https://stage93-lime-api.bnsneo.qq.com:1443");
    #endregion
}

/// <summary>
/// Represents an endpoint defined by an address and port, derived from a DNS endpoint.
/// </summary>
/// <remarks>The <see cref="AddressEndPoint"/> class provides functionality to represent and interact with an
/// endpoint specified as a string in the format "host:port". It includes methods for network diagnostics, such as
/// pinging the host and testing connectivity via Telnet.</remarks>
/// <param name="address"></param>
public class AddressEndPoint(string address) : DnsEndPoint(
	address.SubstringBeforeLast(':'),
	address.SubstringAfterLast(':').To<int>())
{
	public long Ping()
	{
		var ping = new Ping();
		var reply = ping.Send(Host, 200);
		return reply.Status == IPStatus.Success ? reply.RoundtripTime : 999;
	}

	public bool Telnet(int timeout = 500)
	{
		try
		{
			return new TcpClient().ConnectAsync(Host, Port).Wait(timeout);
		}
		catch
		{
			return false;
		}
	}

	public override string ToString() => $"{Host}:{Port}";

	public static implicit operator AddressEndPoint?(string? address) => string.IsNullOrEmpty(address) ? null : new(address);
}
