﻿using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.Wpf;
using Newtonsoft.Json;
using System.Diagnostics;
using System.Net.Http;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class ActivityPageViewModel(WebView2 WebView) : ObservableObject
{
	private CoreWebView2 CoreWebView => WebView.CoreWebView2;

	internal async Task Test()
	{
		// 此处设置提交表单
		var cookie = new Dictionary<string, string>
		{
			["acctype"] = "qc",
			["openid"] = "E1A933F9ABEB7DFCFD7EFE8A8BD160DD",
			["access_token"] = "89EB8FC4F9DE4F4AB6544198C9AA12EF",
			["appid"] = "101491592",
		};
		var data = new Dictionary<string, string>
		{
			["iChartId"] = "390341",    //390337
			["iSubChartId"] = "390341",
			["sIdeToken"] = "KbPZXX",   //joE2Xy
			["isPreengage"] = "1",
			["needGopenid"] = "1",

			//["type"] = "1",
			["talkNo"] = "1",
			["chooseNo"] = "1"
		};

		var client = new HttpClient();
		client.DefaultRequestHeaders.Add("accept", "application/json, text/plain, */*");
		client.DefaultRequestHeaders.Add("cookie", string.Join(";", cookie.Select(x => $"{x.Key}={x.Value}")));

		var response = await client.PostAsync("https://comm.ams.game.qq.com/ide/", new FormUrlEncodedContent(data));
		var content = JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync());
		Debug.WriteLine(content);

		await WebView.CoreWebView2.ExecuteScriptAsync($$"""
			need("biz.login",function(LoginManager){
			  LoginManager.loginByWXAndQQ({
			    "appId":"xxxxxxx", //游戏在微信的appid
			    gameDomain:"xxx.qq.com",
			    serviceType:"xxx"
			  });
			  LoginManager.checkLogin(function(userinfo){
				console.log(userinfo.logtype);
			  });
			});
			""");
	}

	internal void Commit()
	{
		WebView.CoreWebView2.ExecuteScriptAsync($$"""
		var flow_1087761 = {
		  actId: '683323',
		  token: '81964c',
		  sData: {
		    type: 2, // 任务类型
		    taskId: 1, // 任务id
		  },
		  openToOpen:act.openToOpen,
		  success: function (res) {
		    var data = res.details.jData;
		    console.log('领取成功');
		  },
		  fail: function (res) {
		    console.log(res.sMsg);
		  }
		}

		Milo.emit(flow_1087761);
		""").Wait();
	}
}
