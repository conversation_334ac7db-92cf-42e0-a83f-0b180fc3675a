<?php
/**
 * 测试完整的公告Webhook流程
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "=== 测试完整的公告Webhook流程 ===\n\n";

// 模拟公告数据
$announcement = [
    'id' => 88888,
    'title' => 'HTTP Webhook测试公告 - ' . date('Y-m-d H:i:s'),
    'content' => '这是一个通过HTTP Webhook发送的测试公告，用于验证Go服务端是否能正确接收和处理公告推送。',
    'type' => 1,
    'priority' => 1,
    'target_client' => 'all',
    'status' => 1,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "1. 模拟公告数据:\n";
echo "   ID: " . $announcement['id'] . "\n";
echo "   标题: " . $announcement['title'] . "\n";
echo "   类型: " . $announcement['type'] . "\n";
echo "   优先级: " . $announcement['priority'] . "\n";
echo "   目标客户端: " . $announcement['target_client'] . "\n\n";

// 测试发布公告
echo "2. 测试发布公告...\n";
try {
    // 使用AnnouncementPushService发送Webhook
    $result = \app\manage\service\AnnouncementPushService::publishAnnouncementUpdate($announcement);
    
    if ($result) {
        echo "✓ 公告发布Webhook发送成功\n";
    } else {
        echo "✗ 公告发布Webhook发送失败\n";
    }
} catch (Exception $e) {
    echo "✗ 公告发布异常: " . $e->getMessage() . "\n";
}

echo "\n3. 测试更新公告...\n";
$announcement['title'] = 'HTTP Webhook测试公告 - 已更新 - ' . date('Y-m-d H:i:s');
try {
    // 构建更新消息
    $updateMessage = [
        'type' => 'updated',
        'announcement_id' => $announcement['id'],
        'title' => $announcement['title'],
        'type_code' => $announcement['type'],
        'priority' => $announcement['priority'],
        'target_client' => $announcement['target_client'],
        'timestamp' => time(),
    ];
    
    // 发送HTTP Webhook请求
    $webhookUrl = 'http://127.0.0.1:8080/webhook/announcement';
    $postData = json_encode($updateMessage);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $webhookUrl,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($postData)
        ],
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if (!$error && $httpCode === 200) {
        $responseData = json_decode($response, true);
        if ($responseData && $responseData['success']) {
            echo "✓ 公告更新Webhook发送成功\n";
        } else {
            echo "✗ 公告更新Webhook处理失败: " . ($responseData['message'] ?? 'unknown') . "\n";
        }
    } else {
        echo "✗ 公告更新Webhook请求失败: " . ($error ?: "HTTP $httpCode") . "\n";
    }
} catch (Exception $e) {
    echo "✗ 公告更新异常: " . $e->getMessage() . "\n";
}

echo "\n4. 测试删除公告...\n";
try {
    $result = \app\manage\service\AnnouncementPushService::publishAnnouncementDelete($announcement['id']);
    
    if ($result) {
        echo "✓ 公告删除Webhook发送成功\n";
    } else {
        echo "✗ 公告删除Webhook发送失败\n";
    }
} catch (Exception $e) {
    echo "✗ 公告删除异常: " . $e->getMessage() . "\n";
}

echo "\n5. 测试版本更新...\n";
try {
    $result = \app\manage\service\AnnouncementPushService::publishVersionUpdate();
    
    if ($result) {
        echo "✓ 版本更新Webhook发送成功\n";
    } else {
        echo "✗ 版本更新Webhook发送失败\n";
    }
} catch (Exception $e) {
    echo "✗ 版本更新异常: " . $e->getMessage() . "\n";
}

echo "\n6. 检查Go服务端状态...\n";
try {
    $healthUrl = 'http://127.0.0.1:8080/health';
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $healthUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $healthData = json_decode($response, true);
        echo "✓ Go服务端健康状态正常\n";
        echo "   服务: " . ($healthData['service'] ?? 'unknown') . "\n";
        echo "   状态: " . ($healthData['status'] ?? 'unknown') . "\n";
        echo "   时间戳: " . ($healthData['timestamp'] ?? 'unknown') . "\n";
    } else {
        echo "✗ Go服务端健康检查失败: HTTP $httpCode\n";
    }
} catch (Exception $e) {
    echo "✗ 健康检查异常: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n总结:\n";
echo "1. HTTP Webhook方式已经替代了Redis Pub/Sub方式\n";
echo "2. PHP后台可以通过HTTP API直接与Go服务端通信\n";
echo "3. Go服务端能够正确接收和处理公告推送请求\n";
echo "4. 这种方式更可靠，更容易调试和监控\n";
echo "\n建议:\n";
echo "- 在PHP管理后台发布真实公告测试完整流程\n";
echo "- 检查Go服务端日志确认消息处理情况\n";
echo "- 测试客户端是否能收到公告推送\n";
?>
