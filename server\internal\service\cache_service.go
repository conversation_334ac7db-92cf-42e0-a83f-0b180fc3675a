package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"udp-server/server/pkg/metrics"
	"github.com/redis/go-redis/v9"
)

// CacheService 缓存服务接口
type CacheService interface {
	Set(key string, value interface{}) error
	SetWithExpiration(key string, value interface{}, expiration time.Duration) error
	Get(key string) (interface{}, error)
	Delete(key string) error
	BatchSet(items map[string]interface{}) error
	BatchGet(keys []string) (map[string]interface{}, error)
	BatchDelete(keys []string) error
	// Hash操作
	HSet(key, field string, value interface{}) error
	HGet(key, field string) (interface{}, error)
	HExists(key, field string) (bool, error)
	HIncrBy(key, field string, incr int64) (int64, error)
	HDel(key string, fields ...string) error
	// 过期时间设置
	Expire(key string, expiration time.Duration) error
	// 分布式锁
	SetNX(key string, value interface{}, expiration time.Duration) (bool, error)
}

// RedisCache Redis缓存实现
type RedisCache struct {
	client     *redis.Client
	maxRetries int
	metrics    *metrics.Metrics
}

// NewRedisCache 创建Redis缓存实例
func NewRedisCache(addr, password string, db int, metrics *metrics.Metrics) (*RedisCache, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %v", err)
	}

	return &RedisCache{
		client:     client,
		maxRetries: 3,
		metrics:    metrics,
	}, nil
}

// Set 设置缓存
func (r *RedisCache) Set(key string, value interface{}) error {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	var lastErr error
	for i := 0; i < r.maxRetries; i++ {
		err := r.client.Set(ctx, key, data, 0).Err()
		if err == nil {
			return nil
		}
		lastErr = err
		time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
	}
	return fmt.Errorf("failed after %d retries: %v", r.maxRetries, lastErr)
}

// SetWithExpiration 设置缓存并指定过期时间
func (r *RedisCache) SetWithExpiration(key string, value interface{}, expiration time.Duration) error {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	var lastErr error
	for i := 0; i < r.maxRetries; i++ {
		err := r.client.Set(ctx, key, data, expiration).Err()
		if err == nil {
			return nil
		}
		lastErr = err
		time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
	}
	return fmt.Errorf("failed after %d retries: %v", r.maxRetries, lastErr)
}

// Get 获取缓存
func (r *RedisCache) Get(key string) (interface{}, error) {
	ctx := context.Background()
	var lastErr error
	for i := 0; i < r.maxRetries; i++ {
		data, err := r.client.Get(ctx, key).Bytes()
		if err == nil {
			var value interface{}
			if err := json.Unmarshal(data, &value); err != nil {
				return nil, err
			}
			r.metrics.RecordCacheHit()
			return value, nil
		}
		lastErr = err
		time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
	}
	r.metrics.RecordCacheMiss()
	return nil, fmt.Errorf("failed after %d retries: %v", r.maxRetries, lastErr)
}

// Delete 删除缓存
func (r *RedisCache) Delete(key string) error {
	ctx := context.Background()
	var lastErr error
	for i := 0; i < r.maxRetries; i++ {
		err := r.client.Del(ctx, key).Err()
		if err == nil {
			return nil
		}
		lastErr = err
		time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
	}
	return fmt.Errorf("failed after %d retries: %v", r.maxRetries, lastErr)
}

// BatchSet 批量设置缓存
func (r *RedisCache) BatchSet(items map[string]interface{}) error {
	ctx := context.Background()
	pipe := r.client.Pipeline()
	for key, value := range items {
		data, err := json.Marshal(value)
		if err != nil {
			return err
		}
		pipe.Set(ctx, key, data, 0)
	}
	_, err := pipe.Exec(ctx)
	return err
}

// BatchGet 批量获取缓存
func (r *RedisCache) BatchGet(keys []string) (map[string]interface{}, error) {
	ctx := context.Background()
	pipe := r.client.Pipeline()
	
	// 创建命令映射
	cmds := make([]*redis.StringCmd, len(keys))
	for i, key := range keys {
		cmds[i] = pipe.Get(ctx, key)
	}
	
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}
	
	result := make(map[string]interface{})
	for i, cmd := range cmds {
		data, err := cmd.Bytes()
		if err == nil {
			var value interface{}
			if err := json.Unmarshal(data, &value); err == nil {
				result[keys[i]] = value
				r.metrics.RecordCacheHit()
			}
		} else {
			r.metrics.RecordCacheMiss()
		}
	}
	
	return result, nil
}

// BatchDelete 批量删除缓存
func (r *RedisCache) BatchDelete(keys []string) error {
	ctx := context.Background()
	return r.client.Del(ctx, keys...).Err()
}

// HSet 设置Hash字段
func (r *RedisCache) HSet(key, field string, value interface{}) error {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return r.client.HSet(ctx, key, field, data).Err()
}

// HGet 获取Hash字段
func (r *RedisCache) HGet(key, field string) (interface{}, error) {
	ctx := context.Background()
	data, err := r.client.HGet(ctx, key, field).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("field not found")
		}
		return nil, err
	}
	var value interface{}
	if err := json.Unmarshal(data, &value); err != nil {
		return nil, err
	}
	return value, nil
}

// HExists 检查Hash字段是否存在
func (r *RedisCache) HExists(key, field string) (bool, error) {
	ctx := context.Background()
	return r.client.HExists(ctx, key, field).Result()
}

// HIncrBy Hash字段增量操作
func (r *RedisCache) HIncrBy(key, field string, incr int64) (int64, error) {
	ctx := context.Background()
	return r.client.HIncrBy(ctx, key, field, incr).Result()
}

// HDel 删除Hash字段
func (r *RedisCache) HDel(key string, fields ...string) error {
	ctx := context.Background()
	return r.client.HDel(ctx, key, fields...).Err()
}

// Expire 设置键的过期时间
func (r *RedisCache) Expire(key string, expiration time.Duration) error {
	ctx := context.Background()
	return r.client.Expire(ctx, key, expiration).Err()
}

// SetNX 分布式锁设置
func (r *RedisCache) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return false, err
	}
	return r.client.SetNX(ctx, key, data, expiration).Result()
}

// Close 关闭Redis连接
func (r *RedisCache) Close() error {
	return r.client.Close()
}
