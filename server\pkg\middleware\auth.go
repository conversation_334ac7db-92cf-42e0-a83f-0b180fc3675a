package middleware

import (
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/utils"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	authService    *service.AuthService
	responseHelper *utils.ResponseHelper
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(authService *service.AuthService) *AuthMiddleware {
	return &AuthMiddleware{
		authService:    authService,
		responseHelper: utils.NewResponseHelper(),
	}
}

// AuthenticatedHandler 需要认证的处理器函数类型
type AuthenticatedHandler func(user *model.User, args ...interface{}) ([]byte, error)

// WithTokenValidation Token验证装饰器
func (m *AuthMiddleware) WithTokenValidation(handler AuthenticatedHandler) func(token string, args ...interface{}) ([]byte, error) {
	return func(token string, args ...interface{}) ([]byte, error) {
		// 验证Token
		user, err := m.authService.ValidateToken(token)
		if err != nil {
			logger.Warn("Token验证失败: %v, Token=%s", err, token)
			return m.responseHelper.CreateAuthError("Token验证失败")
		}

		// 记录用户操作
		logger.Debug("用户操作: UID=%d, QQ=%d", user.UID, user.Uin)

		// 调用实际处理器
		return handler(user, args...)
	}
}

// WithPermissionCheck 权限检查装饰器
func (m *AuthMiddleware) WithPermissionCheck(requiredPermission uint8, handler AuthenticatedHandler) func(token string, args ...interface{}) ([]byte, error) {
	return m.WithTokenValidation(func(user *model.User, args ...interface{}) ([]byte, error) {
		// 检查权限
		if user.Permission < requiredPermission {
			logger.Warn("权限不足: UID=%d, 需要权限=%d, 当前权限=%d", user.UID, requiredPermission, user.Permission)
			return m.responseHelper.CreatePermissionError("权限不足")
		}

		return handler(user, args...)
	})
}

// WithRateLimit 频率限制装饰器
func (m *AuthMiddleware) WithRateLimit(maxRequests int, windowSeconds int, handler AuthenticatedHandler) func(token string, args ...interface{}) ([]byte, error) {
	return m.WithTokenValidation(func(user *model.User, args ...interface{}) ([]byte, error) {
		// TODO: 实现频率限制逻辑
		// 这里可以使用Redis来实现滑动窗口频率限制
		
		return handler(user, args...)
	})
}

// AuthResult 认证结果
type AuthResult struct {
	User  *model.User
	Error error
}

// ValidateTokenAsync 异步Token验证（用于批量操作）
func (m *AuthMiddleware) ValidateTokenAsync(token string) <-chan AuthResult {
	result := make(chan AuthResult, 1)
	
	go func() {
		defer close(result)
		
		user, err := m.authService.ValidateToken(token)
		result <- AuthResult{
			User:  user,
			Error: err,
		}
	}()
	
	return result
}

// BatchValidateTokens 批量Token验证
func (m *AuthMiddleware) BatchValidateTokens(tokens []string) map[string]*model.User {
	results := make(map[string]*model.User)
	channels := make(map[string]<-chan AuthResult)
	
	// 启动所有异步验证
	for _, token := range tokens {
		channels[token] = m.ValidateTokenAsync(token)
	}
	
	// 收集结果
	for token, ch := range channels {
		result := <-ch
		if result.Error == nil {
			results[token] = result.User
		} else {
			logger.Warn("批量Token验证失败: %v, Token=%s", result.Error, token)
		}
	}
	
	return results
}
