<?php
/**
 * 公告推送测试脚本
 * 用于测试Redis Pub/Sub公告推送功能
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Cache;

// 配置Redis连接
$redis = new Redis();
$redis->connect('127.0.0.1', 6379);
$redis->select(4); // 使用与Go服务相同的数据库

// 测试消息
$testMessages = [
    // 测试公告发布
    [
        'type' => 'ANNOUNCEMENT_PUBLISHED',
        'announcement_id' => 1001,
        'title' => '测试公告标题',
        'type_code' => 1,
        'priority' => 1,
        'target_client' => 'all',
        'timestamp' => time(),
    ],
    
    // 测试公告删除
    [
        'type' => 'ANNOUNCEMENT_DELETED',
        'announcement_id' => 1002,
        'timestamp' => time(),
    ],
    
    // 测试版本更新
    [
        'type' => 'ANNOUNCEMENT_VERSION_UPDATE',
        'timestamp' => time(),
    ],
];

echo "开始测试公告推送功能...\n";

foreach ($testMessages as $index => $message) {
    echo "\n--- 测试消息 " . ($index + 1) . " ---\n";
    echo "消息类型: " . $message['type'] . "\n";
    
    // 发布消息到Redis频道
    $result = $redis->publish('announcement_update', json_encode($message));
    
    if ($result > 0) {
        echo "✓ 消息发送成功，订阅者数量: $result\n";
    } else {
        echo "⚠ 消息发送成功，但无订阅者\n";
    }
    
    // 等待一秒再发送下一条消息
    sleep(1);
}

echo "\n测试完成！\n";
echo "请检查Go服务端日志以确认消息是否被正确接收和处理。\n";

$redis->close();
?>
