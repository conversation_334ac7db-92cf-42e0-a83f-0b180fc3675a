# 公告实时推送系统

## 概述

本系统实现了基于Redis Pub/Sub的公告实时推送功能，允许PHP后台管理系统发布公告后立即推送给所有在线客户端。

## 架构设计

```
PHP后台管理 → 发布公告 → 更新数据库 → 发送Redis Pub/Sub消息
                                              ↓
Go服务端 ← 订阅Redis频道 ← 接收推送消息 ← Redis
    ↓
推送给在线客户端 (UDP)
```

## 核心组件

### 1. PHP端推送服务
- **文件**: `app/manage/service/AnnouncementPushService.php`
- **功能**: 发布公告更新消息到Redis频道
- **频道**: `announcement_update`

### 2. Go端订阅服务
- **文件**: `internal/service/announcement_push_service.go`
- **功能**: 订阅Redis频道，处理公告更新消息，推送给在线用户

### 3. 消息类型
- `ANNOUNCEMENT_PUBLISHED`: 公告发布
- `ANNOUNCEMENT_DELETED`: 公告删除
- `ANNOUNCEMENT_VERSION_UPDATE`: 版本更新

## 使用方法

### PHP端发布公告
```php
use app\manage\service\AnnouncementPushService;

// 发布公告后自动推送
$announcement = AnnouncementModel::find($id);
$result = $announcement->publish();

if ($result) {
    // 发送实时推送通知
    AnnouncementPushService::publishAnnouncementUpdate($announcement->toArray());
}
```

### 消息格式
```json
{
    "type": "ANNOUNCEMENT_PUBLISHED",
    "announcement_id": 1001,
    "title": "公告标题",
    "type_code": 1,
    "priority": 1,
    "target_client": "all",
    "timestamp": 1672531200
}
```

## 配置要求

### Redis配置
- 确保PHP和Go服务使用相同的Redis实例
- 数据库编号: 4 (可在配置中修改)
- 频道名称: `announcement_update`

### Go服务配置
```yaml
redis:
  host: "127.0.0.1"
  port: 6379
  password: ""
  db: 4
```

## 测试方法

### 1. 使用测试脚本
```bash
cd server/tools.bnszs.com_tp6
php test_announcement_push.php
```

### 2. 手动测试
1. 启动Go服务端
2. 在PHP后台管理界面发布公告
3. 检查Go服务端日志确认消息接收
4. 检查客户端是否收到推送

## 日志监控

### Go服务端日志
```
[INFO] 启动公告更新订阅，频道: announcement_update
[DEBUG] 收到公告更新消息: {"type":"ANNOUNCEMENT_PUBLISHED",...}
[INFO] 处理公告发布: ID=1001, 标题=测试公告
[INFO] 公告消息推送完成，推送用户数: 5
```

### PHP端日志
```
[INFO] 公告更新通知已发送 {"announcement_id":1001,"subscribers":1}
```

## 故障排除

### 1. 消息发送成功但无订阅者
- 检查Go服务端是否正常运行
- 检查Redis连接配置是否一致

### 2. Go服务端无法接收消息
- 检查Redis连接配置
- 检查频道名称是否正确
- 检查Redis Pub/Sub功能是否正常

### 3. 客户端未收到推送
- 检查用户是否在线
- 检查UDP推送逻辑是否实现
- 检查网络连接状态

## 扩展功能

### 1. 支持更多消息类型
- 公告修改通知
- 定时公告发布
- 分组推送

### 2. 推送策略优化
- 按客户端类型推送
- 按用户权限推送
- 推送失败重试机制

### 3. 监控和统计
- 推送成功率统计
- 用户接收状态跟踪
- 性能监控指标

## 注意事项

1. **Redis连接**: 确保PHP和Go使用相同的Redis配置
2. **消息格式**: 严格按照定义的JSON格式发送消息
3. **错误处理**: 推送失败不应影响公告发布流程
4. **性能考虑**: 大量在线用户时注意推送性能
5. **安全性**: 验证消息来源和内容合法性
