<?php
declare(strict_types=1);

namespace app\manage\service;

use think\facade\Cache;
use think\facade\Log;
use Exception;

/**
 * 公告推送服务
 * 负责将公告更新通知发送给Go服务端
 */
class AnnouncementPushService
{
    /**
     * Redis频道名称
     */
    const ANNOUNCEMENT_UPDATE_CHANNEL = 'announcement_update';
    
    /**
     * 发布公告更新通知
     *
     * @param array $announcement 公告数据
     * @return bool
     */
    public static function publishAnnouncementUpdate(array $announcement): bool
    {
        try {
            // 构建推送消息
            $message = [
                'type' => 'published',
                'announcement_id' => $announcement['id'],
                'title' => $announcement['title'],
                'type_code' => $announcement['type'],
                'priority' => $announcement['priority'],
                'target_client' => $announcement['target_client'] ?? 'all',
                'timestamp' => time(),
            ];

            // 发送HTTP Webhook请求
            return self::sendWebhookRequest($message, 'published');

        } catch (\Exception $e) {
            Log::error("发送公告更新通知失败", [
                'announcement_id' => $announcement['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 发布公告删除通知
     *
     * @param int $announcementId 公告ID
     * @return bool
     */
    public static function publishAnnouncementDelete(int $announcementId): bool
    {
        try {
            $message = [
                'type' => 'deleted',
                'announcement_id' => $announcementId,
                'timestamp' => time(),
            ];

            return self::sendWebhookRequest($message, 'deleted');

        } catch (\Exception $e) {
            Log::error("发送公告删除通知失败", [
                'announcement_id' => $announcementId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 发布版本更新通知
     *
     * @return bool
     */
    public static function publishVersionUpdate(): bool
    {
        try {
            $message = [
                'type' => 'version_update',
                'timestamp' => time(),
            ];

            return self::sendWebhookRequest($message, 'version_update');

        } catch (\Exception $e) {
            Log::error("发送公告版本更新通知失败", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送Webhook请求到Go服务端
     *
     * @param array $message 消息数据
     * @param string $action 操作类型
     * @return bool
     */
    private static function sendWebhookRequest(array $message, string $action): bool
    {
        try {
            // Go服务端Webhook URL
            $webhookUrl = 'http://127.0.0.1:8080/webhook/announcement';

            // 准备请求数据
            $postData = json_encode($message);

            Log::info("发送Webhook请求", [
                'url' => $webhookUrl,
                'action' => $action,
                'data' => $message
            ]);

            // 使用cURL发送POST请求
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $webhookUrl,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $postData,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_CONNECTTIMEOUT => 5,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($postData)
                ],
                CURLOPT_USERAGENT => 'BNS-PHP-Webhook/1.0'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error("Webhook请求失败", [
                    'url' => $webhookUrl,
                    'action' => $action,
                    'curl_error' => $error
                ]);
                return false;
            }

            if ($httpCode !== 200) {
                Log::error("Webhook请求返回错误状态码", [
                    'url' => $webhookUrl,
                    'action' => $action,
                    'http_code' => $httpCode,
                    'response' => $response
                ]);
                return false;
            }

            // 解析响应
            $responseData = json_decode($response, true);
            if ($responseData && isset($responseData['success']) && $responseData['success']) {
                Log::info("Webhook请求成功", [
                    'url' => $webhookUrl,
                    'action' => $action,
                    'response' => $responseData
                ]);
                return true;
            } else {
                Log::error("Webhook请求失败", [
                    'url' => $webhookUrl,
                    'action' => $action,
                    'response' => $responseData
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error("发送Webhook请求异常", [
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
