package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"udp-server/server/pkg/logger"

	"github.com/redis/go-redis/v9"
)

// AnnouncementPushService 公告推送服务
type AnnouncementPushService struct {
	redisClient *redis.Client
	ctx         context.Context
	authService *AuthService // 用于获取在线用户列表
}

// AnnouncementUpdateMessage 公告更新消息结构
type AnnouncementUpdateMessage struct {
	Type           string `json:"type"`            // 消息类型：ANNOUNCEMENT_PUBLISHED, ANNOUNCEMENT_DELETED, ANNOUNCEMENT_VERSION_UPDATE
	AnnouncementID uint64 `json:"announcement_id"` // 公告ID
	Title          string `json:"title"`           // 公告标题
	TypeCode       int    `json:"type_code"`       // 公告类型代码
	Priority       int    `json:"priority"`        // 优先级
	TargetClient   string `json:"target_client"`   // 目标客户端
	Timestamp      int64  `json:"timestamp"`       // 时间戳
}

// NewAnnouncementPushService 创建公告推送服务
func NewAnnouncementPushService(redisHost string, redisPort int, redisPassword string, redisDB int, authService *AuthService) (*AnnouncementPushService, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", redisHost, redisPort),
		Password: redisPassword,
		DB:       redisDB,
	})

	// 测试连接
	ctx := context.Background()
	_, err := client.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis for announcement push service: %v", err)
	}

	service := &AnnouncementPushService{
		redisClient: client,
		ctx:         ctx,
		authService: authService,
	}

	// 启动订阅监听
	go service.startSubscription()

	return service, nil
}

// startSubscription 启动Redis订阅监听
func (s *AnnouncementPushService) startSubscription() {
	const channelName = "announcement_update"
	
	logger.Info("启动公告更新订阅，频道: %s", channelName)
	
	// 创建订阅
	pubsub := s.redisClient.Subscribe(s.ctx, channelName)
	defer pubsub.Close()

	// 监听消息
	ch := pubsub.Channel()
	logger.Info("开始监听Redis消息...")

	for msg := range ch {
		logger.Info("接收到Redis消息，频道: %s", msg.Channel)
		if err := s.handleAnnouncementUpdate(msg.Payload); err != nil {
			logger.Error("处理公告更新消息失败: %v", err)
		} else {
			logger.Info("公告更新消息处理成功")
		}
	}

	logger.Warn("Redis订阅循环结束")
}

// handleAnnouncementUpdate 处理公告更新消息
func (s *AnnouncementPushService) handleAnnouncementUpdate(payload string) error {
	// 使用Info级别确保消息被记录
	logger.Info("=== 收到公告更新消息 ===")
	logger.Info("消息内容: %s", payload)

	var message AnnouncementUpdateMessage
	if err := json.Unmarshal([]byte(payload), &message); err != nil {
		logger.Error("解析公告更新消息失败: %v, 原始消息: %s", err, payload)
		return fmt.Errorf("解析公告更新消息失败: %v", err)
	}

	logger.Info("解析成功，消息类型: %s, 公告ID: %d", message.Type, message.AnnouncementID)

	switch message.Type {
	case "ANNOUNCEMENT_PUBLISHED":
		logger.Info("处理公告发布消息...")
		return s.handleAnnouncementPublished(&message)
	case "ANNOUNCEMENT_DELETED":
		logger.Info("处理公告删除消息...")
		return s.handleAnnouncementDeleted(&message)
	case "ANNOUNCEMENT_VERSION_UPDATE":
		logger.Info("处理版本更新消息...")
		return s.handleVersionUpdate(&message)
	default:
		logger.Warn("未知的公告更新消息类型: %s", message.Type)
		return nil
	}
}

// handleAnnouncementPublished 处理公告发布
func (s *AnnouncementPushService) handleAnnouncementPublished(message *AnnouncementUpdateMessage) error {
	logger.Info("处理公告发布: ID=%d, 标题=%s", message.AnnouncementID, message.Title)
	
	// 构建推送消息
	pushMessage := map[string]interface{}{
		"type":            "ANNOUNCEMENT_PUSH",
		"announcement_id": message.AnnouncementID,
		"title":           message.Title,
		"message":         fmt.Sprintf("新公告：%s", message.Title),
		"priority":        message.Priority,
		"timestamp":       time.Now().Unix(),
	}

	// 推送给所有在线用户
	return s.pushToOnlineUsers(pushMessage)
}

// handleAnnouncementDeleted 处理公告删除
func (s *AnnouncementPushService) handleAnnouncementDeleted(message *AnnouncementUpdateMessage) error {
	logger.Info("处理公告删除: ID=%d", message.AnnouncementID)
	
	// 构建推送消息
	pushMessage := map[string]interface{}{
		"type":            "ANNOUNCEMENT_DELETED",
		"announcement_id": message.AnnouncementID,
		"message":         "公告已删除",
		"timestamp":       time.Now().Unix(),
	}

	// 推送给所有在线用户
	return s.pushToOnlineUsers(pushMessage)
}

// handleVersionUpdate 处理版本更新
func (s *AnnouncementPushService) handleVersionUpdate(message *AnnouncementUpdateMessage) error {
	logger.Info("处理公告版本更新")
	
	// 构建推送消息
	pushMessage := map[string]interface{}{
		"type":      "ANNOUNCEMENT_VERSION_UPDATE",
		"message":   "公告列表已更新，请刷新",
		"timestamp": time.Now().Unix(),
	}

	// 推送给所有在线用户
	return s.pushToOnlineUsers(pushMessage)
}

// pushToOnlineUsers 推送消息给所有在线用户
func (s *AnnouncementPushService) pushToOnlineUsers(message map[string]interface{}) error {
	// 获取在线用户列表
	onlineUsers := s.authService.GetOnlineUsers()
	if len(onlineUsers) == 0 {
		logger.Debug("当前无在线用户，跳过推送")
		return nil
	}

	// 序列化消息（暂时不使用，等待实现UDP推送逻辑）
	_, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化推送消息失败: %v", err)
	}

	pushCount := 0
	for _, userID := range onlineUsers {
		// TODO: 这里需要实现实际的UDP推送逻辑
		// 暂时只记录日志
		logger.Debug("推送公告消息给用户: %d", userID)
		pushCount++
	}

	logger.Info("公告消息推送完成，推送用户数: %d", pushCount)
	return nil
}

// Close 关闭服务
func (s *AnnouncementPushService) Close() error {
	if s.redisClient != nil {
		return s.redisClient.Close()
	}
	return nil
}
