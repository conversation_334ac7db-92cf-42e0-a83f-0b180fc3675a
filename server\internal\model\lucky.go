package model

import (
	"time"

	"udp-server/server/pkg/utils"
)

// Lucky 签到抽奖活动模型
type Lucky struct {
	ID         uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name       string    `gorm:"column:name;size:255;not null" json:"name"`           // 活动名称
	StartTime  time.Time `gorm:"column:startTime;not null" json:"start_time"`         // 开始时间
	EndTime    time.Time `gorm:"column:endTime;not null" json:"end_time"`             // 结束时间
	IsActivity bool      `gorm:"column:is_activity;default:0" json:"is_activity"`     // 是否为活动状态
	Free       int       `gorm:"column:free;default:1" json:"free"`                   // 每日免费次数
	IsContinue bool      `gorm:"column:is_continue;default:0" json:"is_continue"`     // 是否重置奖励点数
	UpdateTime time.Time `gorm:"column:updateTime;autoUpdateTime" json:"update_time"` // 最后更新时间
}

// TableName 指定表名
func (Lucky) TableName() string {
	return "bns_lucky"
}

// IsActive 检查活动是否正在进行中
func (l *Lucky) IsActive() bool {
	now := time.Now()
	return l.IsActivity && now.After(l.StartTime) && now.Before(l.EndTime)
}

// UserDraw 用户签到记录模型
type UserDraw struct {
	UID      uint64    `gorm:"column:uid;primaryKey" json:"uid"`           // 用户ID (复合主键)
	Schedule uint      `gorm:"column:schedule;primaryKey" json:"schedule"` // 活动ID (复合主键)
	Extra    int       `gorm:"column:extra;default:0" json:"extra"`        // 额外次数
	Day      int       `gorm:"column:day;default:1" json:"day"`            // 连续签到天数
	Point    int       `gorm:"column:point;default:1" json:"point"`        // 奖励点数
	Number   int       `gorm:"column:number;default:0" json:"number"`      // 总签到次数
	Today    int       `gorm:"column:today;default:0" json:"today"`        // 今日签到次数
	Time     time.Time `gorm:"column:time;autoCreateTime" json:"time"`     // 最后签到时间
}

// TableName 指定表名
func (UserDraw) TableName() string {
	return "user_draw"
}

// IsToday 检查是否为今天签到
func (u *UserDraw) IsToday() bool {
	return utils.IsToday(u.Time)
}

// IsYesterday 检查是否为昨天签到
func (u *UserDraw) IsYesterday() bool {
	return utils.IsYesterday(u.Time)
}

// LuckyReward 奖励配置模型
type LuckyReward struct {
	ID           uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Activity     uint      `gorm:"column:activity;not null;index" json:"activity"`        // 活动ID
	IsUse        bool      `gorm:"column:is_use;default:1" json:"is_use"`                 // 是否启用
	IsResetPoint bool      `gorm:"column:is_reset_point;default:0" json:"is_reset_point"` // 是否重置点数
	Point        int       `gorm:"column:point;default:1" json:"point"`                   // 所需点数
	Reward       string    `gorm:"column:reward;size:255" json:"reward"`                  // 奖励内容（CDKey等）
	Text         string    `gorm:"column:text;type:text" json:"text"`                     // 奖励描述文本
	Type         string    `gorm:"column:type;size:50;default:'random'" json:"type"`      // 奖励类型：random/guard
	Weight       int       `gorm:"column:weight;default:0" json:"weight"`                 // 权重
	Limit        int       `gorm:"column:limit;default:0" json:"limit"`                   // 限制次数，0为无限制
	LimitCurrent int       `gorm:"column:limit_current;default:0" json:"limit_current"`   // 当前已使用次数
	Admin        *int      `gorm:"column:admin" json:"admin"`                             // 管理员ID
	CreateTime   time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"`  // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"`  // 更新时间
}

// TableName 指定表名
func (LuckyReward) TableName() string {
	return "bns_lucky_reward"
}

// IsAvailable 检查奖励是否可用
func (r *LuckyReward) IsAvailable() bool {
	return r.IsUse && (r.Limit == 0 || r.LimitCurrent < r.Limit)
}

// CDkey CDKey模型
type CDkey struct {
	ID         uint       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	CDkey      string     `gorm:"column:cdkey;size:255;not null;uniqueIndex" json:"cdkey"` // CDKey
	Type       string     `gorm:"column:type;size:50;not null;index" json:"type"`          // 类型
	MaxNum     uint       `gorm:"column:MaxNum;default:0" json:"max_num"`                  // 可激活的最大数量
	RemainNum  uint       `gorm:"column:RemainNum;default:0" json:"remain_num"`            // 可激活的剩余数量
	Reason     string     `gorm:"column:reason;size:255" json:"reason"`                    // 原因
	StartTime  *time.Time `gorm:"column:startTime" json:"start_time"`                      // 激活开始时间
	EndTime    *time.Time `gorm:"column:endTime" json:"end_time"`                          // 激活结束时间
	UpdateTime time.Time  `gorm:"column:updateTime;autoUpdateTime" json:"update_time"`     // 最后更新时间
	Batch      *int       `gorm:"column:batch" json:"batch"`                               // 批次
	Admin      *int       `gorm:"column:admin" json:"admin"`                               // 生成管理员ID
	Group      *int64     `gorm:"column:group" json:"group"`                               // 需要加入的QQ群号
}

// TableName 指定表名
func (CDkey) TableName() string {
	return "bns_cdkey"
}

// IsUsed 检查CDKey是否已使用完毕
func (c *CDkey) IsUsed() bool {
	return c.RemainNum <= 0
}

// IsActive 检查CDKey是否在有效期内
func (c *CDkey) IsActive() bool {
	now := time.Now()

	// 检查开始时间
	if c.StartTime != nil && now.Before(*c.StartTime) {
		return false
	}

	// 检查结束时间
	if c.EndTime != nil && now.After(*c.EndTime) {
		return false
	}

	return true
}

// UserDrawResult 用户抽奖结果记录
type UserDrawResult struct {
	ID     uint   `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Reward uint   `gorm:"column:reward;not null;index" json:"reward"` // 奖励ID
	UID    uint64 `gorm:"column:uid;not null;index" json:"uid"`       // 用户ID
}

// TableName 指定表名
func (UserDrawResult) TableName() string {
	return "user_draw_result"
}
