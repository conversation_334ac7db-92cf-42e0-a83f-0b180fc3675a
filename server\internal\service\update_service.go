package service

import (
	"fmt"
	"strconv"
	"time"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/version"

	"gorm.io/gorm"
)

// UpdateService 更新配置服务
type UpdateService struct {
	db      *gorm.DB
	cache   cache.Cache
	metrics *UpdateMetrics
}

// NewUpdateService 创建更新配置服务
func NewUpdateService(db *gorm.DB, cache cache.Cache) *UpdateService {
	service := &UpdateService{
		db:      db,
		cache:   cache,
		metrics: NewUpdateMetrics(),
	}

	// 启动定期统计日志
	go service.startMetricsLogger()

	return service
}

// GetUpdateConfig 获取更新配置
func (s *UpdateService) GetUpdateConfig(appName, clientVersion string) (*model.UpdateConfigResponse, error) {
	startTime := time.Now()

	// 记录请求指标
	s.metrics.RecordRequest(appName)

	// 增加今日更新请求统计
	s.incrementUpdateRequestCount()

	// 尝试从缓存获取配置
	cacheKey := fmt.Sprintf("update_config:%s", appName)
	var config model.UpdateConfig

	// 先检查缓存
	if s.cache != nil {
		var cachedConfig model.UpdateConfig
		err := s.cache.Get(cacheKey, &cachedConfig)
		if err == nil {
			s.metrics.RecordCacheHit()
			config = cachedConfig
		} else {
			s.metrics.RecordCacheMiss()
			s.metrics.RecordCacheGetError()
		}
	}

	// 缓存未命中，从数据库获取配置
	if config.ID == 0 {
		s.metrics.RecordCacheMiss()
		err := s.db.Where("name = ? AND is_active = ?", appName, true).First(&config).Error
		if err != nil {
			s.metrics.RecordDatabaseError()
			s.metrics.RecordError()
			s.metrics.RecordResponseTime(time.Since(startTime))
			if err == gorm.ErrRecordNotFound {
				return nil, nil
			}
			return nil, fmt.Errorf("failed to get update config: %v", err)
		}

		// 将配置存入缓存（缓存30分钟）
		if s.cache != nil {
			err = s.cache.Set(cacheKey, config, 30*time.Minute)
			if err != nil {
				s.metrics.RecordCacheSetError()
				logger.Warn("Failed to cache update config for %s: %v", appName, err)
			} else {
				logger.Debug("Update config cached for app: %s", appName)
			}
		}
	}

	// 比较版本，判断是否需要更新
	needsUpdate, err := version.IsVersionOutdated(clientVersion, config.Version)
	if err != nil {
		s.metrics.RecordVersionCompareError()
		logger.Warn("Version comparison failed for %s: client=%s, server=%s, error=%v",
			appName, clientVersion, config.Version, err)
		// 版本比较失败时，假设需要更新以确保安全
		needsUpdate = true
	} else {
		s.metrics.RecordVersionCompareOK()
	}

	// 获取Groups数据
	groups, err := s.getActiveGroups()
	if err != nil {
		logger.Warn("Failed to get active groups: %v", err)
		groups = []int64{} // 使用空数组作为后备
	}

	// 构建响应
	response := &model.UpdateConfigResponse{
		Groups: groups, // 总是包含Groups数据
	}

	// 只有在需要更新时才包含下载信息
	if needsUpdate {
		s.metrics.RecordUpdateNeeded()
		response.ExecutablePath = config.ExecutablePath
		response.DownloadURL = config.URL
		response.Checksum = config.Checksum
	} else {
		s.metrics.RecordNoUpdateNeeded()
	}

	// 记录成功和响应时间
	s.metrics.RecordSuccess()
	s.metrics.RecordResponseTime(time.Since(startTime))

	return response, nil
}

// IsVersionOutdated 检查客户端版本是否过时
// 直接比较客户端版本与最新版本，不依赖缓存
func (s *UpdateService) IsVersionOutdated(appName, clientVersion string) bool {
	// 获取最新版本配置
	latestConfig, err := s.GetLatestUpdateConfig(appName)
	if err != nil {
		logger.Error("Failed to get latest update config for %s: %v", appName, err)
		return false
	}

	if latestConfig == nil {
		return false
	}

	// 使用 Version 类进行版本比较
	isOutdated, err := version.IsVersionOutdated(clientVersion, latestConfig.Version)
	if err != nil {
		logger.Error("Failed to compare versions for app %s: client=%s, latest=%s, error=%v",
			appName, clientVersion, latestConfig.Version, err)
		// 版本比较失败时，为了安全起见假设不需要更新
		return false
	}

	return isOutdated
}

// GetLatestUpdateConfig 获取最新的更新配置
func (s *UpdateService) GetLatestUpdateConfig(appName string) (*model.UpdateConfig, error) {
	// 先尝试从缓存获取
	cacheKey := fmt.Sprintf("latest_config:%s", appName)
	if s.cache != nil {
		var config model.UpdateConfig
		err := s.cache.Get(cacheKey, &config)
		if err == nil {
			return &config, nil
		}
	}

	// 从数据库获取最新配置
	var config model.UpdateConfig
	err := s.db.Where("name = ? AND is_active = ?", appName, true).
		Order("created_at DESC").
		First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	// 缓存结果（缓存10分钟）
	if s.cache != nil {
		s.cache.Set(cacheKey, config, 60*time.Minute)
	}

	return &config, nil
}

// SetForceUpdate 设置强制更新标志
// 当管理员发布新版本时调用此方法，强制所有在线客户端更新
func (s *UpdateService) SetForceUpdate(appName string, force bool) error {
	cacheKey := fmt.Sprintf("force_update:%s", appName)

	if s.cache != nil {
		// 设置强制更新标志，缓存1小时
		err := s.cache.Set(cacheKey, force, 1*time.Hour)
		if err != nil {
			logger.Error("Failed to set force update flag for %s: %v", appName, err)
			return err
		}

		if force {
			logger.Info("Force update flag set for app: %s", appName)
		} else {
			logger.Info("Force update flag cleared for app: %s", appName)
		}
	}

	return nil
}

// incrementUpdateRequestCount 增加今日更新请求统计
func (s *UpdateService) incrementUpdateRequestCount() {
	today := time.Now().Format("2006-01-02")
	cacheKey := fmt.Sprintf("update_request_count:%s", today)

	// 使用Redis INCR命令增加计数
	_, err := s.cache.Increment(cacheKey, 1)
	if err != nil {
		logger.Error("Failed to increment update request count: %v", err)
	}

	// 设置过期时间为7天
	s.cache.Expire(cacheKey, 7*24*time.Hour)
}

// RefreshCache 刷新缓存
func (s *UpdateService) RefreshCache(appName string) error {
	cacheKey := fmt.Sprintf("update_config:%s", appName)

	// 删除缓存
	if s.cache != nil {
		if err := s.cache.Delete(cacheKey); err != nil {
			logger.Error("Failed to delete cache for app %s: %v", appName, err)
		}
	}

	// 重新加载配置到缓存
	_, err := s.GetUpdateConfig(appName, "")
	if err != nil {
		return fmt.Errorf("failed to refresh cache: %v", err)
	}

	logger.Info("Cache refreshed for app: %s", appName)
	return nil
}

// RefreshAllCache 刷新所有应用的缓存
func (s *UpdateService) RefreshAllCache() error {
	// 获取所有活跃的应用名称
	var appNames []string
	err := s.db.Model(&model.UpdateConfig{}).
		Where("is_active = ?", true).
		Distinct("name").
		Pluck("name", &appNames).Error

	if err != nil {
		return fmt.Errorf("failed to get app names: %v", err)
	}

	// 刷新每个应用的缓存
	for _, appName := range appNames {
		if err := s.RefreshCache(appName); err != nil {
			logger.Error("Failed to refresh cache for app %s: %v", appName, err)
		}
	}

	// 刷新Groups缓存
	if err := s.RefreshGroupsCache(); err != nil {
		logger.Error("Failed to refresh groups cache: %v", err)
	}

	logger.Info("All update config caches refreshed")
	return nil
}

// RefreshGroupsCache 刷新Groups缓存
func (s *UpdateService) RefreshGroupsCache() error {
	cacheKey := "active_groups"

	// 删除Groups缓存
	if s.cache != nil {
		if err := s.cache.Delete(cacheKey); err != nil {
			logger.Error("Failed to delete groups cache: %v", err)
		}
	}

	// 重新加载Groups到缓存
	_, err := s.getActiveGroups()
	if err != nil {
		return fmt.Errorf("failed to refresh groups cache: %v", err)
	}

	logger.Info("Groups cache refreshed")
	return nil
}

// GetMetrics 获取性能指标
func (s *UpdateService) GetMetrics() map[string]interface{} {
	return s.metrics.GetStats()
}

// ResetMetrics 重置性能指标
func (s *UpdateService) ResetMetrics() {
	s.metrics.Reset()
	logger.Info("Update service metrics reset")
}

// getActiveGroups 获取活跃的群组列表
func (s *UpdateService) getActiveGroups() ([]int64, error) {
	// 尝试从缓存获取群组数据
	cacheKey := "active_groups"
	var cachedGroups []int64

	if s.cache != nil {
		err := s.cache.Get(cacheKey, &cachedGroups)
		if err == nil {
			return cachedGroups, nil
		}
	}

	// 从数据库查询活跃群组
	var groups []model.WhitelistGroup
	err := s.db.Where("is_active = ?", true).Find(&groups).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query active groups: %v", err)
	}

	logger.Debug("Found %d active groups in database", len(groups))

	// 转换群组ID为int64数组
	groupIDs := make([]int64, len(groups))
	for i, group := range groups {
		// 将字符串群组ID转换为int64
		if groupID, parseErr := strconv.ParseInt(group.GroupID, 10, 64); parseErr == nil {
			groupIDs[i] = groupID
			logger.Debug("Group ID converted: %s -> %d", group.GroupID, groupID)
		} else {
			logger.Warn("Failed to parse group ID: %s, error: %v", group.GroupID, parseErr)
			// 解析失败时使用0作为占位符
			groupIDs[i] = 0
		}
	}

	// 将结果存入缓存（缓存7天，持久缓存，管理员手动刷新）
	if s.cache != nil {
		err = s.cache.Set(cacheKey, groupIDs, 7*24*time.Hour)
		if err != nil {
			logger.Warn("Failed to cache active groups: %v", err)
		} else {
			logger.Debug("Active groups cached for 7 days: %v", groupIDs)
		}
	}

	logger.Info("Active groups retrieved: %v", groupIDs)
	return groupIDs, nil
}

// startMetricsLogger 启动定期指标日志
func (s *UpdateService) startMetricsLogger() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟记录一次
	defer ticker.Stop()

	for range ticker.C {
		s.metrics.LogStats()
	}
}
