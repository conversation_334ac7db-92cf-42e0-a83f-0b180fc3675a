using System.Text;
using System.Windows;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;
internal class LoginPacket : BasePacket
{
    #region Request Fields
    /// <summary>
    /// QQ号码
    /// </summary>
    public string Uin { get; set; }

    /// <summary>
    /// 设备指纹
    /// </summary>
    public string DeviceFingerprint { get; set; }
    #endregion

    #region Response Fields
    public byte Permission { get; set; } = 0;

    public long PermissionExpiration { get; set; } = 0;
    #endregion

    #region Methods
    public override DataArchiveWriter Create()
    {
        // 登录请求包不存在Token信息
        using var writer = new DataArchiveWriter();
        writer.WriteString(Uin, Encoding.UTF8);
        writer.WriteString(DeviceFingerprint, Encoding.UTF8);
        return writer;
    }

    /// <summary>
    /// 解析登录响应数据
    /// </summary>
    /// <param name="reader">数据读取器</param>
    protected override void ReadResponse(DataArchive reader)
    {
        Token = reader.ReadString();
        Permission = reader.Read<byte>();           // 用户当前权限
        PermissionExpiration = reader.Read<long>(); // 权限过期时间

        // 读取插件信息
        if (reader.Position < reader.Length)
        {
            Application.Current.Properties["PluginVersion"] = reader.ReadString();
            Application.Current.Properties["PluginUrl"] = reader.ReadString();
        }
    }
    #endregion
}
