package database

import (
	"fmt"
	"time"
	"udp-server/server/internal/model"
	"gorm.io/gorm"
)

// AutoMigrate 自动执行数据库迁移
func AutoMigrate() error {
	// 先执行SQL命令禁用外键检查
	DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// 检查表是否存在，如果不存在则创建
	migrator := DB.Migrator()

	// 按照依赖关系顺序创建表
	tables := []interface{}{
		&model.User{},
		&model.DeviceHistory{},
		&model.Lucky{},
		&model.UserDraw{},
		&model.LuckyReward{},
		&model.CDkey{},
		&model.UserDrawResult{},
		&model.OnlineStatsHistory{},
		&model.RiskEvent{},            // 风控事件表
		&model.RiskControlConfig{},    // 风控配置表
		&model.AdminLog{},             // 管理员日志表
		&model.Announcement{},         // 公告表
		&model.AnnouncementVersion{},  // 公告版本表
		&model.UpdateConfig{},         // 更新配置表
		&model.WhitelistGroup{},       // 白名单群组表
	}

	// 只创建不存在的表，跳过已存在的表以避免索引冲突
	for _, table := range tables {
		if !migrator.HasTable(table) {
			fmt.Printf("Creating table for %T\n", table)
			if err := migrator.CreateTable(table); err != nil {
				return fmt.Errorf("failed to create table %T: %v", table, err)
			}
		} else {
			fmt.Printf("Table for %T already exists, skipping migration\n", table)
		}
	}

	// 插入初始数据
	if err := insertInitialData(DB); err != nil {
		return fmt.Errorf("failed to insert initial data: %v", err)
	}

	fmt.Println("Database migration completed successfully")
	return nil
}

// insertInitialData 插入初始数据
func insertInitialData(db *gorm.DB) error {
	// 插入公告版本初始记录
	var count int64
	db.Model(&model.AnnouncementVersion{}).Count(&count)
	if count == 0 {
		initialVersion := &model.AnnouncementVersion{
			Version:      1,
			LastModified: time.Now(),
		}
		if err := db.Create(initialVersion).Error; err != nil {
			return fmt.Errorf("failed to create initial announcement version: %v", err)
		}
		fmt.Println("Initial announcement version record created")
	}

	return nil
}
