package model

import (
	"time"
)

// Announcement 公告模型
type Announcement struct {
	ID                 uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Title              string    `gorm:"column:title;size:200;not null" json:"title"`
	Content            string    `gorm:"column:content;type:text;not null" json:"content"`
	Type               int       `gorm:"column:type;not null;default:1" json:"type"`                         // 1-普通，2-重要，3-紧急
	Status             int       `gorm:"column:status;not null;default:1" json:"status"`                     // 0-草稿，1-已发布，2-已下线
	Priority           int       `gorm:"column:priority;not null;default:0" json:"priority"`                 // 优先级，数字越大越高
	StartTime          *time.Time `gorm:"column:start_time" json:"start_time"`                               // 开始显示时间
	EndTime            *time.Time `gorm:"column:end_time" json:"end_time"`                                   // 结束显示时间
	TargetClient       string    `gorm:"column:target_client;size:50;default:'all'" json:"target_client"`   // 目标客户端
	VersionRequirement string    `gorm:"column:version_requirement;size:50" json:"version_requirement"`     // 版本要求
	ViewCount          int       `gorm:"column:view_count;default:0" json:"view_count"`                      // 查看次数
	AdminUID           int       `gorm:"column:admin_uid;not null" json:"admin_uid"`                         // 发布管理员UID
	AdminUsername      string    `gorm:"column:admin_username;size:50;not null" json:"admin_username"`      // 发布管理员用户名
	CreatedAt          time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`                 // 创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                 // 更新时间
}

// TableName 指定表名
func (Announcement) TableName() string {
	return "bns_announcement"
}

// AnnouncementVersion 公告版本控制模型
type AnnouncementVersion struct {
	ID           int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Version      int64     `gorm:"column:version;not null;default:1" json:"version"`
	LastModified time.Time `gorm:"column:last_modified;autoUpdateTime" json:"last_modified"`
}

// TableName 指定表名
func (AnnouncementVersion) TableName() string {
	return "bns_announcement_version"
}

// IsActive 检查公告是否在有效期内
func (a *Announcement) IsActive() bool {
	now := time.Now()
	
	// 检查状态
	if a.Status != 1 { // 1-已发布
		return false
	}
	
	// 检查开始时间
	if a.StartTime != nil && a.StartTime.After(now) {
		return false
	}
	
	// 检查结束时间
	if a.EndTime != nil && a.EndTime.Before(now) {
		return false
	}
	
	return true
}

// GetTypeText 获取类型文本
func (a *Announcement) GetTypeText() string {
	switch a.Type {
	case 1:
		return "普通公告"
	case 2:
		return "重要公告"
	case 3:
		return "紧急公告"
	default:
		return "未知"
	}
}

// GetStatusText 获取状态文本
func (a *Announcement) GetStatusText() string {
	switch a.Status {
	case 0:
		return "草稿"
	case 1:
		return "已发布"
	case 2:
		return "已下线"
	default:
		return "未知"
	}
}

// AnnouncementListResponse 公告列表响应
type AnnouncementListResponse struct {
	Version       int64          `json:"version"`
	LastModified  string         `json:"last_modified"`
	Announcements []Announcement `json:"announcements"`
	Count         int            `json:"count"`
}

// AnnouncementRequest 公告请求
type AnnouncementRequest struct {
	ClientType    string `json:"client_type"`    // 客户端类型
	ClientVersion string `json:"client_version"` // 客户端版本
	CacheVersion  int64  `json:"cache_version"`  // 缓存版本
}

// AnnouncementUpdateResponse 公告更新检查响应
type AnnouncementUpdateResponse struct {
	HasUpdate      bool   `json:"has_update"`
	CurrentVersion int64  `json:"current_version"`
	LastModified   string `json:"last_modified"`
	CacheVersion   int64  `json:"cache_version"`
}

// AnnouncementStats 公告统计
type AnnouncementStats struct {
	Total     int `json:"total"`
	Normal    int `json:"normal"`
	Important int `json:"important"`
	Urgent    int `json:"urgent"`
}
