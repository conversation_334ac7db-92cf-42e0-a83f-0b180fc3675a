﻿using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class PluginSession : BaseSession
{
    #region Constructor
    readonly EventHandler<IPacket> OnReceived;
    readonly ConcurrentDictionary<EndPoint, DateTime> Clients = new();
    public readonly int Port = 8999;

    private Thread? _receiveThread;
    private volatile bool _isRunning = false;

    // 性能优化：缓存和简单池化
    private readonly SimpleBufferPool _bufferPool;
    private readonly Timer _cleanupTimer;
    private static readonly TimeSpan ClientTimeout = TimeSpan.FromMinutes(5);

    public PluginSession(EventHandler<IPacket> received)
    {
        try
        {
            OnReceived = received;

            // 初始化简单缓冲区池
            _bufferPool = new SimpleBufferPool(1024, 50);

            // 初始化清理定时器，每分钟清理一次过期客户端
            _cleanupTimer = new Timer(CleanupExpiredClients, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

            Connect();
        }
        catch (SocketException ex)
        {
            throw new Exception("监听客户端消息失败，请不要同时开启多个ACT进程。", ex);
        }
    }
    #endregion

    #region Methods
    protected override void Connect()
    {
        _socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        _socket.Bind(new IPEndPoint(IPAddress.Parse("127.0.0.1"), Port));

        // 设置Socket选项以提高性能
        _socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        _socket.ReceiveBufferSize = 64 * 1024; // 64KB接收缓冲区
        _socket.SendBufferSize = 64 * 1024;    // 64KB发送缓冲区

        _isRunning = true;
        _receiveThread = new Thread(Receive) { IsBackground = true, Name = "PluginSession-Receive" };
        _receiveThread.Start();
    }

    public void Register(nint hwnd)
    {
        if (hwnd == IntPtr.Zero) throw new Exception(StringHelper.Get("Exception_NoRunningGame"));

        // send hello message
        User32.SendMessage(hwnd, User32.WindowMessage.WM_APP, AppMessage.Register, Port);

        var startTime = DateTime.Now;
        var timeout = TimeSpan.FromMilliseconds(2000);

        // 检查是否有客户端连接到PluginSession
        while (DateTime.Now - startTime < timeout)
        {
            // 检查PluginSession是否有活跃的客户端连接
            if (!Clients.IsEmpty)
            {
                Debug.WriteLine("[DamageMeter] 通信建立成功");
                return;
            }

            // 短暂等待后再次检查
            Thread.Sleep(50);
        }

        Debug.WriteLine("[DamageMeter] 通信建立超时");
        throw new Exception(StringHelper.Get("Exception_CommunicationFailed"));
    }

    void Send(IPacket packet, EndPoint point)
    {
        _socket?.SendTo(packet.Create().ToArray(), point);
    }

    void Receive()
    {
        while (_isRunning && _socket != null)
        {
            byte[]? buffer = null;
            try
            {
                EndPoint point = new IPEndPoint(IPAddress.Any, 0);
                buffer = _bufferPool.Rent(); // 从池中租用缓冲区
                int length = _socket.ReceiveFrom(buffer, ref point);

                // 异步处理数据，避免阻塞接收线程
                var dataToProcess = new byte[length];
                Array.Copy(buffer, dataToProcess, length);

                Task.Run(() => ProcessReceivedData(point, dataToProcess));
            }
            catch (ObjectDisposedException)
            {
                // Socket已被释放，正常退出
                break;
            }
            catch (SocketException ex) when (ex.SocketErrorCode == SocketError.Interrupted)
            {
                // Socket被中断，正常退出
                break;
            }
            catch (Exception ex)
            {
                if (_isRunning) // 只在运行状态下记录错误
                {
                    Debug.WriteLine($"PluginSession Receive error: {ex.Message}");
                }
            }
            finally
            {
                // 归还缓冲区到池中
                if (buffer != null)
                {
                    _bufferPool.Return(buffer);
                }
            }
        }
        Debug.WriteLine("PluginSession Receive thread exited");
    }

    private void ProcessReceivedData(EndPoint point, byte[] data)
    {
        try
        {
            CheckAuthorize(point);

            var reader = new DataArchive(data, size: data.Length);
            var packet = Factory(reader.Read<short>());
            if (packet != null)
            {
                packet.Read(reader);
                OnReceived?.Invoke(this, packet);
            }
        }
        catch (Exception ex)
        {
            if (_isRunning)
            {
                Debug.WriteLine($"PluginSession ProcessReceivedData error: {ex.Message}");
            }
        }
    }

    void CheckAuthorize(EndPoint point)
    {
        // 检查客户端是否已授权且未过期
        if (Clients.TryGetValue(point, out var lastSeen))
        {
            if (DateTime.Now - lastSeen < ClientTimeout)
            {
                // 更新最后活跃时间
                Clients[point] = DateTime.Now;
                return;
            }
            else
            {
                // 客户端已过期，移除
                Clients.TryRemove(point, out _);
            }
        }

        try
        {
            // 添加客户端到授权列表
            Send(new AuthorizePacket(), point);
            Clients[point] = DateTime.Now;

            Debug.WriteLine($"[PluginSession] 客户端已授权: {point}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[PluginSession] 授权客户端失败: {point}, Error: {ex.Message}");
        }
    }

    /// <summary>
    /// 清理过期的客户端连接
    /// </summary>
    private void CleanupExpiredClients(object? state)
    {
        if (!_isRunning) return;

        var now = DateTime.Now;
        var expiredClients = new List<EndPoint>();

        foreach (var kvp in Clients)
        {
            if (now - kvp.Value > ClientTimeout)
            {
                expiredClients.Add(kvp.Key);
            }
        }

        foreach (var client in expiredClients)
        {
            if (Clients.TryRemove(client, out _))
            {
                Debug.WriteLine($"[PluginSession] 清理过期客户端: {client}");
            }
        }

        if (expiredClients.Count > 0)
        {
            Debug.WriteLine($"[PluginSession] 清理了 {expiredClients.Count} 个过期客户端连接");
        }
    }

    /// <summary>
    /// 释放资源并停止所有线程
    /// </summary>
    public new void Dispose()
    {
        try
        {
            Debug.WriteLine("[INFO] 开始关闭PluginSession连接");

            // 停止线程
            _isRunning = false;

            // 停止清理定时器
            _cleanupTimer?.Dispose();

            // 等待接收线程结束，减少等待时间避免程序关闭时卡顿
            if (_receiveThread != null && _receiveThread.IsAlive)
            {
                if (!_receiveThread.Join(500)) // 减少到500毫秒，避免关闭时卡顿
                {
                    Debug.WriteLine("[WARNING] 接收线程未能在超时时间内结束，继续关闭");
                }
                _receiveThread = null;
            }

            // 清理客户端列表
            Clients.Clear();

            Debug.WriteLine("[INFO] PluginSession连接关闭完成");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 关闭PluginSession连接时发生异常: {ex.Message}");
        }
        finally
        {
            // 调用基类的Dispose方法
            base.Dispose();
        }
    }

    private static IPacket? Factory(short type) => type switch
    {
        1 => new EnterWorld(),
        2 => new EnterChannel(),
        3 => new KeyInput(),
        10 => new InstantNotification(),
        11 => new InstantEffectNotification(),
        12 => new InstantEffectNotification2(),

        22 => new QueryQuote(0),
        _ => null
    };
    #endregion
}

/// <summary>
/// 简单的缓冲区池实现，用于复用字节数组
/// </summary>
internal class SimpleBufferPool
{
    private readonly ConcurrentQueue<byte[]> _buffers = new();
    private readonly int _bufferSize;
    private readonly int _maxPoolSize;
    private int _currentCount = 0;

    public SimpleBufferPool(int bufferSize, int maxPoolSize)
    {
        _bufferSize = bufferSize;
        _maxPoolSize = maxPoolSize;
    }

    public byte[] Rent()
    {
        if (_buffers.TryDequeue(out var buffer))
        {
            Interlocked.Decrement(ref _currentCount);
            return buffer;
        }

        // 池中没有可用缓冲区，创建新的
        return new byte[_bufferSize];
    }

    public void Return(byte[] buffer)
    {
        if (buffer == null || buffer.Length != _bufferSize)
            return;

        // 如果池未满，则归还缓冲区
        if (_currentCount < _maxPoolSize)
        {
            // 可选：清零缓冲区以提高安全性
            // Array.Clear(buffer, 0, buffer.Length);

            _buffers.Enqueue(buffer);
            Interlocked.Increment(ref _currentCount);
        }
        // 如果池已满，则丢弃缓冲区让GC回收
    }

    public int Count => _currentCount;
}
