package logger

import (
	"fmt"
	"time"
)

// StructuredLogger 结构化日志工具
type StructuredLogger struct{}

// NewStructuredLogger 创建结构化日志工具
func NewStructuredLogger() *StructuredLogger {
	return &StructuredLogger{}
}

// UserAction 记录用户操作日志
func (l *StructuredLogger) UserAction(action string, uid uint64, uin uint64, details map[string]interface{}) {
	detailsStr := formatDetails(details)
	Info("用户操作: %s, UID=%d, QQ=%d%s", action, uid, uin, detailsStr)
}

// AuthEvent 记录认证事件
func (l *StructuredLogger) AuthEvent(event string, uin uint64, success bool, reason string, clientIP string) {
	status := "成功"
	if !success {
		status = "失败"
	}
	
	if reason != "" {
		Info("认证事件: %s, QQ=%d, 状态=%s, 原因=%s, IP=%s", event, uin, status, reason, clientIP)
	} else {
		Info("认证事件: %s, QQ=%d, 状态=%s, IP=%s", event, uin, status, clientIP)
	}
}

// DatabaseOperation 记录数据库操作日志
func (l *StructuredLogger) DatabaseOperation(operation string, table string, affected int64, duration time.Duration, err error) {
	if err != nil {
		Error("数据库操作失败: %s, 表=%s, 耗时=%v, 错误=%v", operation, table, duration, err)
	} else {
		Debug("数据库操作: %s, 表=%s, 影响行数=%d, 耗时=%v", operation, table, affected, duration)
	}
}

// CacheOperation 记录缓存操作日志
func (l *StructuredLogger) CacheOperation(operation string, key string, hit bool, duration time.Duration, err error) {
	if err != nil {
		Error("缓存操作失败: %s, Key=%s, 耗时=%v, 错误=%v", operation, key, duration, err)
	} else {
		hitStatus := "命中"
		if !hit {
			hitStatus = "未命中"
		}
		Debug("缓存操作: %s, Key=%s, 状态=%s, 耗时=%v", operation, key, hitStatus, duration)
	}
}

// NetworkEvent 记录网络事件
func (l *StructuredLogger) NetworkEvent(event string, clientIP string, messageType string, success bool, details map[string]interface{}) {
	status := "成功"
	if !success {
		status = "失败"
	}
	
	detailsStr := formatDetails(details)
	Info("网络事件: %s, IP=%s, 消息类型=%s, 状态=%s%s", event, clientIP, messageType, status, detailsStr)
}

// ServiceEvent 记录服务事件
func (l *StructuredLogger) ServiceEvent(service string, event string, success bool, duration time.Duration, details map[string]interface{}) {
	status := "成功"
	if !success {
		status = "失败"
	}
	
	detailsStr := formatDetails(details)
	Info("服务事件: %s.%s, 状态=%s, 耗时=%v%s", service, event, status, duration, detailsStr)
}

// SecurityEvent 记录安全事件
func (l *StructuredLogger) SecurityEvent(event string, uin uint64, clientIP string, severity string, details map[string]interface{}) {
	detailsStr := formatDetails(details)
	
	switch severity {
	case "HIGH":
		Error("安全事件[高]: %s, QQ=%d, IP=%s%s", event, uin, clientIP, detailsStr)
	case "MEDIUM":
		Warn("安全事件[中]: %s, QQ=%d, IP=%s%s", event, uin, clientIP, detailsStr)
	case "LOW":
		Info("安全事件[低]: %s, QQ=%d, IP=%s%s", event, uin, clientIP, detailsStr)
	default:
		Info("安全事件: %s, QQ=%d, IP=%s%s", event, uin, clientIP, detailsStr)
	}
}

// PerformanceEvent 记录性能事件
func (l *StructuredLogger) PerformanceEvent(operation string, duration time.Duration, threshold time.Duration, details map[string]interface{}) {
	detailsStr := formatDetails(details)
	
	if duration > threshold {
		Warn("性能警告: %s, 耗时=%v, 阈值=%v%s", operation, duration, threshold, detailsStr)
	} else {
		Debug("性能监控: %s, 耗时=%v%s", operation, duration, detailsStr)
	}
}

// BusinessEvent 记录业务事件
func (l *StructuredLogger) BusinessEvent(event string, uid uint64, success bool, details map[string]interface{}) {
	status := "成功"
	if !success {
		status = "失败"
	}
	
	detailsStr := formatDetails(details)
	Info("业务事件: %s, UID=%d, 状态=%s%s", event, uid, status, detailsStr)
}

// ErrorWithContext 记录带上下文的错误
func (l *StructuredLogger) ErrorWithContext(operation string, err error, context map[string]interface{}) {
	contextStr := formatDetails(context)
	Error("操作失败: %s, 错误=%v%s", operation, err, contextStr)
}

// formatDetails 格式化详情信息
func formatDetails(details map[string]interface{}) string {
	if len(details) == 0 {
		return ""
	}
	
	var result string
	for key, value := range details {
		if result != "" {
			result += ", "
		}
		result += fmt.Sprintf("%s=%v", key, value)
	}
	
	return ", " + result
}

// 全局结构化日志实例
var Structured = NewStructuredLogger()

// 便捷函数
func LogUserAction(action string, uid uint64, uin uint64, details map[string]interface{}) {
	Structured.UserAction(action, uid, uin, details)
}

func LogAuthEvent(event string, uin uint64, success bool, reason string, clientIP string) {
	Structured.AuthEvent(event, uin, success, reason, clientIP)
}

func LogDatabaseOperation(operation string, table string, affected int64, duration time.Duration, err error) {
	Structured.DatabaseOperation(operation, table, affected, duration, err)
}

func LogCacheOperation(operation string, key string, hit bool, duration time.Duration, err error) {
	Structured.CacheOperation(operation, key, hit, duration, err)
}

func LogNetworkEvent(event string, clientIP string, messageType string, success bool, details map[string]interface{}) {
	Structured.NetworkEvent(event, clientIP, messageType, success, details)
}

func LogServiceEvent(service string, event string, success bool, duration time.Duration, details map[string]interface{}) {
	Structured.ServiceEvent(service, event, success, duration, details)
}

func LogSecurityEvent(event string, uin uint64, clientIP string, severity string, details map[string]interface{}) {
	Structured.SecurityEvent(event, uin, clientIP, severity, details)
}

func LogPerformanceEvent(operation string, duration time.Duration, threshold time.Duration, details map[string]interface{}) {
	Structured.PerformanceEvent(operation, duration, threshold, details)
}

func LogBusinessEvent(event string, uid uint64, success bool, details map[string]interface{}) {
	Structured.BusinessEvent(event, uid, success, details)
}

func LogErrorWithContext(operation string, err error, context map[string]interface{}) {
	Structured.ErrorWithContext(operation, err, context)
}
