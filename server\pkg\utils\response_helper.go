package utils

import (
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// ResponseHelper 统一的响应处理工具
type ResponseHelper struct {
	networkHandler *binary.NetworkHandler
}

// NewResponseHelper 创建响应处理工具
func NewResponseHelper() *ResponseHelper {
	return &ResponseHelper{
		networkHandler: binary.NewNetworkHandler(),
	}
}

// CreateAuthError 创建认证错误响应
func (r *ResponseHelper) CreateAuthError(message string) ([]byte, error) {
	logger.Warn("认证失败: %s", message)
	return r.networkHandler.CreateErrorResponse(binary.ErrorCodeUnauthorized, message)
}

// CreateValidationError 创建验证错误响应
func (r *ResponseHelper) CreateValidationError(message string) ([]byte, error) {
	logger.Warn("验证失败: %s", message)
	return r.networkHandler.CreateErrorResponse(binary.ErrorCodeInvalidRequest, message)
}

// CreateServerError 创建服务器错误响应
func (r *ResponseHelper) CreateServerError(message string) ([]byte, error) {
	logger.Error("服务器错误: %s", message)
	return r.networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, message)
}

// CreatePermissionError 创建权限错误响应
func (r *ResponseHelper) CreatePermissionError(message string) ([]byte, error) {
	logger.Warn("权限不足: %s", message)
	return r.networkHandler.CreateErrorResponse(binary.ErrorCodePermissionDenied, message)
}

// CreateRateLimitError 创建频率限制错误响应
func (r *ResponseHelper) CreateRateLimitError(message string) ([]byte, error) {
	logger.Warn("频率限制: %s", message)
	return r.networkHandler.CreateErrorResponse(binary.ErrorCodeRateLimit, message)
}

// CreateNotFoundError 创建资源不存在错误响应
func (r *ResponseHelper) CreateNotFoundError(message string) ([]byte, error) {
	logger.Debug("资源不存在: %s", message)
	return r.networkHandler.CreateErrorResponse(binary.ErrorCodeNotFound, message)
}

// LogAndCreateError 记录错误并创建响应
func (r *ResponseHelper) LogAndCreateError(err error, userMessage string, errorCode uint32) ([]byte, error) {
	logger.Error("操作失败: %v", err)
	return r.networkHandler.CreateErrorResponse(errorCode, userMessage)
}
