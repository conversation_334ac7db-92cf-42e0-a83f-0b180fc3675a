<?php
/**
 * 公告推送调试脚本
 * 详细测试公告推送功能的各个环节
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "=== 公告推送功能调试 ===\n\n";

// 1. 测试Redis连接
echo "1. 测试Redis连接...\n";
try {
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    $redis->select(4);
    echo "✓ Redis连接成功 (数据库: 4)\n";
} catch (Exception $e) {
    echo "✗ Redis连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 测试ThinkPHP Redis配置
echo "\n2. 测试ThinkPHP Redis配置...\n";
try {
    // 模拟ThinkPHP环境
    define('APP_PATH', __DIR__ . '/app/');
    
    // 手动加载配置
    $cacheConfig = include __DIR__ . '/config/cache.php';
    $redisConfig = $cacheConfig['stores']['redis'];
    
    echo "ThinkPHP Redis配置:\n";
    echo "  Host: " . $redisConfig['host'] . "\n";
    echo "  Port: " . $redisConfig['port'] . "\n";
    echo "  Database: " . $redisConfig['select'] . "\n";
    echo "  Password: " . ($redisConfig['password'] ? '***' : '(empty)') . "\n";
    
    // 测试连接
    $testRedis = new Redis();
    $testRedis->connect($redisConfig['host'], $redisConfig['port']);
    if ($redisConfig['password']) {
        $testRedis->auth($redisConfig['password']);
    }
    $testRedis->select($redisConfig['select']);
    echo "✓ ThinkPHP Redis配置测试成功\n";
    $testRedis->close();
    
} catch (Exception $e) {
    echo "✗ ThinkPHP Redis配置测试失败: " . $e->getMessage() . "\n";
}

// 3. 测试AnnouncementPushService类
echo "\n3. 测试AnnouncementPushService类...\n";
try {
    // 检查类文件是否存在
    $serviceFile = __DIR__ . '/app/manage/service/AnnouncementPushService.php';
    if (file_exists($serviceFile)) {
        echo "✓ AnnouncementPushService.php 文件存在\n";
        
        // 检查类是否可以加载
        require_once $serviceFile;
        if (class_exists('app\\manage\\service\\AnnouncementPushService')) {
            echo "✓ AnnouncementPushService 类可以加载\n";
        } else {
            echo "✗ AnnouncementPushService 类无法加载\n";
        }
    } else {
        echo "✗ AnnouncementPushService.php 文件不存在\n";
    }
} catch (Exception $e) {
    echo "✗ AnnouncementPushService 测试失败: " . $e->getMessage() . "\n";
}

// 4. 模拟发布公告消息
echo "\n4. 模拟发布公告消息...\n";
try {
    $testAnnouncement = [
        'id' => 12345,
        'title' => '调试测试公告',
        'type' => 1,
        'priority' => 1,
        'target_client' => 'all'
    ];
    
    // 构建消息
    $message = [
        'type' => 'ANNOUNCEMENT_PUBLISHED',
        'announcement_id' => $testAnnouncement['id'],
        'title' => $testAnnouncement['title'],
        'type_code' => $testAnnouncement['type'],
        'priority' => $testAnnouncement['priority'],
        'target_client' => $testAnnouncement['target_client'],
        'timestamp' => time(),
    ];
    
    echo "准备发布消息:\n";
    echo json_encode($message, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
    // 发布消息
    $result = $redis->publish('announcement_update', json_encode($message));
    
    if ($result > 0) {
        echo "✓ 消息发布成功，订阅者数量: $result\n";
        echo "  这表示Go服务端正在监听\n";
    } else {
        echo "⚠ 消息发布成功，但没有订阅者\n";
        echo "  可能的原因:\n";
        echo "  - Go服务端没有运行\n";
        echo "  - Go服务端没有正确订阅频道\n";
        echo "  - Redis配置不匹配\n";
    }
    
} catch (Exception $e) {
    echo "✗ 消息发布失败: " . $e->getMessage() . "\n";
}

// 5. 检查Redis订阅状态
echo "\n5. 检查Redis订阅状态...\n";
try {
    // 获取订阅信息
    $pubsubInfo = $redis->pubsub('channels', 'announcement_update');
    if (!empty($pubsubInfo)) {
        echo "✓ 频道 'announcement_update' 有活跃订阅\n";
        echo "  订阅的频道: " . implode(', ', $pubsubInfo) . "\n";
    } else {
        echo "⚠ 频道 'announcement_update' 没有活跃订阅\n";
    }
    
    // 获取所有活跃频道
    $allChannels = $redis->pubsub('channels');
    if (!empty($allChannels)) {
        echo "当前所有活跃的订阅频道: " . implode(', ', $allChannels) . "\n";
    } else {
        echo "当前没有任何活跃的订阅频道\n";
    }
    
} catch (Exception $e) {
    echo "✗ 检查订阅状态失败: " . $e->getMessage() . "\n";
}

// 6. 检查Go服务配置
echo "\n6. 检查Go服务配置...\n";
$goConfigFile = __DIR__ . '/../config/config.yaml';
if (file_exists($goConfigFile)) {
    echo "✓ Go服务配置文件存在: $goConfigFile\n";
    $goConfig = file_get_contents($goConfigFile);
    
    // 提取Redis配置
    if (preg_match('/redis:\s*\n\s*host:\s*(\S+)\s*\n\s*port:\s*(\d+)\s*\n\s*password:\s*"?([^"]*)"?\s*\n\s*db:\s*(\d+)/m', $goConfig, $matches)) {
        echo "Go服务Redis配置:\n";
        echo "  Host: " . $matches[1] . "\n";
        echo "  Port: " . $matches[2] . "\n";
        echo "  Password: " . ($matches[3] ? '***' : '(empty)') . "\n";
        echo "  Database: " . $matches[4] . "\n";
        
        // 检查配置是否匹配
        if ($matches[4] == '4') {
            echo "✓ Go服务和PHP使用相同的Redis数据库\n";
        } else {
            echo "✗ Go服务和PHP使用不同的Redis数据库！\n";
            echo "  Go: " . $matches[4] . ", PHP: 4\n";
        }
    } else {
        echo "⚠ 无法解析Go服务Redis配置\n";
    }
} else {
    echo "✗ Go服务配置文件不存在: $goConfigFile\n";
}

$redis->close();

echo "\n=== 调试完成 ===\n";
echo "\n建议检查项目:\n";
echo "1. 确保Go服务端正在运行\n";
echo "2. 检查Go服务端日志输出\n";
echo "3. 确认Redis配置一致\n";
echo "4. 在PHP后台发布公告时检查日志\n";
?>
