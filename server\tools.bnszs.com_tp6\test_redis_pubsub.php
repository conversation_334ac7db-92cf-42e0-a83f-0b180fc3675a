<?php
/**
 * Redis Pub/Sub 测试脚本
 * 用于测试PHP发布消息和Go服务端订阅是否正常工作
 */

require_once __DIR__ . '/vendor/autoload.php';

// 直接使用Redis扩展进行测试
try {
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    $redis->select(4); // 使用数据库4
    
    echo "Redis连接成功！\n";
    echo "当前Redis数据库: " . $redis->getDbNum() . "\n";
    
    // 测试消息
    $testMessage = [
        'type' => 'ANNOUNCEMENT_PUBLISHED',
        'announcement_id' => 9999,
        'title' => 'Redis Pub/Sub 测试公告',
        'type_code' => 1,
        'priority' => 1,
        'target_client' => 'all',
        'timestamp' => time(),
    ];
    
    echo "\n准备发布测试消息到频道: announcement_update\n";
    echo "消息内容: " . json_encode($testMessage, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 发布消息
    $result = $redis->publish('announcement_update', json_encode($testMessage));
    
    echo "\n发布结果:\n";
    if ($result > 0) {
        echo "✓ 消息发送成功！订阅者数量: $result\n";
        echo "这意味着Go服务端正在监听此频道\n";
    } else {
        echo "⚠ 消息发送成功，但没有订阅者\n";
        echo "这可能意味着:\n";
        echo "  1. Go服务端没有运行\n";
        echo "  2. Go服务端没有订阅此频道\n";
        echo "  3. Go服务端使用了不同的Redis数据库\n";
    }
    
    // 测试Redis基本功能
    echo "\n测试Redis基本功能:\n";
    $testKey = 'test_pubsub_' . time();
    $redis->set($testKey, 'test_value', 10);
    $value = $redis->get($testKey);
    
    if ($value === 'test_value') {
        echo "✓ Redis读写功能正常\n";
    } else {
        echo "✗ Redis读写功能异常\n";
    }
    
    $redis->del($testKey);
    
    // 检查Redis信息
    echo "\nRedis服务器信息:\n";
    $info = $redis->info();
    echo "Redis版本: " . ($info['redis_version'] ?? 'unknown') . "\n";
    echo "连接的客户端数: " . ($info['connected_clients'] ?? 'unknown') . "\n";
    
    // 检查当前数据库的键数量
    $dbsize = $redis->dbSize();
    echo "当前数据库键数量: $dbsize\n";
    
    $redis->close();
    
} catch (Exception $e) {
    echo "Redis连接失败: " . $e->getMessage() . "\n";
    echo "请检查:\n";
    echo "  1. Redis服务是否运行\n";
    echo "  2. Redis配置是否正确\n";
    echo "  3. PHP Redis扩展是否安装\n";
}

echo "\n测试完成！\n";
echo "如果Go服务端正在运行，请检查其日志输出以确认是否收到消息。\n";
?>
