package service

import (
	"fmt"
	"log"
	"time"
	"udp-server/server/internal/config"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// PermissionService 权限服务
type PermissionService struct {
	db    *gorm.DB
	cache cache.Cache
}

// NewPermissionService 创建新的权限服务
func NewPermissionService(db *gorm.DB, cache cache.Cache) *PermissionService {
	return &PermissionService{
		db:    db,
		cache: cache,
	}
}

// CDKey信息结构
type CDKeyInfo struct {
	CDKey    string     `gorm:"column:cdkey"`
	Type     string     `gorm:"column:type"`
	TimeType string     `gorm:"column:timeType"`
	Fixed    *time.Time `gorm:"column:fixed"`
	Duration int        `gorm:"column:duration"`
}

// 获取用户权限过期时间
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
// 返回值：过期时间戳（0=无权限，-1=永久权限，>0=具体过期时间）
func (s *PermissionService) GetExpiration(uid uint64, permissionType string) (int64, error) {
	// 首先检查用户数据库中的权限字段
	var user model.User
	if err := s.db.Where("uid = ?", uid).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Debug("用户不存在: UID=%d", uid)
			return 0, nil
		}
		logger.Error("查询用户信息失败: UID=%d, Error=%v", uid, err)
		return 0, err
	}

	// 如果用户权限字段大于0，直接返回永久权限
	if user.Permission > 0 {
		logger.Debug("用户有数据库权限，返回永久权限: UID=%d, Permission=%d", uid, user.Permission)
		return -1, nil
	}

	// 如果用户权限字段等于0，优先计算CDKey权限，然后考虑活动权限
	logger.Debug("用户无数据库权限，开始动态计算: UID=%d", uid)

	// 使用哈希表存储用户权限过期时间
	hashKey := fmt.Sprintf("user_expir_%s", permissionType)
	fieldKey := fmt.Sprintf("%d", uid)

	// 先检查缓存
	var cachedTime int64
	if err := s.getFromHash(hashKey, fieldKey, &cachedTime); err == nil {
		log.Printf("[DEBUG] 从哈希表缓存获取权限过期时间: UID=%d, Type=%s, Time=%d", uid, permissionType, cachedTime)
		return cachedTime, nil
	}

	log.Printf("[DEBUG] 哈希表缓存未命中，开始计算权限过期时间: UID=%d, Type=%s", uid, permissionType)

	// 缓存未命中，从数据库计算
	var calculatedTime int64 = 0
	var logs []model.UserLog
	if err := s.db.Where("uid = ? AND type = ?", uid, "cdkey").Order("id").Find(&logs).Error; err != nil {
		log.Printf("[ERROR] 查询用户CDKey日志失败: UID=%d, Error=%v", uid, err)
		return 0, err
	}

	log.Printf("[DEBUG] 找到用户CDKey记录数量: UID=%d, Count=%d", uid, len(logs))

	// 遍历每条CDKey记录
	for _, logRecord := range logs {
		// 计算起始时间戳，支持多种时间格式
		startTime, err := s.parseTimeString(logRecord.Time)
		if err != nil {
			log.Printf("[WARN] 解析时间失败: %s, Error=%v", logRecord.Time, err)
			continue
		}
		startTimestamp := startTime.Unix()

		// 如果当前计算的时间大于起始时间，则使用当前时间作为起始时间
		if calculatedTime > startTimestamp {
			startTimestamp = calculatedTime
		}

		// 查询CDKey配置信息
		var cdkeyInfo CDKeyInfo
		query := `
			SELECT c.cdkey, c.type, t.timeType, t.fixed, t.duration 
			FROM bns_cdkey c 
			JOIN bns_cdkey_customize t ON t.cdkey = c.cdkey 
			WHERE c.type = ? AND c.cdkey = ?
		`

		if err := s.db.Raw(query, permissionType, logRecord.Extra).Scan(&cdkeyInfo).Error; err != nil {
			log.Printf("[WARN] 查询CDKey配置失败: CDKey=%s, Error=%v", logRecord.Extra, err)
			continue
		}

		log.Printf("[DEBUG] CDKey配置: CDKey=%s, TimeType=%s, Duration=%d",
			cdkeyInfo.CDKey, cdkeyInfo.TimeType, cdkeyInfo.Duration)

		// 根据时间类型计算过期时间
		switch cdkeyInfo.TimeType {
		case "duration":
			// 持续时间类型：起始时间 + 天数
			newTime := startTimestamp + int64(cdkeyInfo.Duration*24*3600)
			if newTime > calculatedTime {
				calculatedTime = newTime
			}

		case "fixed":
			// 固定时间类型
			if cdkeyInfo.Fixed == nil {
				// 固定时间为空，表示永久权限
				calculatedTime = -1
				log.Printf("[DEBUG] Fixed时间为空，设置为永久权限: CDKey=%s", cdkeyInfo.CDKey)
				break // 跳出循环，永久权限优先级最高
			} else {
				// 取最大的固定时间
				fixedTime := cdkeyInfo.Fixed.Unix()
				if fixedTime > calculatedTime {
					calculatedTime = fixedTime
				}
			}
		}
	}

	log.Printf("[DEBUG] CDKey权限计算结果: UID=%d, Type=%s, Time=%d", uid, permissionType, calculatedTime)

	// 检查活动权限并取最大值
	activityConfig := config.GetGlobalActivityConfig()
	var finalTime int64 = calculatedTime
	var useActivityPermission bool

	// 如果活动正在进行中，获取活动结束时间
	if activityConfig.IsActive() {
		activityEndTime := activityConfig.EndTime.Unix()

		// 取CDKey权限时间和活动结束时间的最大值
		if activityEndTime > finalTime {
			finalTime = activityEndTime
			useActivityPermission = true
			log.Printf("[DEBUG] 活动权限更长，使用活动权限: UID=%d, CDKeyTime=%d, ActivityEndTime=%d", uid, calculatedTime, activityEndTime)
		} else {
			log.Printf("[DEBUG] CDKey权限更长或相等，使用CDKey权限: UID=%d, CDKeyTime=%d, ActivityEndTime=%d", uid, calculatedTime, activityEndTime)
		}
	} else {
		log.Printf("[DEBUG] 活动未进行，仅使用CDKey权限: UID=%d, CDKeyTime=%d", uid, calculatedTime)
	}

	calculatedTime = finalTime

	log.Printf("[DEBUG] 最终权限过期时间: UID=%d, Type=%s, Time=%d", uid, permissionType, calculatedTime)

	// 根据权限状态设置不同的缓存时间
	var cacheDuration time.Duration
	if calculatedTime == -1 {
		// 永久权限，缓存24小时
		cacheDuration = 24 * time.Hour
	} else if calculatedTime == 0 {
		// 无权限时，需要考虑活动状态来设置缓存时间
		if activityConfig.IsActive() {
			// 活动进行中但用户无权限，使用短缓存（1小时），以便活动状态变化时能及时更新
			cacheDuration = 1 * time.Hour
			log.Printf("[DEBUG] 无权限但活动进行中，设置短缓存时间: UID=%d, Duration=%v", uid, cacheDuration)
		} else {
			// 活动未进行且无权限，缓存15天（避免频繁查询，CDKey插入时会清理缓存）
			cacheDuration = 15 * 24 * time.Hour
		}
	} else {
		// 有期限权限，根据是否使用活动权限设置不同的缓存时间
		if useActivityPermission {
			// 使用活动权限时，缓存时间较短（1小时），确保活动结束后能及时更新
			cacheDuration = 1 * time.Hour
			log.Printf("[DEBUG] 使用活动权限，设置短缓存时间: UID=%d, Duration=%v", uid, cacheDuration)
		} else {
			// 仅CDKey权限，但需要考虑活动状态
			if activityConfig.IsActive() {
				// 活动进行中，使用短缓存（1小时），以防权限状态变化
				cacheDuration = 1 * time.Hour
				log.Printf("[DEBUG] CDKey权限且活动进行中，设置短缓存时间: UID=%d, Duration=%v", uid, cacheDuration)
			} else {
				// 活动未进行，CDKey权限缓存15天（CDKey插入时会清理缓存）
				cacheDuration = 15 * 24 * time.Hour
			}
		}
	}

	// 将结果存入哈希表缓存
	if err := s.setToHash(hashKey, fieldKey, calculatedTime, cacheDuration); err != nil {
		log.Printf("[WARN] 缓存权限过期时间到哈希表失败: %v", err)
	} else {
		log.Printf("[DEBUG] 权限过期时间已缓存到哈希表: UID=%d, Type=%s, Duration=%v", uid, permissionType, cacheDuration)
	}

	return calculatedTime, nil
}

// GetUserPermissionExpiration 获取用户权限过期时间
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
// 返回值：过期时间戳（0=无权限，-1=永久权限，>0=具体过期时间）
func (s *PermissionService) GetUserPermissionExpiration(uid uint64, permissionType string) (int64, error) {
	// 首先检查用户数据库中的权限字段
	var user model.User
	if err := s.db.Where("uid = ?", uid).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Printf("[DEBUG] 用户不存在: UID=%d", uid)
			return 0, nil
		}
		log.Printf("[ERROR] 查询用户信息失败: UID=%d, Error=%v", uid, err)
		return 0, err
	}

	// 如果用户权限字段大于0，直接返回永久权限
	if user.Permission > 0 {
		log.Printf("[DEBUG] 用户有数据库权限，返回永久权限: UID=%d, Permission=%d", uid, user.Permission)
		return -1, nil
	}

	// 如果用户权限字段等于0，通过GetExpiration方法计算动态权限
	log.Printf("[DEBUG] 用户无数据库权限，通过动态计算获取权限: UID=%d", uid)
	return s.GetExpiration(uid, permissionType)
}

// 获取用户权限级别
func (s *PermissionService) GetPermissionLevel(uid uint64, permissionType string) (int, error) {
	// 先获取权限过期时间（包含CDKey权限和活动权限的综合计算）
	expirationTime, err := s.GetExpiration(uid, permissionType)
	if err != nil {
		return 0, err
	}

	// 权限判断逻辑：
	// expirationTime == 0: 无权限
	// expirationTime == -1: 永久权限
	// expirationTime > 0: 有期限权限，需要检查是否过期
	now := time.Now().Unix()

	if expirationTime == 0 {
		log.Printf("[DEBUG] 用户无权限: UID=%d", uid)
		return 0, nil
	}

	if expirationTime == -1 {
		log.Printf("[DEBUG] 用户有永久权限: UID=%d", uid)
		return 1, nil
	}

	if expirationTime <= now {
		log.Printf("[DEBUG] 用户权限已过期: UID=%d, ExpirationTime=%d, Now=%d", uid, expirationTime, now)
		return 0, nil
	}

	log.Printf("[DEBUG] 用户有有效权限: UID=%d, ExpirationTime=%d, Now=%d", uid, expirationTime, now)
	return 1, nil
}

// isInFreeTrialPeriod 检查是否在免费体验期间
func (s *PermissionService) isInFreeTrialPeriod() bool {
	activityConfig := config.GetGlobalActivityConfig()
	return activityConfig.IsActive()
}

// GetActivityInfoForUser 获取特定用户的活动信息
func (s *PermissionService) GetActivityInfoForUser(userPermission uint8) map[string]interface{} {
	now := time.Now()
	activityConfig := config.GetGlobalActivityConfig()
	isActive := activityConfig.IsActive()

	result := map[string]interface{}{
		"is_active":           isActive,
		"start_time":          activityConfig.StartTime.Unix(),
		"end_time":            activityConfig.EndTime.Unix(),
		"sign_in_resume_time": activityConfig.SignInResumeTime.Unix(),
		"title":               activityConfig.Title,
		"description":         activityConfig.Description,
	}

	// 高级用户和会员用户（userPermission > 0）不显示活动剩余时间
	if userPermission > 0 {
		result["message"] = "您拥有永久权限"
		return result
	}

	if isActive {
		// 计算剩余时间（仅对普通用户）
		remaining := activityConfig.EndTime.Sub(now)
		result["remaining_days"] = int(remaining.Hours() / 24)
		result["remaining_hours"] = int(remaining.Hours()) % 24
		result["message"] = fmt.Sprintf("免费体验还剩 %d 天 %d 小时",
			int(remaining.Hours()/24), int(remaining.Hours())%24)
	} else if now.Before(activityConfig.StartTime) {
		result["message"] = "活动尚未开始"
	} else {
		result["message"] = "活动已结束，已恢复签到获得权限方式"
	}

	return result
}

// ClearUserPermissionCache 清除用户权限缓存（CDKey激活时调用）
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
func (s *PermissionService) ClearUserPermissionCache(uid uint64, permissionType string) error {
	hashKey := fmt.Sprintf("user_expir_%s", permissionType)
	fieldKey := fmt.Sprintf("%d", uid)

	if err := s.deleteFromHash(hashKey, fieldKey); err != nil {
		log.Printf("[ERROR] 清除用户权限哈希表缓存失败: UID=%d, Type=%s, Error=%v", uid, permissionType, err)
		return err
	}

	log.Printf("[DEBUG] 用户权限哈希表缓存已清除: UID=%d, Type=%s", uid, permissionType)
	return nil
}

// getFromHash 从哈希表获取值
func (s *PermissionService) getFromHash(hashKey, fieldKey string, value interface{}) error {
	// 由于当前的cache接口不支持哈希操作，我们使用组合键的方式模拟
	compositeKey := fmt.Sprintf("%s:%s", hashKey, fieldKey)
	return s.cache.Get(compositeKey, value)
}

// setToHash 设置值到哈希表
func (s *PermissionService) setToHash(hashKey, fieldKey string, value interface{}, expiration time.Duration) error {
	// 由于当前的cache接口不支持哈希操作，我们使用组合键的方式模拟
	compositeKey := fmt.Sprintf("%s:%s", hashKey, fieldKey)
	return s.cache.Set(compositeKey, value, expiration)
}

// deleteFromHash 从哈希表删除字段
func (s *PermissionService) deleteFromHash(hashKey, fieldKey string) error {
	// 由于当前的cache接口不支持哈希操作，我们使用组合键的方式模拟
	compositeKey := fmt.Sprintf("%s:%s", hashKey, fieldKey)
	return s.cache.Delete(compositeKey)
}

// parseTimeString 解析时间字符串，支持多种格式
func (s *PermissionService) parseTimeString(timeStr string) (time.Time, error) {
	// 支持的时间格式列表
	timeFormats := []string{
		"2006-01-02T15:04:05Z07:00", // ISO 8601 with timezone: 2025-02-16T22:49:22+08:00
		"2006-01-02T15:04:05Z",      // ISO 8601 UTC: 2025-02-16T22:49:22Z
		"2006-01-02T15:04:05",       // ISO 8601 without timezone: 2025-02-16T22:49:22
		"2006-01-02 15:04:05",       // MySQL format: 2025-02-16 22:49:22
		"2006-01-02",                // Date only: 2025-02-16
		time.RFC3339,                // RFC3339: 2025-02-16T22:49:22Z
		time.RFC3339Nano,            // RFC3339 with nanoseconds
	}

	// 尝试每种格式
	for _, format := range timeFormats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		}
	}

	// 如果所有格式都失败，返回错误
	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}
