package database

import (
	"context"
	"fmt"
	"time"

	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// QueryOptimizer 数据库查询优化器
type QueryOptimizer struct {
	db *gorm.DB
}

// NewQueryOptimizer 创建查询优化器
func NewQueryOptimizer(db *gorm.DB) *QueryOptimizer {
	return &QueryOptimizer{db: db}
}

// BatchQuery 批量查询优化
type BatchQuery struct {
	optimizer *QueryOptimizer
	queries   []QueryItem
}

// QueryItem 查询项
type QueryItem struct {
	Query  string
	Args   []interface{}
	Result interface{}
}

// NewBatchQuery 创建批量查询
func (o *QueryOptimizer) NewBatchQuery() *BatchQuery {
	return &BatchQuery{
		optimizer: o,
		queries:   make([]QueryItem, 0),
	}
}

// Add 添加查询
func (b *BatchQuery) Add(query string, args []interface{}, result interface{}) *BatchQuery {
	b.queries = append(b.queries, QueryItem{
		Query:  query,
		Args:   args,
		Result: result,
	})
	return b
}

// Execute 执行批量查询
func (b *BatchQuery) Execute() error {
	if len(b.queries) == 0 {
		return nil
	}

	start := time.Now()
	defer func() {
		duration := time.Since(start)
		logger.LogPerformanceEvent("batch_query", duration, 100*time.Millisecond, map[string]interface{}{
			"query_count": len(b.queries),
		})
	}()

	tx := b.optimizer.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for i, item := range b.queries {
		if err := tx.Raw(item.Query, item.Args...).Scan(item.Result).Error; err != nil {
			tx.Rollback()
			logger.LogErrorWithContext("batch_query_failed", err, map[string]interface{}{
				"query_index": i,
				"query":       item.Query,
			})
			return err
		}
	}

	return tx.Commit().Error
}

// OptimizedFind 优化的查找方法
func (o *QueryOptimizer) OptimizedFind(model interface{}, conditions map[string]interface{}, limit int, offset int) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		logger.LogDatabaseOperation("optimized_find", fmt.Sprintf("%T", model), 0, duration, nil)
	}()

	query := o.db.Model(model)
	
	// 添加条件
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}
	
	// 添加分页
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	return query.Find(model).Error
}

// OptimizedCount 优化的计数方法
func (o *QueryOptimizer) OptimizedCount(model interface{}, conditions map[string]interface{}) (int64, error) {
	start := time.Now()
	var count int64
	
	query := o.db.Model(model)
	
	// 添加条件
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}
	
	err := query.Count(&count).Error
	duration := time.Since(start)
	
	logger.LogDatabaseOperation("optimized_count", fmt.Sprintf("%T", model), count, duration, err)
	
	return count, err
}

// BulkInsert 批量插入优化
func (o *QueryOptimizer) BulkInsert(models interface{}, batchSize int) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		logger.LogPerformanceEvent("bulk_insert", duration, 1*time.Second, map[string]interface{}{
			"batch_size": batchSize,
		})
	}()

	return o.db.CreateInBatches(models, batchSize).Error
}

// BulkUpdate 批量更新优化
func (o *QueryOptimizer) BulkUpdate(model interface{}, conditions map[string]interface{}, updates map[string]interface{}) (int64, error) {
	start := time.Now()
	
	query := o.db.Model(model)
	
	// 添加条件
	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}
	
	result := query.Updates(updates)
	duration := time.Since(start)
	
	logger.LogDatabaseOperation("bulk_update", fmt.Sprintf("%T", model), result.RowsAffected, duration, result.Error)
	
	return result.RowsAffected, result.Error
}

// WithTimeout 带超时的查询
func (o *QueryOptimizer) WithTimeout(timeout time.Duration) *QueryOptimizer {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	return &QueryOptimizer{
		db: o.db.WithContext(ctx),
	}
}

// CacheableQuery 可缓存的查询
type CacheableQuery struct {
	optimizer *QueryOptimizer
	cacheKey  string
	ttl       time.Duration
}

// NewCacheableQuery 创建可缓存查询
func (o *QueryOptimizer) NewCacheableQuery(cacheKey string, ttl time.Duration) *CacheableQuery {
	return &CacheableQuery{
		optimizer: o,
		cacheKey:  cacheKey,
		ttl:       ttl,
	}
}

// Execute 执行可缓存查询
func (c *CacheableQuery) Execute(query func() (interface{}, error)) (interface{}, error) {
	// TODO: 实现缓存逻辑
	// 这里可以集成Redis缓存
	return query()
}

// QueryStats 查询统计
type QueryStats struct {
	TotalQueries    int64         `json:"total_queries"`
	AverageTime     time.Duration `json:"average_time"`
	SlowQueries     int64         `json:"slow_queries"`
	FailedQueries   int64         `json:"failed_queries"`
	LastSlowQuery   string        `json:"last_slow_query"`
	LastSlowTime    time.Duration `json:"last_slow_time"`
}

// GetQueryStats 获取查询统计
func (o *QueryOptimizer) GetQueryStats() *QueryStats {
	// TODO: 实现查询统计收集
	return &QueryStats{}
}

// OptimizeIndexes 索引优化建议
func (o *QueryOptimizer) OptimizeIndexes(tableName string) ([]string, error) {
	var suggestions []string
	
	// 分析表的查询模式
	var slowQueries []struct {
		Query string
		Time  float64
	}
	
	// 查询慢查询日志（如果启用）
	err := o.db.Raw(`
		SELECT sql_text as query, mean_timer_wait/1000000000 as time 
		FROM performance_schema.events_statements_summary_by_digest 
		WHERE schema_name = DATABASE() 
		AND mean_timer_wait > 1000000000 
		ORDER BY mean_timer_wait DESC 
		LIMIT 10
	`).Scan(&slowQueries).Error
	
	if err != nil {
		logger.Warn("无法获取慢查询信息: %v", err)
		return suggestions, nil
	}
	
	// 分析并生成索引建议
	for _, query := range slowQueries {
		if suggestion := analyzeQueryForIndex(query.Query, tableName); suggestion != "" {
			suggestions = append(suggestions, suggestion)
		}
	}
	
	return suggestions, nil
}

// analyzeQueryForIndex 分析查询并生成索引建议
func analyzeQueryForIndex(query string, tableName string) string {
	// 简单的索引分析逻辑
	// 实际实现中可以使用更复杂的SQL解析
	
	// 这里只是示例，实际需要更复杂的分析
	if len(query) > 100 {
		return fmt.Sprintf("考虑为表 %s 添加复合索引", tableName)
	}
	
	return ""
}

// ConnectionPool 连接池优化
type ConnectionPool struct {
	db *gorm.DB
}

// NewConnectionPool 创建连接池
func NewConnectionPool(db *gorm.DB) *ConnectionPool {
	return &ConnectionPool{db: db}
}

// OptimizePool 优化连接池设置
func (p *ConnectionPool) OptimizePool(maxOpenConns, maxIdleConns int, connMaxLifetime time.Duration) error {
	sqlDB, err := p.db.DB()
	if err != nil {
		return err
	}
	
	sqlDB.SetMaxOpenConns(maxOpenConns)
	sqlDB.SetMaxIdleConns(maxIdleConns)
	sqlDB.SetConnMaxLifetime(connMaxLifetime)
	
	logger.Info("数据库连接池已优化: MaxOpen=%d, MaxIdle=%d, MaxLifetime=%v", 
		maxOpenConns, maxIdleConns, connMaxLifetime)
	
	return nil
}

// GetPoolStats 获取连接池统计
func (p *ConnectionPool) GetPoolStats() map[string]interface{} {
	sqlDB, err := p.db.DB()
	if err != nil {
		return nil
	}
	
	stats := sqlDB.Stats()
	return map[string]interface{}{
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration,
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}
}
