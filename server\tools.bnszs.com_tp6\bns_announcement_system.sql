-- ========================================
-- 公告系统数据库表创建脚本
-- ========================================
-- 执行方式：mysql -u username -p database_name < bns_announcement_system.sql

-- 创建公告表
CREATE TABLE IF NOT EXISTS `bns_announcement` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(200) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '公告类型：1-普通公告，2-重要公告，3-紧急公告',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-草稿，1-已发布，2-已下线',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始显示时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束显示时间',
  `target_client` varchar(50) DEFAULT 'all' COMMENT '目标客户端：all-全部，desktop-桌面端，mobile-移动端',
  `version_requirement` varchar(50) DEFAULT NULL COMMENT '版本要求，如>=1.0.0',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '查看次数',
  `admin_uid` int(11) NOT NULL COMMENT '发布管理员UID',
  `admin_username` varchar(50) NOT NULL COMMENT '发布管理员用户名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_priority` (`priority` DESC),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at` DESC),
  KEY `idx_updated_at` (`updated_at` DESC),
  KEY `idx_admin` (`admin_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告表';

-- 创建公告缓存版本表（用于客户端缓存控制）
CREATE TABLE IF NOT EXISTS `bns_announcement_version` (
  `id` int(11) NOT NULL DEFAULT 1 COMMENT '固定ID为1',
  `version` bigint(20) NOT NULL DEFAULT 1 COMMENT '版本号',
  `last_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告版本控制表';

-- 插入初始版本记录
INSERT INTO `bns_announcement_version` (`id`, `version`, `last_modified`) 
VALUES (1, 1, CURRENT_TIMESTAMP) 
ON DUPLICATE KEY UPDATE `version` = `version`;

-- 创建触发器：当公告表有变更时自动更新版本号
DELIMITER $$

CREATE TRIGGER `tr_announcement_version_insert` 
AFTER INSERT ON `bns_announcement`
FOR EACH ROW
BEGIN
    UPDATE `bns_announcement_version` 
    SET `version` = `version` + 1, `last_modified` = CURRENT_TIMESTAMP 
    WHERE `id` = 1;
END$$

CREATE TRIGGER `tr_announcement_version_update` 
AFTER UPDATE ON `bns_announcement`
FOR EACH ROW
BEGIN
    UPDATE `bns_announcement_version` 
    SET `version` = `version` + 1, `last_modified` = CURRENT_TIMESTAMP 
    WHERE `id` = 1;
END$$

CREATE TRIGGER `tr_announcement_version_delete` 
AFTER DELETE ON `bns_announcement`
FOR EACH ROW
BEGIN
    UPDATE `bns_announcement_version` 
    SET `version` = `version` + 1, `last_modified` = CURRENT_TIMESTAMP 
    WHERE `id` = 1;
END$$

DELIMITER ;

-- 插入示例数据
INSERT INTO `bns_announcement` (
    `title`, `content`, `type`, `status`, `priority`, 
    `start_time`, `end_time`, `target_client`, 
    `admin_uid`, `admin_username`
) VALUES 
(
    '欢迎使用剑灵小助手', 
    '感谢您使用剑灵小助手！本工具为您提供便捷的游戏辅助功能。如有问题请联系客服。', 
    1, 1, 100, 
    CURRENT_TIMESTAMP, 
    DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 30 DAY), 
    'all', 
    1, 'system'
),
(
    '系统维护通知', 
    '系统将于今晚23:00-24:00进行维护升级，期间可能影响部分功能使用，请提前做好准备。', 
    2, 1, 200, 
    CURRENT_TIMESTAMP, 
    DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 1 DAY), 
    'all', 
    1, 'system'
);

-- 验证表创建
SELECT 'bns_announcement表创建完成' as message;
DESCRIBE bns_announcement;

SELECT 'bns_announcement_version表创建完成' as message;
DESCRIBE bns_announcement_version;

-- 查看示例数据
SELECT 'bns_announcement示例数据' as message;
SELECT id, title, type, status, priority, created_at FROM bns_announcement;

SELECT 'bns_announcement_version当前版本' as message;
SELECT * FROM bns_announcement_version;
