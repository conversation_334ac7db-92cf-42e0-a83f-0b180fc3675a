<!DOCTYPE html>
<html>
<head>
    <title>更新配置测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        .button:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-left: 4px solid #007cba; }
        .error { border-left-color: #d32f2f; }
        .success { border-left-color: #388e3c; }
        pre { background: #f8f8f8; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>更新配置测试页面</h1>
    
    <div class="section">
        <h2>1. 创建测试数据</h2>
        <p>创建bns-helper应用的测试配置和群组数据</p>
        <button class="button" onclick="createTestData()">创建测试数据</button>
        <div id="createResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="section">
        <h2>2. 测试Redis缓存</h2>
        <p>检查Go服务端是否已将配置写入Redis缓存</p>
        <button class="button" onclick="testRedisCache()">测试Redis缓存</button>
        <div id="cacheResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="section">
        <h2>3. 清除缓存</h2>
        <p>清除Redis中的更新配置缓存</p>
        <button class="button" onclick="clearCache()">清除缓存</button>
        <div id="clearResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="section">
        <h2>4. 模拟PHP Update接口</h2>
        <p>模拟原有的PHP Update接口调用，测试从Redis获取配置</p>
        <button class="button" onclick="simulateUpdate()">模拟Update接口</button>
        <div id="simulateResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="section">
        <h2>5. 测试说明</h2>
        <ul>
            <li>首先点击"创建测试数据"创建数据库中的配置</li>
            <li>Go服务端启动后会自动将配置加载到Redis缓存</li>
            <li>点击"测试Redis缓存"检查缓存是否存在</li>
            <li>点击"模拟Update接口"测试PHP从Redis读取配置</li>
            <li>可以通过"清除缓存"来测试缓存失效后的后备机制</li>
        </ul>
    </div>

    <script>
        function createTestData() {
            showLoading('createResult');
            fetch('/manage/update-test/create-test-data', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showResult('createResult', data);
            })
            .catch(error => {
                showError('createResult', '请求失败: ' + error.message);
            });
        }
        
        function testRedisCache() {
            showLoading('cacheResult');
            fetch('/manage/update-test/test-redis-cache')
            .then(response => response.json())
            .then(data => {
                showResult('cacheResult', data);
            })
            .catch(error => {
                showError('cacheResult', '请求失败: ' + error.message);
            });
        }
        
        function clearCache() {
            showLoading('clearResult');
            fetch('/manage/update-test/clear-cache', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showResult('clearResult', data);
            })
            .catch(error => {
                showError('clearResult', '请求失败: ' + error.message);
            });
        }
        
        function simulateUpdate() {
            showLoading('simulateResult');
            fetch('/manage/update-test/simulate-update?app=bns-helper&version=3.1.0')
            .then(response => response.json())
            .then(data => {
                showResult('simulateResult', data);
            })
            .catch(error => {
                showError('simulateResult', '请求失败: ' + error.message);
            });
        }
        
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result';
            element.innerHTML = '加载中...';
        }
        
        function showResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (data.code === 1 ? 'success' : 'error');
            element.innerHTML = '<strong>' + data.msg + '</strong><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
        
        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result error';
            element.innerHTML = '<strong>错误:</strong> ' + message;
        }
    </script>
</body>
</html>
