using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using System.Diagnostics;
using System.Windows;
using System.Windows.Media;
using System.Windows.Threading;

namespace Xylia.BnsHelper.ViewModels.Dialogs;
internal partial class MessageDialogViewModel : ObservableObject, IDialogResultable<MessageBoxResult>
{
    #region Fields
    private DispatcherTimer? _autoCloseTimer;
    private DateTime _startTime;
    #endregion

    #region Properties
    [ObservableProperty] private string? _message;
    [ObservableProperty] private MessageBoxButton _buttonType = MessageBoxButton.OK;
    [ObservableProperty] private MessageBoxImage _icon = MessageBoxImage.Information;
    [ObservableProperty] private int _autoCloseMilliseconds = 0;
    [ObservableProperty] private string _okText = "确定";

    // Icon properties for binding
    [ObservableProperty] private Geometry? _iconGeometry;
    [ObservableProperty] private Brush? _iconBrush;

    // Button visibility properties
    [ObservableProperty] private bool _showOkButton;
    [ObservableProperty] private bool _showCancelButton;
    [ObservableProperty] private bool _showYesButton;
    [ObservableProperty] private bool _showNoButton;

    public MessageBoxResult Result { get; set; }
    public Action? CloseAction { get; set; }
    #endregion

    #region Constructor
    public MessageDialogViewModel()
    {
        UpdateButtonVisibility();
        UpdateAutoCloseTimer();
        // 立即初始化图标，不等待Loaded事件
        UpdateIcon();
    }
    #endregion

    #region Commands
    [RelayCommand]
    private void Ok()
    {
        StopTimer();
        Result = MessageBoxResult.OK;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void Cancel()
    {
        StopTimer();
        Result = MessageBoxResult.Cancel;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void Yes()
    {
        StopTimer();
        Result = MessageBoxResult.Yes;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void No()
    {
        StopTimer();
        Result = MessageBoxResult.No;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    public void Close()
    {
        StopTimer();
        Result = MessageBoxResult.Cancel;
        CloseAction?.Invoke();
    }
    #endregion

    #region Methods
    public void Initialize()
    {
        UpdateButtonVisibility();
        UpdateAutoCloseTimer();
    }

    public void UpdateIcon()
    {
        try
        {
            // 如果Application.Current为null，使用默认图标
            if (Application.Current?.Resources == null)
            {
                SetDefaultIcon();
                return;
            }

            string geometryKey;
            string brushKey;

            switch (Icon)
            {
                case MessageBoxImage.Information:
                    geometryKey = "InfoGeometry";
                    brushKey = "PrimaryBrush";
                    break;
                case MessageBoxImage.Warning:
                    geometryKey = "WarningGeometry";
                    brushKey = "WarningBrush";
                    break;
                case MessageBoxImage.Error:
                    geometryKey = "ErrorGeometry";
                    brushKey = "DangerBrush";
                    break;
                case MessageBoxImage.Question:
                    geometryKey = "QuestionGeometry";
                    brushKey = "InfoBrush";
                    break;
                default:
                    geometryKey = "InfoGeometry";
                    brushKey = "PrimaryBrush";
                    break;
            }

            // 获取几何形状
            if (Application.Current.Resources.Contains(geometryKey) &&
                Application.Current.Resources[geometryKey] is Geometry geo)
            {
                IconGeometry = geo;
            }
            else
            {
                // 如果找不到资源，使用默认几何形状
                IconGeometry = CreateDefaultGeometry(Icon);
            }

            // 获取画刷
            if (Application.Current.Resources.Contains(brushKey) &&
                Application.Current.Resources[brushKey] is Brush br)
            {
                IconBrush = br;
            }
            else
            {
                // 如果找不到资源，使用默认颜色
                IconBrush = CreateDefaultBrush(Icon);
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[MessageDialogViewModel] UpdateIcon failed: {ex.Message}");
            // 发生异常时，使用默认图标
            SetDefaultIcon();
        }
    }

    private void SetDefaultIcon()
    {
        IconGeometry = CreateDefaultGeometry(Icon);
        IconBrush = CreateDefaultBrush(Icon);
    }

    private Geometry CreateDefaultGeometry(MessageBoxImage icon)
    {
        // 创建简单的默认几何形状
        return icon switch
        {
            MessageBoxImage.Information => Geometry.Parse("M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,7V13H13V7H11M11,15V17H13V15H11Z"),
            MessageBoxImage.Warning => Geometry.Parse("M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"),
            MessageBoxImage.Error => Geometry.Parse("M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"),
            MessageBoxImage.Question => Geometry.Parse("M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M13,13H11V7H13M13,17H11V15H13"),
            _ => Geometry.Parse("M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,7V13H13V7H11M11,15V17H13V15H11Z")
        };
    }

    private Brush CreateDefaultBrush(MessageBoxImage icon)
    {
        // 创建默认颜色画刷
        return icon switch
        {
            MessageBoxImage.Information => new SolidColorBrush(Colors.DodgerBlue),
            MessageBoxImage.Warning => new SolidColorBrush(Colors.Orange),
            MessageBoxImage.Error => new SolidColorBrush(Colors.Red),
            MessageBoxImage.Question => new SolidColorBrush(Colors.Green),
            _ => new SolidColorBrush(Colors.DodgerBlue)
        };
    }

    private void UpdateButtonVisibility()
    {
        ShowOkButton = ButtonType == MessageBoxButton.OK || ButtonType == MessageBoxButton.OKCancel;
        ShowCancelButton = ButtonType == MessageBoxButton.OKCancel || ButtonType == MessageBoxButton.YesNoCancel;
        ShowYesButton = ButtonType == MessageBoxButton.YesNo || ButtonType == MessageBoxButton.YesNoCancel;
        ShowNoButton = ButtonType == MessageBoxButton.YesNo || ButtonType == MessageBoxButton.YesNoCancel;
    }

    private void UpdateAutoCloseTimer()
    {
        // 先停止现有的定时器
        StopTimer();

        // 只有当 AutoCloseMilliseconds > 0 时才启动新的定时器
        if (AutoCloseMilliseconds > 0)
        {
            StartAutoCloseTimer();
        }
    }

    private void StartAutoCloseTimer()
    {
        if (_autoCloseTimer != null) return; // 避免重复启动

        _startTime = DateTime.Now;
        _autoCloseTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(100) // 每100ms检查一次
        };
        _autoCloseTimer.Tick += OnTimerTick;
        _autoCloseTimer.Start();
    }

    private void OnTimerTick(object? sender, EventArgs e)
    {
        var elapsed = (DateTime.Now - _startTime).TotalMilliseconds;

        if (elapsed >= AutoCloseMilliseconds)
        {
            _autoCloseTimer?.Stop();
            Result = MessageBoxResult.OK; // Auto-close returns OK
            CloseAction?.Invoke();
        }
    }

    private void StopTimer()
    {
        _autoCloseTimer?.Stop();
        _autoCloseTimer = null;
    }
    #endregion

    #region Property Changed Handlers
    partial void OnButtonTypeChanged(MessageBoxButton value)
    {
        UpdateButtonVisibility();
    }

    partial void OnAutoCloseMillisecondsChanged(int value)
    {
        UpdateAutoCloseTimer();
    }

    partial void OnIconChanged(MessageBoxImage value)
    {
        UpdateIcon();
    }
    #endregion
}
