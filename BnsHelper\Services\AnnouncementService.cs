using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Windows;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// 公告服务 - 通过UDP与Go服务端通信
/// </summary>
public class AnnouncementService
{
    private static readonly Lazy<AnnouncementService> _instance = new(() => new AnnouncementService());
    public static AnnouncementService Instance => _instance.Value;

    private readonly string _readStatusFilePath;
    private readonly ObservableCollection<Announcement> _announcements;
    private readonly HashSet<string> _readAnnouncementIds;
    private long _cacheVersion = 0;
    private DateTime _lastRefresh = DateTime.MinValue;
    private readonly object _lockObject = new object();
    private Timer _refreshTimer;

    private AnnouncementService()
    {
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Xylia");
        Directory.CreateDirectory(appDataPath);

        _readStatusFilePath = Path.Combine(appDataPath, "read_announcements.json");

        _announcements = new ObservableCollection<Announcement>();
        _readAnnouncementIds = new HashSet<string>();

        LoadReadStatus();

        // 启动定时刷新（每5分钟检查一次更新）
        _refreshTimer = new Timer(async _ => await CheckForUpdatesAsync(), null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
    }

    /// <summary>
    /// 所有公告
    /// </summary>
    public ObservableCollection<Announcement> Announcements => _announcements;

    /// <summary>
    /// 未读公告数量
    /// </summary>
    public int UnreadCount => _announcements.Count(a => !a.IsRead);

    /// <summary>
    /// 加载公告（同步版本，用于本地数据）
    /// </summary>
    public void LoadAnnouncements()
    {
        try
        {
            // 首先尝试从服务端加载
            Task.Run(async () => await LoadAnnouncementsAsync());
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 启动异步加载公告失败，使用本地数据: {ex.Message}");
            LoadLocalAnnouncements();
        }
    }

    /// <summary>
    /// 加载本地公告数据
    /// </summary>
    private void LoadLocalAnnouncements()
    {
        try
        {
            // 如果没有本地文件，创建默认公告
            if (!File.Exists(_readStatusFilePath))
            {
                CreateDefaultAnnouncements();
                return;
            }

            // 这里可以添加本地公告文件的加载逻辑
            // 目前直接创建默认公告
            CreateDefaultAnnouncements();

            Debug.WriteLine($"[INFO] 加载了 {_announcements.Count} 条本地公告，未读 {UnreadCount} 条");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 加载本地公告失败: {ex.Message}");
            CreateDefaultAnnouncements();
        }
    }

    /// <summary>
    /// 标记公告为已读
    /// </summary>
    public void MarkAsRead(string announcementId)
    {
        if (_readAnnouncementIds.Add(announcementId))
        {
            var announcement = _announcements.FirstOrDefault(a => a.Id == announcementId);
            if (announcement != null)
            {
                announcement.IsRead = true;
            }
            SaveReadStatus();
        }
    }

    /// <summary>
    /// 标记所有公告为已读
    /// </summary>
    public void MarkAllAsRead()
    {
        foreach (var announcement in _announcements)
        {
            if (!announcement.IsRead)
            {
                announcement.IsRead = true;
                _readAnnouncementIds.Add(announcement.Id);
            }
        }
        SaveReadStatus();
    }

    /// <summary>
    /// 刷新公告（供UI调用）
    /// </summary>
    public async Task RefreshAnnouncementsAsync()
    {
        try
        {
            Debug.WriteLine("[INFO] 开始刷新公告...");
            await LoadAnnouncementsAsync();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 刷新公告失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 更新公告（从服务器获取）
    /// </summary>
    public async Task UpdateAnnouncementsAsync()
    {
        try
        {
            await LoadAnnouncementsAsync();
            Debug.WriteLine("[INFO] 公告已更新");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 更新公告失败: {ex.Message}");
        }
    }

    private void LoadReadStatus()
    {
        try
        {
            if (File.Exists(_readStatusFilePath))
            {
                var json = File.ReadAllText(_readStatusFilePath);
                var readIds = JsonSerializer.Deserialize<string[]>(json);
                if (readIds != null)
                {
                    foreach (var id in readIds)
                    {
                        _readAnnouncementIds.Add(id);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 加载已读状态失败: {ex.Message}");
        }
    }

    private void SaveReadStatus()
    {
        try
        {
            var json = JsonSerializer.Serialize(_readAnnouncementIds.ToArray(), new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_readStatusFilePath, json);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 保存已读状态失败: {ex.Message}");
        }
    }

    private void CreateDefaultAnnouncements()
    {
        var defaultAnnouncements = new[]
        {
            new AnnouncementData
            {
                Id = "welcome_001",
                Title = "欢迎使用 BnsHelper",
                Content = "感谢您使用 BnsHelper！\n\n本工具旨在为剑灵玩家提供便捷的游戏辅助功能。\n\n如有问题或建议，请联系开发团队。",
                PublishTime = DateTime.Now.AddDays(-1),
                Type = AnnouncementType.Info
            },
            new AnnouncementData
            {
                Id = "update_001",
                Title = "系统优化更新",
                Content = "本次更新内容：\n\n1. 优化登录性能，提高响应速度\n2. 简化签到流程，减少网络传输\n3. 修复已知问题，提升稳定性\n\n感谢您的支持！",
                PublishTime = DateTime.Now,
                Type = AnnouncementType.Update
            }
        };

        try
        {
            // 直接添加到内存中的公告列表
            Application.Current.Dispatcher.Invoke(() =>
            {
                _announcements.Clear();
                foreach (var announcementData in defaultAnnouncements.OrderByDescending(a => a.PublishTime))
                {
                    var announcement = new Announcement
                    {
                        Id = announcementData.Id,
                        Title = announcementData.Title,
                        Content = announcementData.Content,
                        PublishTime = announcementData.PublishTime,
                        Type = announcementData.Type,
                        IsRead = _readAnnouncementIds.Contains(announcementData.Id)
                    };
                    _announcements.Add(announcement);
                }
            });

            Debug.WriteLine($"[INFO] 创建了 {defaultAnnouncements.Length} 条默认公告");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 创建默认公告失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查服务端公告更新
    /// </summary>
    private async Task CheckForUpdatesAsync()
    {
        try
        {
            // 避免频繁检查
            if (DateTime.Now - _lastRefresh < TimeSpan.FromMinutes(1))
                return;

            var hasUpdate = await CheckAnnouncementUpdateAsync();
            if (hasUpdate)
            {
                await LoadAnnouncementsAsync();
            }

            _lastRefresh = DateTime.Now;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 检查公告更新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 从Go服务端获取公告列表
    /// </summary>
    private async Task<List<Announcement>?> FetchAnnouncementsFromServerAsync()
    {
        try
        {
            // 检查是否已登录
            // TODO: 实现与Go服务端的连接检查
            // if (!BnszsSession.Instance.IsConnected)
            // {
            //     Debug.WriteLine("[WARNING] 未连接到服务端，无法获取公告");
            //     return null;
            // }

            // 暂时总是返回null，使用本地数据
            Debug.WriteLine("[WARNING] UDP通信暂未实现，使用本地数据");
            return null;

            // 构建公告请求数据
            var requestData = new
            {
                client_type = "desktop",
                client_version = "1.0.0", // TODO: 使用VersionHelper.Version
                cache_version = _cacheVersion
            };

            // 发送UDP请求到Go服务端
            var response = await SendAnnouncementRequestAsync("list", requestData);
            if (response == null)
                return null;

            // 解析响应
            var jsonResponse = JsonSerializer.Deserialize<AnnouncementListResponse>(response);
            if (jsonResponse?.success == true && jsonResponse.data != null)
            {
                _cacheVersion = jsonResponse.data.version;

                var announcements = new List<Announcement>();
                foreach (var item in jsonResponse.data.announcements)
                {
                    announcements.Add(new Announcement
                    {
                        Id = item.id.ToString(),
                        Title = item.title,
                        Content = item.content,
                        PublishTime = DateTime.Parse(item.created_at),
                        Type = ConvertAnnouncementType(item.type)
                    });
                }

                return announcements;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 从服务端获取公告失败: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 检查公告是否有更新
    /// </summary>
    private async Task<bool> CheckAnnouncementUpdateAsync()
    {
        try
        {
            // TODO: 实现与Go服务端的连接检查
            // if (!BnszsSession.Instance.IsConnected)
            //     return false;

            // 暂时总是返回false，使用本地数据
            return false;

            var requestData = new { cache_version = _cacheVersion };
            var response = await SendAnnouncementRequestAsync("checkUpdate", requestData);

            if (response != null)
            {
                var jsonResponse = JsonSerializer.Deserialize<AnnouncementUpdateResponse>(response);
                return jsonResponse?.success == true && jsonResponse.data?.has_update == true;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 检查公告更新失败: {ex.Message}");
        }

        return false;
    }

    /// <summary>
    /// 发送公告请求到Go服务端
    /// </summary>
    private async Task<string?> SendAnnouncementRequestAsync(string action, object requestData)
    {
        try
        {
            // 这里需要实现UDP通信逻辑
            // 由于现有的BnszsSession可能不支持公告消息类型，
            // 我们暂时返回null，后续需要扩展UDP通信协议

            Debug.WriteLine($"[WARNING] 公告{action}请求暂未实现UDP通信，使用本地数据");
            return null;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 发送公告{action}请求失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 转换公告类型
    /// </summary>
    private AnnouncementType ConvertAnnouncementType(int serverType)
    {
        return serverType switch
        {
            1 => AnnouncementType.Info,
            2 => AnnouncementType.Warning,
            3 => AnnouncementType.Maintenance,
            _ => AnnouncementType.Info
        };
    }

    /// <summary>
    /// 从服务端加载公告（异步版本）
    /// </summary>
    public async Task LoadAnnouncementsAsync()
    {
        try
        {
            var announcements = await FetchAnnouncementsFromServerAsync();
            if (announcements != null && announcements.Any())
            {
                lock (_lockObject)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        _announcements.Clear();
                        foreach (var announcement in announcements.OrderByDescending(a => a.PublishTime))
                        {
                            announcement.IsRead = _readAnnouncementIds.Contains(announcement.Id);
                            _announcements.Add(announcement);
                        }
                    });
                }

                Debug.WriteLine($"[INFO] 成功从服务端加载 {announcements.Count} 条公告");
            }
            else
            {
                Debug.WriteLine("[WARNING] 服务端返回空公告列表，使用本地默认公告");
                // 如果服务端无公告，创建默认公告
                CreateDefaultAnnouncements();
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 从服务端加载公告失败，使用本地数据: {ex.Message}");
            CreateDefaultAnnouncements();
        }
    }

    /// <summary>
    /// 公告数据结构（用于序列化）
    /// </summary>
    private class AnnouncementData
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime PublishTime { get; set; }
        public AnnouncementType Type { get; set; }
    }

    /// <summary>
    /// 服务端响应数据结构
    /// </summary>
    private class AnnouncementListResponse
    {
        public bool success { get; set; }
        public AnnouncementListData? data { get; set; }
    }

    private class AnnouncementListData
    {
        public long version { get; set; }
        public string last_modified { get; set; } = string.Empty;
        public AnnouncementItem[] announcements { get; set; } = Array.Empty<AnnouncementItem>();
        public int count { get; set; }
    }

    private class AnnouncementItem
    {
        public long id { get; set; }
        public string title { get; set; } = string.Empty;
        public string content { get; set; } = string.Empty;
        public int type { get; set; }
        public string created_at { get; set; } = string.Empty;
    }

    private class AnnouncementUpdateResponse
    {
        public bool success { get; set; }
        public AnnouncementUpdateData? data { get; set; }
    }

    private class AnnouncementUpdateData
    {
        public bool has_update { get; set; }
        public long current_version { get; set; }
    }
}
