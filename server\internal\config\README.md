# 活动配置管理

## 概述

为了解决代码中多处硬编码活动时间配置的问题，我们创建了统一的活动配置管理系统。现在所有活动相关的时间配置都集中在 `activity_config.go` 文件中管理。

## 主要改进

### 问题
之前代码中存在多个地方独立硬编码了相同的活动时间：
- `permission_service.go` 中的 `GetExpiration` 方法
- `permission_service.go` 中的 `isInFreeTrialPeriod` 方法  
- `permission_service.go` 中的 `GetActivityInfoForUser` 方法

这种分散的配置方式导致：
1. **维护困难**：修改活动时间需要在多个地方同步修改
2. **容易出错**：可能遗漏某个地方导致配置不一致
3. **代码重复**：相同的时间配置在多处重复定义

### 解决方案
创建了统一的活动配置管理系统：

1. **集中配置**：所有活动时间配置集中在 `GetCurrentActivityConfig()` 函数中
2. **单例模式**：使用 `GetGlobalActivityConfig()` 获取全局唯一的配置实例
3. **功能封装**：提供 `IsActive()`、`GetRemainingTime()` 等便捷方法

## 使用方法

### 获取活动配置

```go
import "udp-server/server/internal/config"

// 获取活动配置（推荐使用全局单例）
activityConfig := config.GetGlobalActivityConfig()

// 检查活动是否进行中
if activityConfig.IsActive() {
    // 活动进行中的逻辑
    fmt.Printf("活动进行中，结束时间：%s\n", activityConfig.EndTime.Format("2006-01-02 15:04:05"))
}

// 获取活动剩余时间
remaining := activityConfig.GetRemainingTime()
if remaining > 0 {
    days := int(remaining.Hours() / 24)
    hours := int(remaining.Hours()) % 24
    fmt.Printf("活动剩余：%d天%d小时\n", days, hours)
}
```

### 配置结构

```go
type ActivityConfig struct {
    StartTime        time.Time // 活动开始时间
    EndTime          time.Time // 活动结束时间
    SignInResumeTime time.Time // 签到权限恢复时间
    Title            string    // 活动标题
    Description      string    // 活动描述
}
```

### 可用方法

- `IsActive() bool` - 检查活动是否正在进行中
- `GetRemainingTime() time.Duration` - 获取活动剩余时间

## 修改活动配置

当需要修改活动时间时，只需要修改 `activity_config.go` 文件中的 `GetCurrentActivityConfig()` 函数：

```go
func GetCurrentActivityConfig() *ActivityConfig {
    return &ActivityConfig{
        // 修改这里的时间配置即可
        StartTime: time.Date(2025, 6, 25, 0, 0, 0, 0, time.Local),
        EndTime:   time.Date(2025, 7, 10, 23, 59, 59, 0, time.Local),
        // ... 其他配置
    }
}
```

修改后，所有使用该配置的地方都会自动生效，无需在多个文件中同步修改。

## 配置刷新

如果需要在运行时刷新配置（例如从数据库重新加载配置），可以调用：

```go
config.RefreshActivityConfig()
```

## 迁移说明

已完成的迁移：
- ✅ `PermissionService.GetExpiration()` - 使用统一配置
- ✅ `PermissionService.isInFreeTrialPeriod()` - 使用统一配置  
- ✅ `PermissionService.GetActivityInfoForUser()` - 使用统一配置

## 注意事项

1. **时区一致性**：所有时间配置都使用 `time.Local` 确保时区一致
2. **单例模式**：推荐使用 `GetGlobalActivityConfig()` 而不是 `GetCurrentActivityConfig()`
3. **配置更新**：修改配置后建议重启服务或调用 `RefreshActivityConfig()`

## 未来扩展

该配置系统可以进一步扩展为：
1. **数据库配置**：从数据库读取活动配置
2. **多活动支持**：支持同时进行多个活动
3. **动态配置**：支持运行时动态修改配置
4. **配置验证**：添加配置有效性验证
