using System.ComponentModel;

namespace Xylia.BnsHelper.Models;

/// <summary>
/// BOSS倒计时器类型
/// </summary>
public enum BossTimerType
{
    /// <summary>
    /// 怀旧服普通BOSS倒计时（150秒）
    /// </summary>
    ZTX_Normal,

    /// <summary>
    /// 怀旧服白青山脉普通BOSS倒计时（300秒）- 区域5200/5295/5500
    /// </summary>
    ZTX_Normal2,

    /// <summary>
    /// 怀旧服白青山脉变异体BOSS倒计时（300秒）- 区域5200/5295/5500
    /// </summary>
    ZTX_MutantAlarm,

    /// <summary>
    /// 普通BOSS倒计时（5分钟）
    /// </summary>
    ZNCS_Normal,

    /// <summary>
    /// 变异体BOSS倒计时（8分钟）
    /// </summary>
    ZNCS_Mutant,

    /// <summary>
    /// 不祥力量倒计时（2分钟，带感叹号标记）
    /// </summary>
    ZNCS_MutantAlarm,
}

/// <summary>
/// BOSS倒计时器状态
/// </summary>
public enum BossTimerState
{
    /// <summary>
    /// 活跃倒计时中
    /// </summary>
    Active,

    /// <summary>
    /// 已结束，显示"已刷新"
    /// </summary>
    Finished,

    /// <summary>
    /// 已过期，准备隐藏
    /// </summary>
    Expired
}

/// <summary>
/// BOSS倒计时器模型
/// </summary>
public class BossTimer(int channel, BossTimerType type) : INotifyPropertyChanged
{
    /// <summary>
    /// 频道号
    /// </summary>
    public int Channel { get; set; } = channel;

    /// <summary>
    /// 计时器类型
    /// </summary>
    public BossTimerType Type { get; set; } = type;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 总持续时间（秒）
    /// </summary>
    public int TotalDuration { get; set; } = type switch
    {
        BossTimerType.ZNCS_Normal => 300,          // 5分钟
        BossTimerType.ZNCS_Mutant => 480,          // 8分钟
        BossTimerType.ZNCS_MutantAlarm => 120,     // 2分钟
        BossTimerType.ZTX_Normal => 150,           // 1分30秒
        BossTimerType.ZTX_Normal2 => 300,          // 5分钟
        BossTimerType.ZTX_MutantAlarm => 300,      // 5分钟
        _ => 300
    };

    /// <summary>
    /// 倒计时器状态
    /// </summary>
    public BossTimerState State { get; set; } = BossTimerState.Active;

    /// <summary>
    /// 结束时间（倒计时到0的时间）
    /// </summary>
    public DateTime? FinishedTime { get; set; }

    /// <summary>
    /// 剩余时间（秒）
    /// </summary>
    public int RemainingTime
    {
        get
        {
            if (State == BossTimerState.Finished || State == BossTimerState.Expired)
            {
                return 0;
            }

            var elapsed = (DateTime.Now - StartTime).TotalSeconds;
            var remaining = TotalDuration - (int)elapsed;
            return Math.Max(0, remaining);
        }
    }

    /// <summary>
    /// 是否已过期（可以从UI中移除）
    /// </summary>
    public bool IsExpired
    {
        get
        {
            // 如果状态已经是Expired，直接返回true
            if (State == BossTimerState.Expired)
                return true;

            // 如果倒计时结束且已经过了15秒，标记为过期
            if (State == BossTimerState.Finished && FinishedTime.HasValue)
            {
                var timeSinceFinished = (DateTime.Now - FinishedTime.Value).TotalSeconds;
                if (timeSinceFinished >= 15)
                {
                    return true;
                }
            }

            return false;
        }
    }

    /// <summary>
    /// 是否有感叹号标记（不祥力量）
    /// </summary>
    public bool HasExclamationMark => Type is BossTimerType.ZNCS_MutantAlarm or BossTimerType.ZTX_MutantAlarm;

    /// <summary>
    /// 获取显示文本
    /// </summary>
    public string DisplayText
    {
        get
        {
            var name = Type switch
            {
                BossTimerType.ZNCS_MutantAlarm or BossTimerType.ZTX_MutantAlarm => "变异体",
                _ => "BOSS"
            };

            // 如果已结束，显示"已刷新"
            if (State == BossTimerState.Finished)
            {
                return $"[{Channel}] {name}: 已刷新";
            }
            else
            {
                var minutes = RemainingTime / 60;
                var seconds = RemainingTime % 60;
                return $"[{Channel}] {name}: {minutes:D2}:{seconds:D2}";
            }
        }
    }

    #region INotifyPropertyChanged
    public event PropertyChangedEventHandler? PropertyChanged;

    /// <summary>
    /// 更新倒计时器状态
    /// </summary>
    public void UpdateState()
    {
        var elapsed = (DateTime.Now - StartTime).TotalSeconds;
        var remaining = TotalDuration - (int)elapsed;

        // 如果倒计时刚结束，标记为已完成
        if (remaining <= 0 && State == BossTimerState.Active)
        {
            State = BossTimerState.Finished;
            FinishedTime = DateTime.Now;
            System.Diagnostics.Debug.WriteLine($"[BossTimer] 频道{Channel}倒计时结束，显示'已刷新'");
        }
        // 如果已完成且超过15秒，标记为过期
        else if (State == BossTimerState.Finished && FinishedTime.HasValue)
        {
            var timeSinceFinished = (DateTime.Now - FinishedTime.Value).TotalSeconds;
            if (timeSinceFinished >= 15)
            {
                State = BossTimerState.Expired;
                System.Diagnostics.Debug.WriteLine($"[BossTimer] 频道{Channel}倒计时器15秒后准备隐藏");
            }
        }
    }

    /// <summary>
    /// 触发属性变更通知（用于UI更新）
    /// </summary>
    public void NotifyPropertyChanged()
    {
        // 先更新状态
        UpdateState();

        // 然后通知UI更新
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(RemainingTime)));
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(DisplayText)));
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsExpired)));
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(State)));
    }
    #endregion
}
