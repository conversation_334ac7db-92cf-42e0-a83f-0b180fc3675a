<?php /*a:2:{s:101:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\announcement\edit.html";i:1751813114;s:99:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\template.html";i:1751973426;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <!-- 项目构建：兔子、0x1ng、Xylia  | 项目创建:2020-06-01  | 项目更新:2025-02-05 -->
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="剑灵小助手管理系统。">
  <meta name="keywords" content="剑灵骗子,剑灵骗子大全,游戏骗子,剑灵骗子数据库,小助手骗子数据库,小助手提交骗子,小助手自定义,装备查询优化,装备查询自定义,小助手装备查询,剑灵装备查询,剑灵小助手">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-title" content="Amaze UI" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />

  <title>剑灵小助手管理系统</title>
  <link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.css">
  <link rel="stylesheet" href="/css/manage.css?version=2025021504"/>
  <link rel="stylesheet" href="/css/admin.css?version=2021021110">
  <link rel="stylesheet" href="/css/tally.css?version=2021021112">

  <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.min.js"></script>
  <script src="https://static-1251192097.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>
  <script src="/js/amazeui.min.js"></script>
  <script src="/js/manage.js"></script>

  <script>
  // 移动设备侧边栏控制函数 - 全局定义
  function toggleMobileSidebar() {
    console.log('toggleMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    console.log('sidebar:', sidebar);
    console.log('overlay:', overlay);

    if (!sidebar) {
      console.error('Sidebar not found!');
      return;
    }

    if (sidebar.classList.contains('mobile-show')) {
      console.log('Hiding sidebar');
      hideMobileSidebar();
    } else {
      console.log('Showing sidebar');
      showMobileSidebar();
    }
  }

  function showMobileSidebar() {
    console.log('showMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.add('mobile-show');
      console.log('Added mobile-show class to sidebar');
    }

    if (overlay) {
      overlay.classList.add('show');
      console.log('Added show class to overlay');
    }
  }

  function hideMobileSidebar() {
    console.log('hideMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.remove('mobile-show');
      console.log('Removed mobile-show class from sidebar');
    }

    if (overlay) {
      overlay.classList.remove('show');
      console.log('Removed show class from overlay');
    }
  }
  </script>

  <style type="text/css">
  	.ripple {
		position: relative;
		overflow: hidden;
	}
			
	.ripple:after {
		content: "";
		display: block;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		background-image: radial-gradient(circle, #666 10%, transparent 10.01%);
		background-repeat: no-repeat;
		background-position: 50%;
		transform: scale(10, 10);
		opacity: 0;
		transition: transform .3s, opacity .5s;
	}
			
	.ripple:active:after {
		transform: scale(0, 0);
		opacity: .3;
		transition: 0s;
	}

	.btn-sign{
		border-width:0px;
		width: 80px;
		height: 80px;
	}
			
	.option {
		width: 200px;
		height: 40px;
		border: 1px solid #cccccc;
		position: relative;
	}

	.option select {
		border: none;
		outline: none;
		width: 100%;
		height: 40px;
		line-height: 40px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		padding-left: 20px;
	}

	.option:after {
		content: "";
		width: 14px;
		height: 8px;
		background: url(/assets/arrow-down.png) no-repeat center;
		position: absolute;
		right: 20px;
		top: 41%;
		pointer-events: none;
	}

	/* 统计卡片样式 */
	.stats-card {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		margin-bottom: 15px;
		transition: all 0.3s ease;
	}

	.stats-card:hover {
		background: #e9ecef;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
	}

	.stats-number {
		font-size: 2.5em;
		font-weight: bold;
		color: #007bff;
		margin-bottom: 5px;
	}

	.stats-label {
		color: #6c757d;
		font-size: 0.9em;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	/* 统计卡片链接样式 */
	.stats-card-link {
		display: block;
		text-decoration: none;
		color: inherit;
	}

	.stats-card-link:hover {
		text-decoration: none;
		color: inherit;
	}

	/* 状态颜色 */
	.status-normal { color: #28a745; }
	.status-banned { color: #dc3545; }
	.status-premium { color: #ffc107; }
	.status-online { color: #17a2b8; }

	/* 移动设备侧边栏优化 */
	@media only screen and (max-width: 640px) {
		#admin-offcanvas {
			position: fixed !important;
			left: -260px !important;
			top: 51px !important;
			bottom: 0 !important;
			z-index: 1600 !important;
			transition: left 0.3s ease !important;
			background: #fff !important;
			box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
			width: 260px !important;
			height: calc(100vh - 51px) !important;
			overflow-y: auto !important;
			border: 1px solid #ddd !important;
		}

		#admin-offcanvas.mobile-show {
			left: 0 !important;
		}

		/* 确保侧边栏内容可见 */
		#admin-offcanvas .am-offcanvas-bar {
			position: static !important;
			transform: none !important;
			width: 100% !important;
			height: 100% !important;
			background: #fff !important;
			padding: 10px !important;
		}

		/* 确保侧边栏菜单项可见 */
		#admin-offcanvas .admin-sidebar-list {
			background: #fff !important;
			margin: 0 !important;
			padding: 0 !important;
		}

		#admin-offcanvas .admin-sidebar-list li {
			background: #fff !important;
			border-bottom: 1px solid #eee !important;
		}

		#admin-offcanvas .admin-sidebar-list li a {
			color: #333 !important;
			padding: 12px 15px !important;
			display: block !important;
			text-decoration: none !important;
		}

		#admin-offcanvas .admin-sidebar-list li a:hover {
			background: #f5f5f5 !important;
			color: #1E9FFF !important;
		}

		.admin-content {
			margin-left: 0 !important;
		}

		.mobile-sidebar-toggle {
			background: #1E9FFF !important;
			border-color: #1E9FFF !important;
			color: white !important;
			border: none !important;
			padding: 6px 12px !important;
			border-radius: 3px !important;
		}

		.mobile-sidebar-toggle:hover {
			background: #0e7ce8 !important;
			border-color: #0e7ce8 !important;
		}

		/* 确保移动端菜单按钮在右侧正确显示 */
		.am-topbar-right .am-show-sm-only {
			float: right;
		}

		.am-topbar-right .mobile-sidebar-toggle {
			margin: 8px 10px 8px 0;
		}

		/* 遮罩层 */
		.mobile-sidebar-overlay {
			position: fixed;
			top: 51px;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			z-index: 1500;
			display: none;
		}

		.mobile-sidebar-overlay.show {
			display: block;
		}
	}
  </style>
</head>
<body>
	<header class="am-topbar am-topbar-inverse admin-header">
	  <div class="am-topbar-brand">
		<strong>剑灵小助手管理系统</strong> <small> 堕络</small>
	  </div>

	  <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
		<ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list">
			<?php if(session("admin")): ?>
			<li class="am-dropdown" data-am-dropdown>
				<a class="am-dropdown-toggle" data-am-dropdown-toggle href="javascript:void(0);" onclick="SwitchPanel('#admin-dropdown-content')">
				  <span class="am-icon-admin"></span> 管理员 <span class="am-icon-caret-down"></span>
				</a>
				<ul class="am-dropdown-content" id="admin-dropdown-content">
				  <li><a href="javascript:void(0);" onclick="window.location.href='/admin/userinfo.php?id=1'"><span class="am-icon-admin"></span> 资料</a></li>
				  <li><a href="javascript:void(0);" onclick="logout()"><span class="am-icon-power-off"></span> 退出</a></li>
				</ul>
			  </li>
			<?php endif; ?>
			<li class="am-hide-sm-only">
				<?php if(session("user")): ?>
				<a href="/manage/center" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">个人中心</span>
				</a>
				<?php else: ?>
				<a href="/manage/login" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">登录账号</span>
				</a>
				<?php endif; ?>
			</li>
			<!-- 移动设备侧边栏切换按钮 - 放在最右边 -->
			<li class="am-show-sm-only">
				<button class="am-topbar-btn am-btn am-btn-sm am-btn-primary mobile-sidebar-toggle" onclick="toggleMobileSidebar()">
					<span class="am-sr-only">菜单</span> <span class="am-icon-navicon"></span>
				</button>
			</li>
		</ul>
	  </div>
	</header>

	<div class="am-cf admin-main">
	  <!-- sidebar start -->
	  <div class="admin-sidebar am-offcanvas" id="admin-offcanvas">
		<div class="am-offcanvas-bar admin-offcanvas-bar">
		  <ul class="am-list admin-sidebar-list">
			<li><a href="/manage"><span class="am-icon-home"></span> 系统首页</a></li>
			<!-- <li><a href="/manage/liars"><span class="am-icon-th"></span> 骗子列表 <span class="am-badge am-badge-secondary am-margin-right am-fr"></span></a></li> -->
			<!-- <li>
			  <a href="/manage/liarpost"><span class="am-icon-pencil-square-o"></span> <?php if(session('admin')) echo('提交骗子<span class="am-badge am-badge-secondary am-margin-right am-fr">管理</span>'); 		
				  else echo('举报骗子'); 
			  ?></a>
			</li> -->
			<?php if(isset($_SESSION['admin']) && $_SESSION['admin']): 			try {
				$adminMenuItems = app\manage\model\UserAdmin::GetItems();
				if (!empty($adminMenuItems)) {
					foreach ($adminMenuItems as $item) {?>
						<li><a href="<?php echo $item['url']; ?>"><span class="<?php echo $item['icon']; ?>"></span> <?php echo $item['itemName']; ?></a></li>
					<?php }
				} else { ?>
					<li><a href="#"><span class="am-icon-warning"></span> 暂无管理菜单</a></li>
				<?php }
			} catch (Exception $e) { ?>
				<li><a href="#"><span class="am-icon-exclamation-triangle"></span> 菜单加载失败</a></li>
			<?php } ?>
			<?php endif; ?>
			<li><a href="/manage/profile"><span class="am-icon-gift"></span> 自定义资料</a></li>
			<!-- <li><a href="/manage/help"><span class="am-icon-map-signs"></span> 防骗指南</a></li> -->
			<!-- <li><a href="/manage/choose"><span class="am-icon-paint-brush"></span> 安全测试</a></li> -->
			<?php if(session('user')){ ?> <li><a href="/manage/logout"><span class="am-icon-sign-out"></span> 注销</a></li> <?php } ?>
		  </ul>
		  
		  <div class="am-panel am-panel-default admin-sidebar-panel">
			<div class="am-panel-bd">
			  <p><span class="am-icon-bookmark"></span> 公告</p>
			  <div class="line">
                    <span>萌新必看：</span>
                    <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/9839e342f1dd89219e2e2980a9a803a42b9d94cf">2.0.1 使用指南</a></p>
			        <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/7ea1c83b63c8b59b7472c999d15156c4fb843d31">3.0 新版使用指南</a></p>
			        
               </div>
			  
			  <div class="line">
                    <span>剑灵小助手[3.0]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/94255b808597">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1qcuS-obYbHKBQAuuH6_Bhw?pwd=5210">百度网盘</a>
                    </p>
               </div>
               <div class="line">
                    <span>剑灵小助手[2.0.1]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/2ad567e2b816#/list/share">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1irL97YxJfR1-UWxkxqXHIQ?pwd=5210">百度网盘</a>
                    <a class="home" target="_blank" href="https://www.lanzoul.com/iUyTU1irepef">蓝奏云</a>
                    </p>
                </div>
			  
			  <div class="texts" style="margin-top: 20px;">
                    <p><span>助手①群：<code>548810086</code></span></p>
                    <p><span>助手②群：<code>563768233</code></span></p>
                    <p><span>助手③群：<code>618986361</code></span></p>
                </div>
			</div>
		  </div>
		</div>
	  </div>

	  <!-- 移动设备侧边栏遮罩层 -->
	  <div class="mobile-sidebar-overlay" onclick="hideMobileSidebar()"></div>

	  

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">编辑公告</h5>
                    <a href="/manage/admin/announcement" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
                
                <div class="card-body">
                    <form method="post" id="announcementForm">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- 基本信息 -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">公告标题 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" required maxlength="200" value="<?php echo htmlentities((string) $announcement['title']); ?>" placeholder="请输入公告标题">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="content" class="form-label">公告内容 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="content" name="content" rows="10" required placeholder="请输入公告内容，支持HTML格式"><?php echo htmlentities((string) $announcement['content']); ?></textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <!-- 设置选项 -->
                                <div class="mb-3">
                                    <label for="type" class="form-label">公告类型</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="1" <?php if($announcement['type'] == 1): ?>selected<?php endif; ?>>普通公告</option>
                                        <option value="2" <?php if($announcement['type'] == 2): ?>selected<?php endif; ?>>重要公告</option>
                                        <option value="3" <?php if($announcement['type'] == 3): ?>selected<?php endif; ?>>紧急公告</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="status" class="form-label">发布状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="0" <?php if($announcement['status'] == 0): ?>selected<?php endif; ?>>保存为草稿</option>
                                        <option value="1" <?php if($announcement['status'] == 1): ?>selected<?php endif; ?>>已发布</option>
                                        <option value="2" <?php if($announcement['status'] == 2): ?>selected<?php endif; ?>>已下线</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="priority" class="form-label">优先级</label>
                                    <input type="number" class="form-control" id="priority" name="priority" value="<?php echo htmlentities((string) $announcement['priority']); ?>" min="0" max="999" placeholder="数字越大优先级越高">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="target_client" class="form-label">目标客户端</label>
                                    <select class="form-select" id="target_client" name="target_client">
                                        <option value="all" <?php if($announcement['target_client'] == 'all'): ?>selected<?php endif; ?>>全部客户端</option>
                                        <option value="desktop" <?php if($announcement['target_client'] == 'desktop'): ?>selected<?php endif; ?>>桌面端</option>
                                        <option value="mobile" <?php if($announcement['target_client'] == 'mobile'): ?>selected<?php endif; ?>>移动端</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="version_requirement" class="form-label">版本要求</label>
                                    <input type="text" class="form-control" id="version_requirement" name="version_requirement" value="<?php echo htmlentities((string) $announcement['version_requirement']); ?>" placeholder="如：>=1.0.0">
                                    <small class="form-text text-muted">留空表示不限制版本</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">开始显示时间</label>
                                    <input type="datetime-local" class="form-control" id="start_time" name="start_time" value="<?php echo htmlentities((string) date('Y-m-dTH:i',!is_numeric($announcement['start_time'])? strtotime($announcement['start_time']) : $announcement['start_time'])); ?>">
                                    <small class="form-text text-muted">留空表示立即显示</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">结束显示时间</label>
                                    <input type="datetime-local" class="form-control" id="end_time" name="end_time" value="<?php echo htmlentities((string) date('Y-m-dTH:i',!is_numeric($announcement['end_time'])? strtotime($announcement['end_time']) : $announcement['end_time'])); ?>">
                                    <small class="form-text text-muted">留空表示永久显示</small>
                                </div>
                                
                                <!-- 统计信息 -->
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">统计信息</h6>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                创建时间：<?php echo htmlentities((string) $announcement['created_at']); ?><br>
                                                更新时间：<?php echo htmlentities((string) $announcement['updated_at']); ?><br>
                                                查看次数：<?php echo htmlentities((string) $announcement['view_count']); ?><br>
                                                发布者：<?php echo htmlentities((string) $announcement['admin_username']); ?>
                                            </small>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-secondary" onclick="previewAnnouncement()">预览</button>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">取消</button>
                                        <button type="submit" class="btn btn-primary">更新公告</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">公告预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <h4 id="previewTitle"></h4>
                    <div class="mb-2">
                        <span id="previewType" class="badge"></span>
                        <span id="previewTarget" class="badge bg-secondary"></span>
                        <span id="previewPriority" class="badge bg-info"></span>
                    </div>
                    <div id="previewBody"></div>
                    <hr>
                    <small class="text-muted">
                        <div>显示时间：<span id="previewTime"></span></div>
                        <div>版本要求：<span id="previewVersion"></span></div>
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 表单提交
document.getElementById('announcementForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // 验证必填字段
    if (!formData.get('title').trim()) {
        layer.msg('请输入公告标题', {icon: 2});
        return;
    }
    
    if (!formData.get('content').trim()) {
        layer.msg('请输入公告内容', {icon: 2});
        return;
    }
    
    // 验证时间逻辑
    const startTime = formData.get('start_time');
    const endTime = formData.get('end_time');
    if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
        layer.msg('结束时间必须大于开始时间', {icon: 2});
        return;
    }
    
    // 提交表单
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.textContent = '更新中...';
    
    $.ajax({
        url: window.location.href,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => {
                    window.location.href = '/manage/admin/announcement';
                }, 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
                submitBtn.disabled = false;
                submitBtn.textContent = '更新公告';
            }
        },
        error: function() {
            layer.msg('网络错误，请重试', {icon: 2});
            submitBtn.disabled = false;
            submitBtn.textContent = '更新公告';
        }
    });
});

// 预览功能
function previewAnnouncement() {
    const title = document.getElementById('title').value;
    const content = document.getElementById('content').value;
    const type = document.getElementById('type').value;
    const targetClient = document.getElementById('target_client').value;
    const priority = document.getElementById('priority').value;
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    const versionRequirement = document.getElementById('version_requirement').value;
    
    if (!title.trim()) {
        layer.msg('请先输入公告标题', {icon: 2});
        return;
    }
    
    if (!content.trim()) {
        layer.msg('请先输入公告内容', {icon: 2});
        return;
    }
    
    // 设置预览内容
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewBody').innerHTML = content;
    
    // 设置类型标签
    const typeText = type == '1' ? '普通公告' : (type == '2' ? '重要公告' : '紧急公告');
    const typeClass = type == '1' ? 'bg-secondary' : (type == '2' ? 'bg-warning' : 'bg-danger');
    document.getElementById('previewType').textContent = typeText;
    document.getElementById('previewType').className = 'badge ' + typeClass;
    
    // 设置目标客户端
    const targetText = targetClient == 'all' ? '全部客户端' : (targetClient == 'desktop' ? '桌面端' : '移动端');
    document.getElementById('previewTarget').textContent = targetText;
    
    // 设置优先级
    document.getElementById('previewPriority').textContent = '优先级: ' + (priority || '0');
    
    // 设置时间信息
    let timeText = '';
    if (startTime && endTime) {
        timeText = startTime + ' 至 ' + endTime;
    } else if (startTime) {
        timeText = '从 ' + startTime + ' 开始';
    } else if (endTime) {
        timeText = '至 ' + endTime + ' 结束';
    } else {
        timeText = '永久显示';
    }
    document.getElementById('previewTime').textContent = timeText;
    
    // 设置版本要求
    document.getElementById('previewVersion').textContent = versionRequirement || '无限制';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}
</script>

	   
	  <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
		<div id="innerdiv" style="position:absolute;">
		  <img id="bigimg" style="border:5px solid #fff;" src="" />
		</div>
	  </div>
	  <footer class="admin-content-footer">
		<hr>
		<p class="am-padding-left">© 2018 duoluosb.</p>
	  </footer>
	</div>

	<script>
	// 页面初始化脚本

	// 窗口大小改变时隐藏移动侧边栏
	window.addEventListener('resize', function() {
		if (window.innerWidth > 640) {
			hideMobileSidebar();
		}
	});

	// 页面加载完成后的初始化
	document.addEventListener('DOMContentLoaded', function() {
		// 添加触摸事件支持
		var toggleBtn = document.querySelector('.mobile-sidebar-toggle');
		if (toggleBtn) {
			toggleBtn.addEventListener('touchstart', function(e) {
				e.preventDefault();
				toggleMobileSidebar();
			});
		}
	});
	</script>
</body>

<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>