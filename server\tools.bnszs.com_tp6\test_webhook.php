<?php
/**
 * 测试HTTP Webhook功能
 */

echo "=== 测试HTTP Webhook功能 ===\n\n";

// 测试数据
$testData = [
    'type' => 'published',
    'announcement_id' => 12345,
    'title' => 'Webhook测试公告',
    'type_code' => 1,
    'priority' => 1,
    'target_client' => 'all',
    'timestamp' => time(),
];

$webhookUrl = 'http://127.0.0.1:8080/webhook/announcement';

echo "1. 测试Webhook URL: $webhookUrl\n";
echo "2. 测试数据:\n";
echo json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 发送请求
echo "3. 发送HTTP POST请求...\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $webhookUrl,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_CONNECTTIMEOUT => 5,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testData))
    ],
    CURLOPT_USERAGENT => 'BNS-PHP-Webhook-Test/1.0'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "4. 响应结果:\n";

if ($error) {
    echo "✗ cURL错误: $error\n";
    echo "这可能意味着:\n";
    echo "  - Go服务端没有运行\n";
    echo "  - HTTP服务器没有启动\n";
    echo "  - 端口8080被占用或不可访问\n";
} else {
    echo "HTTP状态码: $httpCode\n";
    echo "响应内容: $response\n";
    
    if ($httpCode === 200) {
        $responseData = json_decode($response, true);
        if ($responseData && isset($responseData['success']) && $responseData['success']) {
            echo "✓ Webhook测试成功！\n";
            echo "Go服务端正确接收并处理了Webhook请求\n";
        } else {
            echo "⚠ HTTP请求成功，但响应表示处理失败\n";
            echo "响应数据: " . print_r($responseData, true) . "\n";
        }
    } else {
        echo "✗ HTTP请求失败，状态码: $httpCode\n";
        if ($httpCode === 404) {
            echo "这可能意味着路由配置有问题\n";
        } elseif ($httpCode === 500) {
            echo "这可能意味着Go服务端内部错误\n";
        }
    }
}

echo "\n5. 测试健康检查接口...\n";

$healthUrl = 'http://127.0.0.1:8080/health';
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $healthUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 5,
    CURLOPT_CONNECTTIMEOUT => 3,
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "✗ 健康检查失败: $error\n";
} else {
    echo "健康检查状态码: $httpCode\n";
    echo "健康检查响应: $response\n";
    
    if ($httpCode === 200) {
        echo "✓ HTTP服务器正常运行\n";
    } else {
        echo "✗ HTTP服务器异常\n";
    }
}

echo "\n=== 测试完成 ===\n";
?>
