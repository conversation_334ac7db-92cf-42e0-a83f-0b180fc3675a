﻿using Newtonsoft.Json;
using System.IO;
using System.Net.Http;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.Data.Models;

namespace Xylia.Preview.UI.GameUI.Scene.Game_CharacterInfo.Api;
public class Equipments
{
    public Equipment hand;
    public Equipment hand_appearance;
    public Equipment body;
    public Equipment eye;
    public Equipment head;
    public Equipment ear_left;
    public Equipment finger_left;
    public Equipment bracelet;
    public Equipment neck;
    public Equipment soul;
    public Equipment soul_2;
    public Equipment gloves;
    public Equipment belt;
    public Equipment pet;
    public Equipment pet_1_appearance;
    public Equipment swift_badge;
    public Equipment soul_badge;
    public Equipment soulshield_1;
    public Equipment soulshield_2;
    public Equipment soulshield_3;
    public Equipment soulshield_4;
    public Equipment soulshield_5;
    public Equipment soulshield_6;
    public Equipment soulshield_7;
    public Equipment soulshield_8;
    public Equipment alternate_soulshield_1;
    public Equipment alternate_soulshield_2;
    public Equipment alternate_soulshield_3;
    public Equipment alternate_soulshield_4;
    public Equipment alternate_soulshield_5;
    public Equipment alternate_soulshield_6;
    public Equipment alternate_soulshield_7;
    public Equipment alternate_soulshield_8;

    public static Equipments? Get(Creature creature)
    {
        string Host = @"https://%sgate.bns.qq.com";
        string Url = @"/ingame/api/character/equipments.json";

        var url = new UriBuilder(Host.Replace("%s", creature.WorldId.ToString()[..2]) + Url) { Query = $"c={creature.Name}" }.Uri;

        var response = new HttpClient().GetAsync(url).Result;
        if (!response.IsSuccessStatusCode) throw new InvalidDataException();

        return JsonConvert.DeserializeObject<Equipments>(response.Content.ReadAsStringAsync().Result);
    }
}

public class Equipment
{
    public Detail detail;
    public Equip equip;
    public string? guild_uniform;
    public string? tooltip_string;

    public struct Detail
    {
        public Gem[] added_enchant_gems;
        public Gem[] added_gems;
        public bool appearance;
        public string? appearance_item;
        public AssetType asset_type;
        public string? custom_list;
        public int durability;
        public int enchant;
        public int equip_gem_piece_id;
        public string equipped_part;
        public string? gem_identities;
        public string? growth;
        public int guild_id;
        public Item item;
        public string? item_spirit;
        public int pos;
        public int quantity;
        public bool Sealed;
        public bool sequestration;
        public bool used;
        public bool wearable;

        public struct Gem
        {
            public string? category_major_name;
            public string? category_middle_name;
            public string? category_minor_name;
            public int grade;
            public string grade_name;
            public string icon;
            public string icon_transparent;
            public int id;
            public sbyte level;
            public string? name;
            public int slot;
            public bool slot_open;
            public string? main_abilities;
            public string? sub_abilities;
        }
    }

    public struct Equip
    {
        public object appearance;
        public AssetType asset_type;
        public string equipped_part;
        public long id;
        public Item item;
        public int pos;
        public bool sequestration;
        public bool Sealed;
        public bool wearable;
    }

    public struct Item
    {
        public int id;
        public sbyte level;
        public string name;
        public string icon;
        public string icon_extra;
        public string icon_transparent;
        public sbyte grade;
        public string grade_name;
        public string set_item_code;
        public bool resealable;
        public string type;
    }


    public static implicit operator Equipment?(Xylia.BnsHelper.Models.Equipment? e)
    {
        if (e is null) return null;

        return new Equipment()
        {
            detail = new Detail()
            {
                item = new Item()
                {
                    id = e.Id,
                    name = e.Name,
                    icon = SettingHelper.Default.Publisher is EPublisher.ZTX ?
                        $"https://game.gtimg.cn/images/bns/iconneo/{e.Id}.png" :
                        $"https://game.gtimg.cn/images/bns/iconneoglo//{e.Id}.png"
                },
            },
        };
    }
}
