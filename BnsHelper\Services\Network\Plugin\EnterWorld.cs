﻿using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class EnterWorld : IPacket
{
    #region Fields
    public int ZoneId = 0;
    public Creature? Player;
    public Creature? PlayerSummoned;
    #endregion

    #region Methods
    public DataArchiveWriter Create() => new();

    public void Read(DataArchive reader)
    {
        ZoneId = reader.Read<int>();
        Player = reader.ReadByte() == 1 ? new Creature(reader) : null;
        PlayerSummoned = reader.ReadByte() == 1 ? new Creature(reader) : null;
    }
    #endregion
}
