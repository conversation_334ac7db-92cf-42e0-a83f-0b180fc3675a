<?php
// +----------------------------------------------------------------------
// | Manage应用路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// 测试路由
Route::rule('/test', 'Test/index');

// 管理后台基础路由
Route::rule('/', 'Manage/Index');
Route::rule('/login', 'Manage/Login');
Route::rule('/logout', 'Manage/Logout');
Route::rule('/help', 'Manage/Help');
Route::rule('/choose', 'Manage/Choose');
Route::rule('/liars', 'Manage/Liars');
Route::rule('/liarpost', 'Manage/LiarPost');
Route::rule('/changelog', 'Manage/ChangeLog');
Route::rule('/sendcode', 'Manage/SendCode');
Route::rule('/register', 'Manage/Register');
Route::rule('/image', 'Manage/Image');

// 用户中心路由
Route::rule('/center', 'manage/Center/main');
Route::rule('/draw', 'manage/Center/Draw');
Route::rule('/profile', 'manage/Profile/main');

// 管理员功能路由组
Route::group('admin', function() {
    Route::rule('/check', 'Admin/check');
    Route::rule('/cdkey', 'Admin/cdkey');
    Route::rule('/users', 'Admin/users');
    Route::rule('/question', 'Admin/question');
    Route::rule('/profiles', 'Admin/profiles');

    // 风控管理路由
    Route::group('risk', function() {
        Route::rule('/', 'manage.RiskControl/index');
        Route::rule('/events', 'manage.RiskControl/events');
        Route::rule('/processEvent', 'manage.RiskControl/processEvent');
        Route::rule('/batchProcessUsers', 'manage.RiskControl/batchProcessUsers');
        Route::rule('/config', 'manage.RiskControl/config');
        Route::rule('/signin', 'manage.RiskControl/signin');
        Route::rule('/clearSigninCache', 'manage.RiskControl/clearSigninCache');
        Route::rule('/getCacheStats', 'manage.RiskControl/getCacheStats');
    });

    // 在线用户统计管理路由
    Route::group('online', function() {
        Route::rule('/', 'manage.OnlineDashboard/index');
        Route::rule('/chart', 'manage.OnlineDashboard/chart');
        Route::rule('/realtime', 'manage.OnlineDashboard/realtime');
        Route::rule('/users', 'manage.OnlineDashboard/users');
    });

    // 公告管理路由
    Route::group('announcement', function() {
        Route::rule('/', 'manage.Announcement/index');
        Route::rule('/add', 'manage.Announcement/add');
        Route::rule('/edit/:id', 'manage.Announcement/edit');
        Route::rule('/delete/:id', 'manage.Announcement/delete');
        Route::rule('/publish/:id', 'manage.Announcement/publish');
        Route::rule('/offline/:id', 'manage.Announcement/offline');
        Route::rule('/batch', 'manage.Announcement/batch');
        Route::rule('/detail/:id', 'manage.Announcement/detail');
    });

    // 更新配置管理路由
    Route::group('update-config', function() {
        Route::rule('/', 'manage.UpdateConfig/index');
        Route::rule('/edit', 'manage.UpdateConfig/edit');
        Route::rule('/save', 'manage.UpdateConfig/save');
        Route::rule('/delete/:id', 'manage.UpdateConfig/delete');
        Route::rule('/groups/:app_name', 'manage.UpdateConfig/groups');
    });
});

// API接口路由组
Route::group('api', function() {
    // 在线统计API
    Route::rule('/online/current', 'api/OnlineStats/current');
    Route::rule('/online/health', 'api/OnlineStats/health');

    // 风控API
    Route::rule('/risk/record', 'api/RiskControl/recordEvent');
    Route::rule('/risk/stats', 'api/RiskControl/getStats');
    Route::rule('/risk/events', 'api/RiskControl/getEvents');
    Route::rule('/risk/update', 'api/RiskControl/updateEventStatus');
    Route::rule('/risk/check', 'api/RiskControl/checkDeviceRisk');
    Route::rule('/risk/health', 'api/RiskControl/health');

    // 更新配置API
    Route::rule('/update/list', 'api/UpdateConfig/list');
    Route::rule('/update/detail', 'api/UpdateConfig/detail');
    Route::rule('/update/save', 'api/UpdateConfig/save');
    Route::rule('/update/delete', 'api/UpdateConfig/delete');
    Route::rule('/update/toggle', 'api/UpdateConfig/toggle');
    Route::rule('/update/refresh-cache', 'api/UpdateConfig/refreshCache');
});
