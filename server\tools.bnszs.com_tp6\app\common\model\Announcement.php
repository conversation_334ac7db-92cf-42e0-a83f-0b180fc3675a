<?php
namespace app\common\model;

use think\Model;
use think\facade\Db;

class Announcement extends Model
{
    protected $pk = 'id';
    protected $table = 'bns_announcement';
    
    // 公告类型常量
    const TYPE_NORMAL = 1;      // 普通公告
    const TYPE_IMPORTANT = 2;   // 重要公告
    const TYPE_URGENT = 3;      // 紧急公告
    
    // 状态常量
    const STATUS_DRAFT = 0;     // 草稿
    const STATUS_PUBLISHED = 1; // 已发布
    const STATUS_OFFLINE = 2;   // 已下线
    
    // 目标客户端常量
    const TARGET_ALL = 'all';
    const TARGET_DESKTOP = 'desktop';
    const TARGET_MOBILE = 'mobile';

    /**
     * 获取有效的公告列表（供客户端API使用）
     * 支持缓存机制
     */
    public static function getActiveAnnouncements($clientType = 'all', $version = null) {
        $query = static::where('status', self::STATUS_PUBLISHED)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->whereOr('start_time', '<=', date('Y-m-d H:i:s'));
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->whereOr('end_time', '>=', date('Y-m-d H:i:s'));
            });
            
        // 客户端类型过滤
        if ($clientType !== 'all') {
            $query->where(function($query) use ($clientType) {
                $query->where('target_client', 'all')
                      ->whereOr('target_client', $clientType);
            });
        }
        
        return $query->order('priority DESC, created_at DESC')
                    ->field('id,title,content,type,priority,start_time,end_time,target_client,version_requirement,created_at,updated_at')
                    ->select()
                    ->toArray();
    }
    
    /**
     * 获取当前公告版本号
     */
    public static function getCurrentVersion() {
        $version = Db::table('bns_announcement_version')->where('id', 1)->value('version');
        return $version ?: 1;
    }
    
    /**
     * 获取最后修改时间
     */
    public static function getLastModified() {
        $lastModified = Db::table('bns_announcement_version')->where('id', 1)->value('last_modified');
        return $lastModified ?: date('Y-m-d H:i:s');
    }
    
    /**
     * 检查版本是否有更新
     */
    public static function hasUpdate($clientVersion) {
        $currentVersion = self::getCurrentVersion();
        return $currentVersion > $clientVersion;
    }
    
    /**
     * 获取管理后台公告列表
     */
    public static function getAdminList($page = 1, $limit = 20, $filters = []) {
        $query = static::order('created_at DESC');
        
        // 状态过滤
        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('status', $filters['status']);
        }
        
        // 类型过滤
        if (isset($filters['type']) && $filters['type'] !== '') {
            $query->where('type', $filters['type']);
        }
        
        // 关键词搜索
        if (isset($filters['keyword']) && $filters['keyword'] !== '') {
            $keyword = $filters['keyword'];
            $query->where(function($query) use ($keyword) {
                $query->where('title', 'like', '%' . $keyword . '%')
                      ->whereOr('content', 'like', '%' . $keyword . '%');
            });
        }
        
        return $query->paginate([
            'list_rows' => $limit,
            'page' => $page
        ]);
    }
    
    /**
     * 创建公告
     */
    public static function createAnnouncement($data, $adminInfo) {
        $announcement = new static();
        $announcement->title = $data['title'];
        $announcement->content = $data['content'];
        $announcement->type = $data['type'] ?? self::TYPE_NORMAL;
        $announcement->status = $data['status'] ?? self::STATUS_DRAFT;
        $announcement->priority = $data['priority'] ?? 0;
        $announcement->start_time = $data['start_time'] ?? null;
        $announcement->end_time = $data['end_time'] ?? null;
        $announcement->target_client = $data['target_client'] ?? self::TARGET_ALL;
        $announcement->version_requirement = $data['version_requirement'] ?? null;
        $announcement->admin_uid = $adminInfo['uid'];
        $announcement->admin_username = $adminInfo['username'];

        if ($announcement->save()) {
            return $announcement;
        }

        return false;
    }
    
    /**
     * 更新公告
     */
    public function updateAnnouncement($data, $adminInfo) {
        $this->title = $data['title'];
        $this->content = $data['content'];
        $this->type = $data['type'] ?? $this->type;
        $this->status = $data['status'] ?? $this->status;
        $this->priority = $data['priority'] ?? $this->priority;
        $this->start_time = $data['start_time'] ?? $this->start_time;
        $this->end_time = $data['end_time'] ?? $this->end_time;
        $this->target_client = $data['target_client'] ?? $this->target_client;
        $this->version_requirement = $data['version_requirement'] ?? $this->version_requirement;

        if ($this->save()) {
            return $this;
        }

        return false;
    }
    
    /**
     * 删除公告
     */
    public function deleteAnnouncement() {
        return $this->delete();
    }
    
    /**
     * 发布公告
     */
    public function publish() {
        $this->status = self::STATUS_PUBLISHED;
        return $this->save();
    }
    
    /**
     * 下线公告
     */
    public function offline() {
        $this->status = self::STATUS_OFFLINE;
        return $this->save();
    }
    
    /**
     * 增加查看次数
     */
    public function incrementViewCount() {
        $this->view_count = ($this->view_count ?? 0) + 1;
        return $this->save();
    }
    
    /**
     * 获取公告统计信息
     */
    public static function getStats() {
        return [
            'total' => static::count(),
            'published' => static::where('status', self::STATUS_PUBLISHED)->count(),
            'draft' => static::where('status', self::STATUS_DRAFT)->count(),
            'offline' => static::where('status', self::STATUS_OFFLINE)->count(),
            'today' => static::where('created_at', '>=', date('Y-m-d 00:00:00'))->count()
        ];
    }
    
    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data) {
        $types = [
            self::TYPE_NORMAL => '普通公告',
            self::TYPE_IMPORTANT => '重要公告',
            self::TYPE_URGENT => '紧急公告'
        ];
        return $types[$data['type']] ?? '未知';
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data) {
        $statuses = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_OFFLINE => '已下线'
        ];
        return $statuses[$data['status']] ?? '未知';
    }
    
    /**
     * 获取目标客户端文本
     */
    public function getTargetClientTextAttr($value, $data) {
        $targets = [
            self::TARGET_ALL => '全部客户端',
            self::TARGET_DESKTOP => '桌面端',
            self::TARGET_MOBILE => '移动端'
        ];
        return $targets[$data['target_client']] ?? '未知';
    }
    
    /**
     * 检查公告是否在有效期内
     */
    public function isActive() {
        $now = date('Y-m-d H:i:s');
        
        // 检查状态
        if ($this->status !== self::STATUS_PUBLISHED) {
            return false;
        }
        
        // 检查开始时间
        if ($this->start_time && $this->start_time > $now) {
            return false;
        }
        
        // 检查结束时间
        if ($this->end_time && $this->end_time < $now) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取有效期状态文本
     */
    public function getValidityStatusAttr($value, $data) {
        if (!$this->isActive()) {
            return '无效';
        }
        
        $now = date('Y-m-d H:i:s');
        
        if ($this->start_time && $this->start_time > $now) {
            return '未开始';
        }
        
        if ($this->end_time && $this->end_time < $now) {
            return '已过期';
        }
        
        return '有效';
    }
}
