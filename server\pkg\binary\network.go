package binary

import (
	"encoding/binary"
	"fmt"
	"net"
	"time"
)

// NetworkHandler 网络处理器
type NetworkHandler struct {
	detector *ProtocolDetector
}

// NewNetworkHandler 创建网络处理器
func NewNetworkHandler() *NetworkHandler {
	return &NetworkHandler{
		detector: NewProtocolDetector(),
	}
}

// SendMessage 发送消息到UDP连接
func (nh *NetworkHandler) SendMessage(conn *net.UDPConn, addr *net.UDPAddr, data []byte) error {
	if len(data) == 0 {
		return fmt.Errorf("empty message data")
	}

	// 检查消息大小
	if len(data) > MaxMessageSize {
		return fmt.Errorf("message too large: %d bytes (max: %d)", len(data), MaxMessageSize)
	}

	// 发送完整消息
	n, err := conn.WriteToUDP(data, addr)
	if err != nil {
		return fmt.Errorf("failed to send message: %w", err)
	}

	if n != len(data) {
		return fmt.Errorf("incomplete send: sent %d bytes, expected %d", n, len(data))
	}

	return nil
}

// ReceiveMessage 从UDP连接接收消息
func (nh *NetworkHandler) ReceiveMessage(conn *net.UDPConn, timeout time.Duration) ([]byte, *net.UDPAddr, error) {
	// 设置读取超时
	if timeout > 0 {
		conn.SetReadDeadline(time.Now().Add(timeout))
		defer conn.SetReadDeadline(time.Time{}) // 清除超时
	}

	// 创建缓冲区
	buffer := make([]byte, MaxMessageSize)

	// 读取数据
	n, addr, err := conn.ReadFromUDP(buffer)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to read from UDP: %w", err)
	}

	if n == 0 {
		return nil, addr, fmt.Errorf("received empty message")
	}

	// 返回实际接收的数据
	return buffer[:n], addr, nil
}

// ValidateMessage 验证接收到的消息
func (nh *NetworkHandler) ValidateMessage(data []byte) error {
	if len(data) == 0 {
		return fmt.Errorf("empty message")
	}

	// 检测协议类型
	protocolType := nh.detector.DetectProtocol(data)
	if protocolType == ProtocolTypeUnknown {
		return fmt.Errorf("unknown protocol type")
	}

	// 验证协议数据
	return nh.detector.ValidateProtocolData(data, protocolType)
}

// ProcessBinaryMessage 处理二进制协议消息
func (nh *NetworkHandler) ProcessBinaryMessage(data []byte) (*Message, error) {
	// 验证消息
	if err := nh.ValidateMessage(data); err != nil {
		return nil, fmt.Errorf("message validation failed: %w", err)
	}

	// 检查消息头
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("incomplete message header: need %d bytes, got %d", HeaderSize, len(data))
	}

	// 解码消息头
	header, err := DecodeHeader(data)
	if err != nil {
		return nil, fmt.Errorf("failed to decode header: %w", err)
	}

	// 验证消息长度
	expectedLength := int(header.Length)
	if len(data) < expectedLength {
		return nil, fmt.Errorf("incomplete message: expected %d bytes, got %d", expectedLength, len(data))
	}

	// 只处理完整的消息数据
	completeData := data[:expectedLength]

	// 解密消息（如果已加密）
	decryptedData, err := DecryptMessageBody(completeData)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt message: %w", err)
	}

	// 解压缩消息（如果已压缩）
	decompressedData, err := DecompressMessageBody(decryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress message: %w", err)
	}

	// 解码二进制消息
	binaryMsg, err := DecodeMessage(decompressedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode binary message: %w", err)
	}

	return binaryMsg, nil
}

// CreateLoginResponse 创建登录响应
func (nh *NetworkHandler) CreateLoginResponse(success bool, errorCode uint32, errorMsg string, data map[string]interface{}) ([]byte, error) {
	// 如果success为false，确保errorCode非0
	if !success && errorCode == 0 {
		errorCode = 1 // 默认错误码
	}
	// 如果success为true，确保errorCode为0
	if success {
		errorCode = 0
	}

	resp := &LoginResponse{
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	// 填充响应数据（只处理需要编码传输的字段）
	if data != nil {
		if permission := convertToUint8(data["permission"]); permission != 0 {
			resp.Permission = permission
		}
		if permissionExpiration, ok := data["permission_expiration"].(int64); ok {
			resp.PermissionExpiration = permissionExpiration
		}
		if token, ok := data["token"].(string); ok {
			resp.Token = token
		}
		if pluginVersion, ok := data["plugin_version"].(string); ok {
			resp.PluginVersion = pluginVersion
		}
		if pluginUrl, ok := data["plugin_url"].(string); ok {
			resp.PluginUrl = pluginUrl
		}
	}

	// 创建消息
	message := NewLoginResponseMessage(resp)

	// 编码并加密消息
	return message.EncodeWithEncryption()
}

// 心跳响应现在使用最小响应包，只包含基本响应字段，不包含业务数据

// CreateEmptyResponse 创建最小响应包（包含基本响应字段）
func (nh *NetworkHandler) CreateEmptyResponse(msgType uint8) ([]byte, error) {
	// 创建包含基本响应字段的最小消息体
	// 字段：ErrorCode(uint32)
	// ErrorCode=0表示成功，成功时不包含ErrorMessage字段
	body := make([]byte, 4) // 只有4字节ErrorCode

	// ErrorCode = 0 (表示成功，4字节，小端序)
	binary.LittleEndian.PutUint32(body, 0)

	message := NewMessage(msgType, 0, body)

	// 编码消息（不需要加密）
	return message.Encode()
}

// CreateHeartbeatResponse 创建心跳响应包（包含在线用户数量）
func (nh *NetworkHandler) CreateHeartbeatResponse(onlineUserCount int) ([]byte, error) {
	// 创建包含基本响应字段和在线用户数量的消息体
	// 字段：ErrorCode(uint32) + OnlineUserCount(uint32)
	body := make([]byte, 8) // 4字节ErrorCode + 4字节OnlineUserCount

	// ErrorCode = 0 (表示成功，4字节，小端序)
	binary.LittleEndian.PutUint32(body[0:4], 0)

	// OnlineUserCount (4字节，小端序)
	binary.LittleEndian.PutUint32(body[4:8], uint32(onlineUserCount))

	message := NewMessage(MsgTypeHeartbeatResponse, 0, body)

	// 编码消息（不需要加密）
	return message.Encode()
}

// CreateHeartbeatResponseWithUpdateCheck 创建心跳响应包（包含在线用户数量和更新检查）
func (nh *NetworkHandler) CreateHeartbeatResponseWithUpdateCheck(onlineUserCount int, forceUpdate bool) ([]byte, error) {
	// 创建包含基本响应字段、在线用户数量和强制更新标志的消息体
	// 字段：ErrorCode(uint32) + OnlineUserCount(uint32) + ForceUpdate(uint8)
	body := make([]byte, 9) // 4字节ErrorCode + 4字节OnlineUserCount + 1字节ForceUpdate

	// ErrorCode = 0 (表示成功，4字节，小端序)
	binary.LittleEndian.PutUint32(body[0:4], 0)

	// OnlineUserCount (4字节，小端序)
	binary.LittleEndian.PutUint32(body[4:8], uint32(onlineUserCount))

	// ForceUpdate (1字节)
	if forceUpdate {
		body[8] = 1
	} else {
		body[8] = 0
	}

	message := NewMessage(MsgTypeHeartbeatResponse, 0, body)

	// 编码消息（不需要加密）
	return message.Encode()
}

// CreateLogoutResponse 创建注销响应包
func (nh *NetworkHandler) CreateLogoutResponse(success bool, errorCode uint32, errorMsg string) ([]byte, error) {
	// 如果success为false，确保errorCode非0
	if !success && errorCode == 0 {
		errorCode = 1 // 默认错误码
	}
	// 如果success为true，确保errorCode为0
	if success {
		errorCode = 0
	}

	resp := &LogoutResponse{
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	// 创建消息
	message := NewLogoutResponseMessage(resp)

	// 编码消息（不需要加密）
	return message.Encode()
}

// GetMessageInfo 获取消息信息
func (nh *NetworkHandler) GetMessageInfo(data []byte) (map[string]interface{}, error) {
	info := make(map[string]interface{})

	if len(data) == 0 {
		return info, fmt.Errorf("empty data")
	}

	// 检测协议类型
	protocolType, protocolInfo, err := nh.detector.GetProtocolInfo(data)
	if err != nil {
		return info, fmt.Errorf("failed to get protocol info: %w", err)
	}

	info["protocol_type"] = protocolType.String()
	info["protocol_info"] = protocolInfo
	info["data_length"] = len(data)

	// 如果是二进制协议，获取更多信息
	if protocolType == ProtocolTypeBinary && len(data) >= HeaderSize {
		header, err := DecodeHeader(data)
		if err == nil {
			info["message_type"] = fmt.Sprintf("0x%02X", header.MsgType)
			info["message_type_name"] = MsgTypeNames[header.MsgType]
			info["message_length"] = header.Length
			info["is_encrypted"] = header.HasFlag(FlagEncrypted)
			info["is_compressed"] = header.HasFlag(FlagCompressed)
			info["needs_response"] = header.HasFlag(FlagNeedResponse)
			info["timestamp"] = header.Timestamp
		}
	}

	return info, nil
}

// LogMessageInfo 记录消息信息（用于调试）
func (nh *NetworkHandler) LogMessageInfo(data []byte, direction string, addr *net.UDPAddr) {
	info, err := nh.GetMessageInfo(data)
	if err != nil {
		fmt.Printf("[%s] Failed to get message info from %s: %v\n", direction, addr.String(), err)
		return
	}

	fmt.Printf("[%s] Message from %s: %+v\n", direction, addr.String(), info)
}

// CreateErrorResponse 创建错误响应
func (nh *NetworkHandler) CreateErrorResponse(errorCode uint32, errorMessage string) ([]byte, error) {
	// 确保错误码非0
	if errorCode == 0 {
		errorCode = 1
	}

	// 根据消息类型创建相应的错误响应
	// 这里我们创建一个通用的登录错误响应
	resp := &LoginResponse{
		ErrorCode: errorCode,
		ErrorMsg:  errorMessage,
	}

	message := NewLoginResponseMessage(resp)
	return message.EncodeWithEncryption()
}

// CreateLuckyDrawResponse 创建签到抽奖响应
func (nh *NetworkHandler) CreateLuckyDrawResponse(success bool, errorCode uint32, errorMsg string, message string, count uint32) ([]byte, error) {
	// 如果success为false，确保errorCode非0
	if !success && errorCode == 0 {
		errorCode = 1 // 默认错误码
	}
	// 如果success为true，确保errorCode为0
	if success {
		errorCode = 0
	}

	resp := &LuckyDrawResponse{
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
		Message:   message,
		Count:     count,
	}

	// 创建消息
	msg := NewLuckyDrawResponseMessage(resp)

	// 编码并加密消息
	return msg.EncodeWithEncryption()
}

// CreateLuckyDrawResponseWithPermission 创建包含权限信息的签到抽奖响应
func (nh *NetworkHandler) CreateLuckyDrawResponseWithPermission(success bool, errorCode uint32, errorMsg string, message string, count uint32, permission uint8, permissionExpiration int64) ([]byte, error) {
	// 如果success为false，确保errorCode非0
	if !success && errorCode == 0 {
		errorCode = 1 // 默认错误码
	}
	// 如果success为true，确保errorCode为0
	if success {
		errorCode = 0
	}

	resp := &LuckyDrawResponse{
		ErrorCode:            errorCode,
		ErrorMsg:             errorMsg,
		Message:              message,
		Count:                count,
		Permission:           &permission,
		PermissionExpiration: &permissionExpiration,
	}

	// 创建消息
	msg := NewLuckyDrawResponseMessage(resp)

	// 编码并加密消息
	return msg.EncodeWithEncryption()
}

// CreateLuckyStatusResponse 创建签到状态响应
func (nh *NetworkHandler) CreateLuckyStatusResponse(success bool, errorCode uint32, errorMsg string, point, availableCount uint32) ([]byte, error) {
	// 如果success为false，确保errorCode非0
	if !success && errorCode == 0 {
		errorCode = 1 // 默认错误码
	}
	// 如果success为true，确保errorCode为0
	if success {
		errorCode = 0
	}

	resp := &LuckyStatusResponse{
		ErrorCode:      errorCode,
		ErrorMsg:       errorMsg,
		Point:          point,
		AvailableCount: availableCount,
	}

	// 创建消息
	msg := NewLuckyStatusResponseMessage(resp)

	// 编码并加密消息
	return msg.EncodeWithEncryption()
}

// CreateCDKeyActivateResponse 创建CDKEY激活响应
func (nh *NetworkHandler) CreateCDKeyActivateResponse(success bool, errorCode uint32, errorMsg string, group *int64) ([]byte, error) {
	// 如果success为false，确保errorCode非0
	if !success && errorCode == 0 {
		errorCode = 1 // 默认错误码
	}
	// 如果success为true，确保errorCode为0
	if success {
		errorCode = 0
	}

	resp := &CDKeyActivateResponse{
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
		Group:     group,
	}

	// 创建消息
	msg := NewCDKeyActivateResponseMessage(resp)

	// 编码并加密消息
	return msg.EncodeWithEncryption()
}

// CreateCDKeyActivateSuccessResponse 创建CDKEY激活成功响应（包含权限信息）
func (nh *NetworkHandler) CreateCDKeyActivateSuccessResponse(permission uint8, permissionExpiration int64) ([]byte, error) {
	resp := &CDKeyActivateResponse{
		ErrorCode:            0,
		ErrorMsg:             "",
		Group:                nil,
		Permission:           &permission,
		PermissionExpiration: &permissionExpiration,
	}

	// 创建消息
	msg := NewCDKeyActivateResponseMessage(resp)

	// 编码并加密消息
	return msg.EncodeWithEncryption()
}

// CreateGetActivityInfoResponse 创建获取活动信息响应
func (nh *NetworkHandler) CreateGetActivityInfoResponse(success bool, errorCode uint32, errorMsg string, activityInfo map[string]interface{}) ([]byte, error) {
	// 如果success为false，确保errorCode非0
	if !success && errorCode == 0 {
		errorCode = 1 // 默认错误码
	}
	// 如果success为true，确保errorCode为0
	if success {
		errorCode = 0
	}

	resp := &GetActivityInfoResponse{
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	// 如果成功，填充活动信息
	if success && activityInfo != nil {
		if isActive, ok := activityInfo["is_active"].(bool); ok {
			resp.IsActive = isActive
		}
		if startTime, ok := activityInfo["start_time"].(int64); ok {
			resp.StartTime = uint64(startTime)
		}
		if endTime, ok := activityInfo["end_time"].(int64); ok {
			resp.EndTime = uint64(endTime)
		}
		if signInResumeTime, ok := activityInfo["sign_in_resume_time"].(int64); ok {
			resp.SignInResumeTime = uint64(signInResumeTime)
		}
		if title, ok := activityInfo["title"].(string); ok {
			resp.Title = title
		}
		if description, ok := activityInfo["description"].(string); ok {
			resp.Description = description
		}
		if message, ok := activityInfo["message"].(string); ok {
			resp.Message = message
		}
		if remainingDays, ok := activityInfo["remaining_days"].(int); ok {
			resp.RemainingDays = uint32(remainingDays)
		}
		if remainingHours, ok := activityInfo["remaining_hours"].(int); ok {
			resp.RemainingHours = uint32(remainingHours)
		}
	}

	// 创建消息
	msg := NewGetActivityInfoResponseMessage(resp)

	// 编码并加密消息
	return msg.EncodeWithEncryption()
}

// CreateSuccessResponse 创建成功响应
func (nh *NetworkHandler) CreateSuccessResponse(responseType uint8, data map[string]interface{}) ([]byte, error) {
	switch responseType {
	case MsgTypeLoginResponse:
		return nh.CreateLoginResponse(true, 0, "", data)
	case MsgTypeHeartbeatResponse:
		// 心跳响应使用最小响应包，只包含基本响应字段，不包含业务数据
		return nh.CreateEmptyResponse(MsgTypeHeartbeatResponse)
	case MsgTypeLuckyDrawResponse:
		message := ""
		count := uint32(0)
		if msg, ok := data["message"].(string); ok {
			message = msg
		}
		if c, ok := data["count"].(uint32); ok {
			count = c
		}
		return nh.CreateLuckyDrawResponse(true, 0, "", message, count)
	case MsgTypeLuckyStatusResponse:
		point := uint32(0)
		availableCount := uint32(0)
		if d, ok := data["point"].(uint32); ok {
			point = d
		}
		if a, ok := data["available_count"].(uint32); ok {
			availableCount = a
		}
		return nh.CreateLuckyStatusResponse(true, 0, "", point, availableCount)
	case MsgTypeGetActivityInfoResponse:
		return nh.CreateGetActivityInfoResponse(true, 0, "", data)
	default:
		return nil, fmt.Errorf("unsupported response type: 0x%02X", responseType)
	}
}

// convertToUint64 安全地将interface{}转换为uint64
func convertToUint64(value interface{}) uint64 {
	switch v := value.(type) {
	case uint64:
		return v
	case int64:
		if v >= 0 {
			return uint64(v)
		}
	case int:
		if v >= 0 {
			return uint64(v)
		}
	case uint:
		return uint64(v)
	case uint32:
		return uint64(v)
	case int32:
		if v >= 0 {
			return uint64(v)
		}
	case float64:
		if v >= 0 && v <= float64(^uint64(0)) {
			return uint64(v)
		}
	case float32:
		if v >= 0 && v <= float32(^uint64(0)) {
			return uint64(v)
		}
	}
	return 0
}

// convertToUint8 安全地将interface{}转换为uint8
func convertToUint8(value interface{}) uint8 {
	switch v := value.(type) {
	case uint8:
		return v
	case int8:
		if v >= 0 {
			return uint8(v)
		}
	case int:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case uint:
		if v <= 255 {
			return uint8(v)
		}
	case uint32:
		if v <= 255 {
			return uint8(v)
		}
	case int32:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case uint64:
		if v <= 255 {
			return uint8(v)
		}
	case int64:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case float64:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case float32:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	}
	return 0
}

// convertToUint32 安全地将interface{}转换为uint32
func convertToUint32(value interface{}) uint32 {
	switch v := value.(type) {
	case uint32:
		return v
	case int32:
		if v >= 0 {
			return uint32(v)
		}
	case int:
		if v >= 0 && v <= int(^uint32(0)) {
			return uint32(v)
		}
	case uint:
		if v <= uint(^uint32(0)) {
			return uint32(v)
		}
	case uint64:
		if v <= uint64(^uint32(0)) {
			return uint32(v)
		}
	case int64:
		if v >= 0 && v <= int64(^uint32(0)) {
			return uint32(v)
		}
	case float64:
		if v >= 0 && v <= float64(^uint32(0)) {
			return uint32(v)
		}
	case float32:
		if v >= 0 && v <= float32(^uint32(0)) {
			return uint32(v)
		}
	}
	return 0
}
