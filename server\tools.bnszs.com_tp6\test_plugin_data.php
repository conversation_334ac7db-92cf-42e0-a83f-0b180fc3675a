<?php
/**
 * 测试插件和群组数据获取
 */

echo "=== 测试插件和群组数据获取 ===\n\n";

// 直接使用MySQL连接
try {
    $host = '127.0.0.1';
    $port = 3306;
    $dbname = 'bns';
    $username = 'root';
    $password = 'caf96bf987033318';
    
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ 数据库连接成功\n\n";
    
    // 1. 检查update_config表
    echo "1. 检查update_config表:\n";
    $stmt = $pdo->query("SELECT * FROM update_config WHERE is_active = 1");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($configs)) {
        echo "   ⚠ 没有找到活跃的更新配置\n";
    } else {
        foreach ($configs as $config) {
            echo "   ID: " . $config['id'] . "\n";
            echo "   Name: " . $config['name'] . "\n";
            echo "   Version: " . $config['version'] . "\n";
            echo "   PluginVersion: " . ($config['plugin_version'] ?: 'NULL') . "\n";
            echo "   PluginURL: " . ($config['plugin_url'] ?: 'NULL') . "\n";
            echo "   ExecutablePath: " . $config['executable_path'] . "\n";
            echo "   URL: " . $config['url'] . "\n";
            echo "   IsActive: " . $config['is_active'] . "\n";
            echo "   ---\n";
        }
    }
    
    // 2. 检查bns_whitelist_group表
    echo "\n2. 检查bns_whitelist_group表:\n";
    $stmt = $pdo->query("SELECT * FROM bns_whitelist_group WHERE is_active = 1");
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($groups)) {
        echo "   ⚠ 没有找到活跃的白名单群组\n";
    } else {
        echo "   找到 " . count($groups) . " 个活跃群组:\n";
        foreach ($groups as $group) {
            echo "   ID: " . $group['id'] . "\n";
            echo "   GroupID: " . $group['group_id'] . "\n";
            echo "   IsActive: " . $group['is_active'] . "\n";
            echo "   Description: " . ($group['description'] ?: 'NULL') . "\n";
            echo "   ---\n";
        }
    }
    
    // 3. 模拟Go服务端的查询逻辑
    echo "\n3. 模拟Go服务端查询逻辑:\n";
    $appNames = ['bns-helper', 'BnsHelper', 'bns_helper'];
    $foundConfig = null;
    
    foreach ($appNames as $appName) {
        echo "   尝试查询应用名称: $appName\n";
        $stmt = $pdo->prepare("SELECT * FROM update_config WHERE name = ? AND is_active = 1");
        $stmt->execute([$appName]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($config) {
            echo "   ✓ 找到配置!\n";
            $foundConfig = $config;
            break;
        } else {
            echo "   ✗ 未找到\n";
        }
    }
    
    if ($foundConfig) {
        echo "\n   最终找到的配置:\n";
        echo "   Name: " . $foundConfig['name'] . "\n";
        echo "   PluginVersion: " . ($foundConfig['plugin_version'] ?: 'NULL') . "\n";
        echo "   PluginURL: " . ($foundConfig['plugin_url'] ?: 'NULL') . "\n";
        
        // 模拟群组ID转换
        echo "\n   群组ID转换测试:\n";
        foreach ($groups as $group) {
            $groupId = $group['group_id'];
            if (is_numeric($groupId)) {
                $intGroupId = (int)$groupId;
                echo "   GroupID: $groupId -> $intGroupId ✓\n";
            } else {
                echo "   GroupID: $groupId -> 转换失败 ✗\n";
            }
        }
    } else {
        echo "\n   ✗ 没有找到任何匹配的配置\n";
        echo "   建议:\n";
        echo "   1. 检查数据库中是否有数据\n";
        echo "   2. 确认应用名称是否正确\n";
        echo "   3. 确认is_active字段是否为1\n";
    }
    
} catch (PDOException $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "\n";
    echo "请检查数据库配置和连接信息\n";
} catch (Exception $e) {
    echo "✗ 发生错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
?>
