using System.Text;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;
internal class HeartbeatPacket : BasePacket
{
    #region Response Fields
    /// <summary>
    /// 当前在线用户数量
    /// </summary>
    public uint OnlineUserCount { get; set; }

    /// <summary>
    /// 是否需要强制更新
    /// </summary>
    public bool ForceUpdate { get; set; }
    #endregion

    #region Methods
    public override DataArchiveWriter Create()
    {
        using var writer = new DataArchiveWriter();
        writer.WriteString(Token, Encoding.UTF8);
        writer.WriteString(VersionHelper.InternalVersion.ToString(), Encoding.UTF8); // 添加版本信息
        return writer;
    }

    protected override void ReadResponse(DataArchive reader)
    {
        OnlineUserCount = reader.Read<uint>();
        ForceUpdate = reader.Read<byte>() != 0;

        // 更新服务中的用户数量
        Application.Current.Properties["user-count"] = OnlineUserCount;
    }
    #endregion
}
