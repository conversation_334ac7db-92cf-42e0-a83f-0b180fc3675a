{layout name="manage/template" /}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">添加公告</h5>
                    <a href="/manage/admin/announcement" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
                
                <div class="card-body">
                    <form method="post" id="announcementForm">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- 基本信息 -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">公告标题 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" required maxlength="200" placeholder="请输入公告标题">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="content" class="form-label">公告内容 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="content" name="content" rows="10" required placeholder="请输入公告内容，支持HTML格式"></textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <!-- 设置选项 -->
                                <div class="mb-3">
                                    <label for="type" class="form-label">公告类型</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="1">普通公告</option>
                                        <option value="2">重要公告</option>
                                        <option value="3">紧急公告</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="status" class="form-label">发布状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="0">保存为草稿</option>
                                        <option value="1">立即发布</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="priority" class="form-label">优先级</label>
                                    <input type="number" class="form-control" id="priority" name="priority" value="0" min="0" max="999" placeholder="数字越大优先级越高">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="target_client" class="form-label">目标客户端</label>
                                    <select class="form-select" id="target_client" name="target_client">
                                        <option value="all">全部客户端</option>
                                        <option value="desktop">桌面端</option>
                                        <option value="mobile">移动端</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="version_requirement" class="form-label">版本要求</label>
                                    <input type="text" class="form-control" id="version_requirement" name="version_requirement" placeholder="如：>=1.0.0">
                                    <small class="form-text text-muted">留空表示不限制版本</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">开始显示时间</label>
                                    <input type="datetime-local" class="form-control" id="start_time" name="start_time">
                                    <small class="form-text text-muted">留空表示立即显示</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">结束显示时间</label>
                                    <input type="datetime-local" class="form-control" id="end_time" name="end_time">
                                    <small class="form-text text-muted">留空表示永久显示</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-secondary" onclick="previewAnnouncement()">预览</button>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">取消</button>
                                        <button type="submit" class="btn btn-primary">保存公告</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">公告预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <h4 id="previewTitle"></h4>
                    <div class="mb-2">
                        <span id="previewType" class="badge"></span>
                        <span id="previewTarget" class="badge bg-secondary"></span>
                        <span id="previewPriority" class="badge bg-info"></span>
                    </div>
                    <div id="previewBody"></div>
                    <hr>
                    <small class="text-muted">
                        <div>显示时间：<span id="previewTime"></span></div>
                        <div>版本要求：<span id="previewVersion"></span></div>
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 表单提交
document.getElementById('announcementForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // 验证必填字段
    if (!formData.get('title').trim()) {
        layer.msg('请输入公告标题', {icon: 2});
        return;
    }
    
    if (!formData.get('content').trim()) {
        layer.msg('请输入公告内容', {icon: 2});
        return;
    }
    
    // 验证时间逻辑
    const startTime = formData.get('start_time');
    const endTime = formData.get('end_time');
    if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
        layer.msg('结束时间必须大于开始时间', {icon: 2});
        return;
    }
    
    // 提交表单
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.textContent = '保存中...';
    
    $.ajax({
        url: '/manage/admin/announcement/add',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => {
                    window.location.href = '/manage/admin/announcement';
                }, 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
                submitBtn.disabled = false;
                submitBtn.textContent = '保存公告';
            }
        },
        error: function() {
            layer.msg('网络错误，请重试', {icon: 2});
            submitBtn.disabled = false;
            submitBtn.textContent = '保存公告';
        }
    });
});

// 预览功能
function previewAnnouncement() {
    const title = document.getElementById('title').value;
    const content = document.getElementById('content').value;
    const type = document.getElementById('type').value;
    const targetClient = document.getElementById('target_client').value;
    const priority = document.getElementById('priority').value;
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    const versionRequirement = document.getElementById('version_requirement').value;
    
    if (!title.trim()) {
        layer.msg('请先输入公告标题', {icon: 2});
        return;
    }
    
    if (!content.trim()) {
        layer.msg('请先输入公告内容', {icon: 2});
        return;
    }
    
    // 设置预览内容
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewBody').innerHTML = content;
    
    // 设置类型标签
    const typeText = type == '1' ? '普通公告' : (type == '2' ? '重要公告' : '紧急公告');
    const typeClass = type == '1' ? 'bg-secondary' : (type == '2' ? 'bg-warning' : 'bg-danger');
    document.getElementById('previewType').textContent = typeText;
    document.getElementById('previewType').className = 'badge ' + typeClass;
    
    // 设置目标客户端
    const targetText = targetClient == 'all' ? '全部客户端' : (targetClient == 'desktop' ? '桌面端' : '移动端');
    document.getElementById('previewTarget').textContent = targetText;
    
    // 设置优先级
    document.getElementById('previewPriority').textContent = '优先级: ' + (priority || '0');
    
    // 设置时间信息
    let timeText = '';
    if (startTime && endTime) {
        timeText = startTime + ' 至 ' + endTime;
    } else if (startTime) {
        timeText = '从 ' + startTime + ' 开始';
    } else if (endTime) {
        timeText = '至 ' + endTime + ' 结束';
    } else {
        timeText = '永久显示';
    }
    document.getElementById('previewTime').textContent = timeText;
    
    // 设置版本要求
    document.getElementById('previewVersion').textContent = versionRequirement || '无限制';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// 设置默认时间
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认开始时间为当前时间
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    document.getElementById('start_time').value = localDateTime;
});
</script>
