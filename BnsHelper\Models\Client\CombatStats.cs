using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Serilog;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using Xylia.BnsHelper.Common.Converters;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.BnsHelper.ViewModels;
using Xylia.Preview.Data.Models.Sequence;
using static Xylia.Preview.Data.Models.BattleMessage;

namespace Xylia.BnsHelper.Models;

/// <summary>
/// 技能统计数据
/// </summary>
internal class SkillStats : ObservableObject
{
    #region Properties
    public CreatureStats? Owner { get; set; }
    public string Name { get; set; } = string.Empty;
    public string TargetName { get; set; } = string.Empty;
    public bool Summoned { get; set; } = false;  // 标识是否为召唤物的技能

    // 伤害统计
    public long TotalDamage { get; set; }
    public int HitCount { get; set; }
    public int CriticalHitCount { get; set; }
    public int DodgeCount { get; set; }
    public int ParryCount { get; set; }
    public int MissCount { get; set; }

    // 时间统计
    public DateTime? StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public double Seconds => StartTime.HasValue ? Math.Max(1.0, Math.Round((EndTime - StartTime.Value).TotalSeconds, 0)) : 0;

    // 计算属性
    public double CriticalRate => HitCount > 0 ? (double)CriticalHitCount / HitCount : 0;
    public long DamagePerHit => HitCount > 0 ? TotalDamage / HitCount : 0;
    public long DamagePerSec => Seconds > 0 ? (long)(TotalDamage / Seconds) : 0;
    public double TotalRate
    {
        get
        {
            if (Owner == null) return 0;

            // 根据当前统计类型确定总量基准
            var statisticsType = Owner.Owner?.StatisticsType ?? StatisticsType.Damage;
            long totalBase = statisticsType switch
            {
                StatisticsType.DamageTaken => Owner.TotalDamageTaken,
                StatisticsType.Healing => Owner.TotalHealingDone,
                _ => Owner.TotalDamageDealt
            };

            // 如果目标名称是NULL，表示聚合统计，计算占对应总量的百分比
            if (string.IsNullOrEmpty(TargetName))
            {
                return totalBase > 0 ? (double)TotalDamage / totalBase : 0;
            }

            // 如果是特定目标，计算占该目标对应类型总量的百分比
            var targetTotal = statisticsType switch
            {
                StatisticsType.DamageTaken => Owner.Owner?.GetTotalDamageTakenForTarget(TargetName) ?? 0,
                StatisticsType.Healing => Owner.Owner?.GetTotalHealingForTarget(TargetName) ?? 0,
                _ => Owner.Owner?.GetTotalDamageForTarget(TargetName) ?? 0
            };
            return targetTotal > 0 ? (double)TotalDamage / targetTotal : 0;
        }
    }
    #endregion

    #region Methods
    public void AddHit(InstantEffectNotification2 item, long damage)
    {
        // 更新时间
        EndTime = item.Time;
        StartTime ??= item.Time;
        TotalDamage += damage;

        // 如果是物品技能，不记录伤害次数
        if (item.EffectAlias != null && item.EffectAlias.StartsWith("ItemSkill_", StringComparison.OrdinalIgnoreCase)) return;

        HitCount++;
        switch (item.SkillResultType)
        {
            case SkillResultTypeSeq.Cri:
                CriticalHitCount++;
                break;
            case SkillResultTypeSeq.Dodge:
                DodgeCount++;
                break;
            case SkillResultTypeSeq.Parry:
            case SkillResultTypeSeq.Pparry:
                ParryCount++;
                break;
            case SkillResultTypeSeq.Miss:
                MissCount++;
                break;
        }
    }

    public void Refresh()
    {
        OnPropertyChanged(nameof(TotalDamage));
        OnPropertyChanged(nameof(HitCount));
        OnPropertyChanged(nameof(CriticalHitCount));
        OnPropertyChanged(nameof(DodgeCount));
        OnPropertyChanged(nameof(ParryCount));
        OnPropertyChanged(nameof(MissCount));

        OnPropertyChanged(nameof(CriticalRate));
        OnPropertyChanged(nameof(DamagePerHit));
        OnPropertyChanged(nameof(DamagePerSec));
        OnPropertyChanged(nameof(TotalRate));
        OnPropertyChanged(nameof(Seconds));
    }
    #endregion
}

/// <summary>
/// 玩家统计数据
/// </summary>
[JsonConverter(typeof(CreatureStatsConverter))]
internal partial class CreatureStats : Collection<InstantEffectNotification2>, INotifyPropertyChanged
{
    #region Constructor
    public CreatureStats(CombatCollection? owner, long playerId, string? name, JobSeq job = JobSeq.JobNone, bool self = false, bool skipInitialUpdate = false)
    {
        Owner = owner;
        PlayerId = playerId;
        Name = name;
        Job = job;
        Self = self;

        // 初始化技能显示（除非明确跳过）
        if (!skipInitialUpdate)
        {
            UpdateSkillsDisplay();
        }
    }
    #endregion

    #region Fields
    public CombatCollection? Owner { get; set; }
    public long PlayerId { get; set; }
    public string? Name { get; set; }
    public JobSeq Job { get; set; }
    public bool Self { get; set; }

    // 时间统计
    public DateTime? StartTime { get; private set; }
    public DateTime EndTime { get; private set; }
    public double Seconds => GetFilteredSeconds();

    /// <summary>
    /// 根据当前目标过滤器计算过滤后的战斗时间
    /// </summary>
    private double GetFilteredSeconds()
    {
        return Owner?.GetFilteredSeconds(this) ?? 0;
    }

    // 伤害输出统计
    private readonly Dictionary<string, SkillStats> _damageSkills = [];
    public ObservableCollection<SkillStats> DamageSkills { get; } = [];

    // 聚合的伤害输出统计（按技能名称聚合，不区分目标）
    private readonly Dictionary<string, SkillStats> _aggregatedDamageSkills = [];
    public ObservableCollection<SkillStats> AggregatedDamageSkills { get; } = [];

    // 当前显示的技能集合（根据目标过滤器动态更新）
    public ObservableCollection<SkillStats> Skills { get; } = [];

    // 承伤统计
    private readonly Dictionary<string, SkillStats> _takenDamageSkills = [];
    public ObservableCollection<SkillStats> TakenDamageSkills { get; } = [];

    // 治疗统计
    private readonly Dictionary<string, SkillStats> _healSkills = [];
    public ObservableCollection<SkillStats> HealSkills { get; } = [];

    // 内力统计
    private readonly Dictionary<string, SkillStats> _spSkills = [];
    public ObservableCollection<SkillStats> SpSkills { get; } = [];

    // 状态统计
    public int ExhaustionCount { get; private set; }  // 濒死次数
    public int DeathCount { get; private set; }       // 死亡次数
    #endregion

    #region Properties
    public bool Mode => Owner?.Count > 1 && IsDamageOutputType;

    // 伤害输出统计
    public long TotalDamageDealt => GetFilteredDamage(DamageSkills);
    public long DamagePerSec => Seconds > 0 ? (long)(TotalDamageDealt / Seconds) : 0;
    public double DamageRate => Owner?.TotalDamage > 0 ? (double)TotalDamageDealt / Owner.TotalDamage : 0;
    public double DamageRate2 => Owner?.MaxDamage > 0 ? (double)TotalDamageDealt / Owner.MaxDamage : 1;

    // 承伤统计
    public long TotalDamageTaken => GetFilteredDamage(TakenDamageSkills);
    public long DamageTakenPerSec => Seconds > 0 ? (long)(TotalDamageTaken / Seconds) : 0;

    // 治疗统计
    public long TotalHealingDone => GetFilteredDamage(HealSkills); // 治疗使用 TotalDamage 属性存储
    public long HealingPerSec => Seconds > 0 ? (long)(TotalHealingDone / Seconds) : 0;

    // 内力统计
    public long TotalSpRestored => GetFilteredDamage(SpSkills); // 内力恢复使用 TotalDamage 属性存储
    public long SpPerSec => Seconds > 0 ? (long)(TotalSpRestored / Seconds) : 0;

    /// <summary>
    /// 根据当前统计类型获取右侧显示文本（用于DamageMeterPanel）
    /// </summary>
    public string PerSecondDisplayText
    {
        get
        {
            var statisticsType = Owner?.StatisticsType ?? StatisticsType.Damage;
            return statisticsType switch
            {
                StatisticsType.DamageTaken => $"总承伤 {TotalDamageTaken}",
                StatisticsType.Healing => $"总恢复 {TotalHealingDone}",
                _ => $"{DamagePerSec}/秒"
            };
        }
    }

    /// <summary>
    /// 根据当前统计类型获取总量的显示文本
    /// </summary>
    public string TotalValueDisplayText
    {
        get
        {
            var statisticsType = Owner?.StatisticsType ?? StatisticsType.Damage;
            return statisticsType switch
            {
                StatisticsType.DamageTaken => $"总承伤值 {TotalDamageTaken}",
                StatisticsType.Healing => $"总恢复值 {TotalHealingDone}",
                _ => $"累积伤害量 {TotalDamageDealt}"
            };
        }
    }

    /// <summary>
    /// 根据当前目标过滤器计算过滤后的伤害总量
    /// </summary>
    private long GetFilteredDamage(ObservableCollection<SkillStats> skills)
    {
        return Owner?.GetFilteredDamage(skills) ?? 0;
    }

    // 防御统计（基于当前聚合技能计算）
    public int TotalHitEvents => GetFilteredHitCount();
    public double DodgeRate => TotalHitEvents > 0 ? (double)GetFilteredDodgeCount() / TotalHitEvents : 0;
    public double ParryRate => TotalHitEvents > 0 ? (double)GetFilteredParryCount() / TotalHitEvents : 0;

    /// <summary>
    /// 获取当前过滤条件下的总闪避次数
    /// </summary>
    private int GetFilteredDodgeCount()
    {
        return Skills.Sum(s => s.DodgeCount);
    }

    /// <summary>
    /// 获取当前过滤条件下的总格挡次数
    /// </summary>
    private int GetFilteredParryCount()
    {
        return Skills.Sum(s => s.ParryCount);
    }

    /// <summary>
    /// 获取当前过滤条件下的总Miss次数
    /// </summary>
    private int GetFilteredMissCount()
    {
        return Skills.Sum(s => s.MissCount);
    }

    /// <summary>
    /// 获取当前过滤条件下的总命中次数
    /// </summary>
    private int GetFilteredHitCount()
    {
        return Skills.Sum(s => s.HitCount);
    }

    /// <summary>
    /// 是否显示每秒伤害
    /// </summary>
    public bool DisplayPerSecondValue => Self && IsDamageOutputType;

    /// <summary>
    /// 根据当前统计类型获取每秒数值文本（用于DamageMeterTooltipPanel，不带/秒后缀）
    /// </summary>
    public string PerSecondValueText
    {
        get
        {
            var statisticsType = Owner?.StatisticsType ?? StatisticsType.Damage;
            return statisticsType switch
            {
                StatisticsType.DamageTaken => DamageTakenPerSec.ToString(),
                StatisticsType.Healing => HealingPerSec.ToString(),
                _ => DamagePerSec.ToString()
            };
        }
    }

    /// <summary>
    /// 检查当前是否为伤害输出统计类型
    /// </summary>
    public bool IsDamageOutputType => (Owner?.StatisticsType ?? StatisticsType.Damage) == StatisticsType.Damage;

    /// <summary>
    /// 检查当前是否为承伤统计类型
    /// </summary>
    public bool IsDamageTakenType => (Owner?.StatisticsType ?? StatisticsType.Damage) == StatisticsType.DamageTaken;

    /// <summary>
    /// 检查当前是否为治疗统计类型
    /// </summary>
    public bool IsHealingType => (Owner?.StatisticsType ?? StatisticsType.Damage) == StatisticsType.Healing;


    /// <summary>
    /// 更新Skills集合以反映当前的目标过滤器和统计类型
    /// </summary>
    public void UpdateSkillsDisplay(StatisticsType statisticsType = StatisticsType.Damage)
    {
        Skills.Clear();

        var targetFilter = Owner?.TargetFilter;

        // 根据统计类型选择对应的技能集合
        ObservableCollection<SkillStats> sourceSkills;
        ObservableCollection<SkillStats> aggregatedSkills;

        switch (statisticsType)
        {
            case StatisticsType.DamageTaken:
                sourceSkills = TakenDamageSkills;
                aggregatedSkills = new ObservableCollection<SkillStats>(); // 承伤没有预聚合集合，需要动态聚合
                break;
            case StatisticsType.Healing:
                sourceSkills = HealSkills;
                aggregatedSkills = new ObservableCollection<SkillStats>(); // 治疗没有预聚合集合，需要动态聚合
                break;
            default: // StatisticsType.Damage
                sourceSkills = DamageSkills;
                aggregatedSkills = AggregatedDamageSkills;
                break;
        }

        // 如果没有目标过滤器，显示聚合的技能统计
        if (string.IsNullOrEmpty(targetFilter))
        {
            if (statisticsType == StatisticsType.Damage && aggregatedSkills.Count > 0)
            {
                // 伤害输出有预聚合数据
                foreach (var skill in aggregatedSkills.OrderByDescending(s => s.TotalDamage))
                {
                    Skills.Add(skill);
                }
            }
            else
            {
                // 承伤和治疗需要动态聚合
                var dynamicAggregated = sourceSkills
                    .GroupBy(s => new { s.Name, s.Summoned })
                    .Select(g => CreateAggregatedSkill(g, string.Empty))
                    .OrderByDescending(s => s.TotalDamage);

                foreach (var skill in dynamicAggregated)
                {
                    Skills.Add(skill);
                }
            }
            return;
        }

        // 如果有特定目标过滤器，显示该目标的技能统计（按技能名称聚合）
        var targetSkills = sourceSkills.Where(s => s.TargetName == targetFilter);

        var aggregatedTargetSkills = targetSkills
            .GroupBy(s => new { s.Name, s.Summoned })
            .Select(g => CreateAggregatedSkill(g, targetFilter))
            .OrderByDescending(s => s.TotalDamage);

        foreach (var skill in aggregatedTargetSkills)
        {
            Skills.Add(skill);
        }

        // 通知防御统计属性变化
        OnPropertyChanged(nameof(DodgeRate));
        OnPropertyChanged(nameof(ParryRate));
        OnPropertyChanged(nameof(TotalHitEvents));
    }

    /// <summary>
    /// 创建聚合的技能统计
    /// </summary>
    private SkillStats CreateAggregatedSkill(IGrouping<dynamic, SkillStats> group, string targetName)
    {
        var first = group.First();
        var aggregated = new SkillStats
        {
            Name = first.Name,
            TargetName = targetName,
            Owner = this,
            Summoned = first.Summoned,
            TotalDamage = group.Sum(s => s.TotalDamage),
            HitCount = group.Sum(s => s.HitCount),
            CriticalHitCount = group.Sum(s => s.CriticalHitCount),
            DodgeCount = group.Sum(s => s.DodgeCount),
            ParryCount = group.Sum(s => s.ParryCount),
            MissCount = group.Sum(s => s.MissCount)
        };

        // 设置时间信息
        var startTimes = group.Where(s => s.StartTime.HasValue).Select(s => s.StartTime!.Value);
        var endTimes = group.Select(s => s.EndTime);

        if (startTimes.Any())
        {
            aggregated.StartTime = startTimes.Min();
            aggregated.EndTime = endTimes.Max();
        }

        return aggregated;
    }

    #endregion

    #region Methods
    protected override void InsertItem(int index, InstantEffectNotification2 item)
    {
        base.InsertItem(index, item);

        // 更新时间
        EndTime = item.Time;
        StartTime ??= item.Time;

        // 根据事件类型处理数据
        switch (item.ObjectType)
        {
            case ObjectTypeSeq.PlayerAttack:
            case ObjectTypeSeq.Other when item.CasterId == PlayerId || IsSummonedAttack(item.CasterId):
                ProcessCombatEvent(item, true);
                break;
            case ObjectTypeSeq.PlayerAttacked:
            case ObjectTypeSeq.Other when item.TargetId == PlayerId || IsSummonedTarget(item.TargetId):
                ProcessCombatEvent(item, false);
                break;
        }
    }

    /// <summary>
    /// 检查是否为当前玩家的召唤兽攻击
    /// </summary>
    private bool IsSummonedAttack(long casterId)
    {
        // 检查施法者是否为当前玩家的召唤兽
        bool isSummoned = Owner?.IsSummonedBy(casterId, PlayerId) == true;
        if (isSummoned)
        {
            Debug.WriteLine($"[召唤兽检查] 施法者 {casterId} 是玩家 {Name}({PlayerId}) 的召唤兽");
        }
        return isSummoned;
    }

    /// <summary>
    /// 检查是否为当前玩家的召唤兽被攻击
    /// </summary>
    private bool IsSummonedTarget(long targetId)
    {
        // 检查目标是否为当前玩家的召唤兽
        bool isSummoned = Owner?.IsSummonedBy(targetId, PlayerId) == true;
        if (isSummoned)
        {
            Debug.WriteLine($"[召唤兽检查] 目标 {targetId} 是玩家 {Name}({PlayerId}) 的召唤兽");
        }
        return isSummoned;
    }

    /// <summary>
    /// 处理战斗事件（统一处理伤害输出和承受伤害）
    /// </summary>
    /// <param name="item">战斗事件</param>
    /// <param name="isOutputEvent">是否为输出事件（true=攻击方视角，false=被攻击方视角）</param>
    private void ProcessCombatEvent(InstantEffectNotification2 item, bool isOutputEvent)
    {
        // 检查是否为召唤物技能（通过EffectAlias判断）
        bool isSummoned = item.EffectAlias?.EndsWith("_SUMMONED") == true;

        // 调试输出
        if (isSummoned)
        {
            Debug.WriteLine($"[战斗事件] 玩家 {Name}({PlayerId}) 处理召唤兽技能: {item.Name}, 输出事件: {isOutputEvent}, 伤害: {item.Value}");
        }

        // 处理治疗和内力恢复事件
        switch (item.EffectType)
        {
            case EffectTypeSeq.InstantHp:
            case EffectTypeSeq.IntervalHp:
                ProcessSkillStats(item, item.Value, item.TargetName, _healSkills, HealSkills, isSummoned);
                return;

            case EffectTypeSeq.InstantSp:
            case EffectTypeSeq.IntervalSp:
                ProcessSkillStats(item, item.Value, item.TargetName, _spSkills, SpSkills, isSummoned);
                return;

            // 跳过效果附加
            case EffectTypeSeq.Attach:
            case EffectTypeSeq.AttachFail:
            case EffectTypeSeq.Detach:
                return;
        }

        // 处理伤害事件
        if (isOutputEvent)
        {
            ProcessSkillStats(item, item.Value, item.TargetName, _damageSkills, DamageSkills, isSummoned);

            if (item.Value2 > 0)
            {
                ProcessSkillStats(item, item.Value2, item.TargetName, _healSkills, HealSkills, isSummoned);
            }
        }
        else
        {
            ProcessSkillStats(item, item.Value, item.CasterName, _takenDamageSkills, TakenDamageSkills, isSummoned);

            switch (item.EffectType)
            {
                case EffectTypeSeq.Exhaustion:
                    ExhaustionCount++;
                    OnPropertyChanged(nameof(ExhaustionCount));
                    break;
                case EffectTypeSeq.Dead:
                    DeathCount++;
                    OnPropertyChanged(nameof(DeathCount));
                    break;
            }
        }
    }

    /// <summary>
    /// 处理技能统计（通用方法）
    /// </summary>
    private void ProcessSkillStats(InstantEffectNotification2 item, long value, string? target, Dictionary<string, SkillStats> skillDict, ObservableCollection<SkillStats> skillCollection, bool isSummoned = false)
    {
        string skillKey = isSummoned ? $"{item.Name}_{target}_SUMMONED" : $"{item.Name}_{target}";

        if (!skillDict.TryGetValue(skillKey, out var skill))
        {
            skill = new SkillStats
            {
                Owner = this,
                Name = item.Name,
                TargetName = target,
                Summoned = isSummoned
            };
            skillDict[skillKey] = skill;
            skillCollection.Add(skill);
        }

        skill.AddHit(item, value);

        // 如果是伤害输出统计，同时更新聚合统计
        if (skillDict == _damageSkills)
        {
            UpdateAggregatedSkillStats(item, value, item.Name, isSummoned);
            // 伤害技能更新时使目标缓存失效
            Owner?.InvalidateTargetCache();
        }
    }

    /// <summary>
    /// 更新聚合的技能统计（按技能名称聚合，不区分目标）
    /// </summary>
    private void UpdateAggregatedSkillStats(InstantEffectNotification2 item, long value, string name, bool isSummoned = false)
    {
        // 构建聚合技能key，只包含技能名称，不包含目标信息
        string aggregatedSkillKey = isSummoned ? $"{name}_SUMMONED" : name;

        if (!_aggregatedDamageSkills.TryGetValue(aggregatedSkillKey, out var aggregatedSkill))
        {
            aggregatedSkill = new SkillStats
            {
                Name = name,
                TargetName = string.Empty, // 聚合统计的目标名称为空字符串
                Owner = this,
                Summoned = isSummoned
            };
            _aggregatedDamageSkills[aggregatedSkillKey] = aggregatedSkill;
            AggregatedDamageSkills.Add(aggregatedSkill);
        }

        aggregatedSkill.AddHit(item, value);
    }

    /// <summary>
    /// 刷新所有统计数据
    /// </summary>
    public void Refresh()
    {
        if (!SettingHelper.Default.EncounterMode)
            EndTime = DateTime.Now;

        // 刷新属性统计
        OnPropertyChanged(nameof(Mode));
        OnPropertyChanged(nameof(Seconds));
        OnPropertyChanged(nameof(TotalDamageDealt));
        OnPropertyChanged(nameof(TotalDamageTaken));
        OnPropertyChanged(nameof(TotalHealingDone));
        OnPropertyChanged(nameof(DamagePerSec));
        OnPropertyChanged(nameof(DamageTakenPerSec));
        OnPropertyChanged(nameof(HealingPerSec));
        OnPropertyChanged(nameof(PerSecondDisplayText));
        OnPropertyChanged(nameof(TotalValueDisplayText));
        OnPropertyChanged(nameof(PerSecondValueText));
        OnPropertyChanged(nameof(IsDamageOutputType));
        OnPropertyChanged(nameof(DisplayPerSecondValue));
        OnPropertyChanged(nameof(DamageRate));
        OnPropertyChanged(nameof(DamageRate2));
        OnPropertyChanged(nameof(DodgeRate));
        OnPropertyChanged(nameof(ParryRate));

        // 技能统计会在访问时自动计算，不需要手动刷新
        UpdateSkillsDisplay(Owner?.StatisticsType ?? StatisticsType.Damage);
    }

    [RelayCommand]
    void ToWorld()
    {
        if (Name is null) return;

        WindowHelper.SendMessage(DamageMeterViewModel.gHwnd, $"<font name=\"00008130.UI.Hypertext_PC\"><link id='pc:{Name}'>{Name}</link></font>");
    }
    #endregion

    #region Interface
    public event PropertyChangedEventHandler? PropertyChanged;

    public void OnPropertyChanged(string name)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    /// <summary>
    /// 批量通知属性变化，减少PropertyChanged事件的触发次数
    /// </summary>
    public void BatchNotifyPropertiesChanged(params string[] propertyNames)
    {
        if (PropertyChanged != null)
        {
            foreach (var propertyName in propertyNames)
            {
                PropertyChanged.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }
    #endregion
}

/// <summary>
/// 战斗记录系统的核心集合类 - 管理所有玩家的战斗数据
/// </summary>
[JsonConverter(typeof(CombatCollectionConverter))]
internal class CombatCollection : Collection<CreatureStats>, INotifyPropertyChanged
{
    #region Fields
    internal bool IsHistory;

    // 玩家对象缓存 - 按ID快速查找
    internal readonly Dictionary<long, CreatureStats> _playersByPlayerId = [];

    // 玩家信息缓存 - 从 InstantEffectNotification 获取
    internal readonly ConcurrentDictionary<long, Creature> _creatureInfoById = [];

    // 召唤物关系映射 - 召唤兽 -> 召唤师
    internal readonly Dictionary<long, long> _summonedToSummoner = [];

    // 目标统计缓存 - 避免频繁的LINQ查询
    private readonly Dictionary<string, long> _targetDamageCache = [];
    private HashSet<string>? _targetNamesCache;
    private bool _targetCacheValid = false;

    // 时间统计
    public DateTime? StartTime { get; private set; }
    public DateTime EndTime { get; private set; }
    public DateTime LastCombatTime { get; private set; }
    public TimeSpan TimeSpan => StartTime.HasValue ? (EndTime - StartTime.Value) : TimeSpan.Zero;

    // 伤害统计
    public long TotalDamage => this.Sum(x => x.TotalDamageDealt);
    public long MaxDamage => Count == 0 ? 0 : this.Max(x => x.TotalDamageDealt);
    public IEnumerable? View
    {
        get
        {
            if (Count == 0) return null;

            var players = this.Where(x => x.Job != JobSeq.JobNone);

            // Apply target filtering if a specific target is selected
            if (TargetFilter != null && !string.IsNullOrEmpty(TargetFilter))
            {
                // Check if it's a specific target (not the "all targets" option)
                bool isAllTargets = TargetFilter.Contains(',') || TargetFilter.Contains("全部目标");
                if (!isAllTargets)
                {
                    // Filter players who have damage to this specific target (只考虑正数伤害)
                    players = players.Where(p => GetTotalDamageForTarget(TargetFilter) > 0 &&
                                                 p.DamageSkills.Any(s => s.TargetName == TargetFilter && s.TotalDamage > 0));
                }
            }

            return players.OrderByDescending(x => x.TotalDamageDealt);
        }
    }

    // Target filter property
    private string? _targetFilter;
    public string? TargetFilter
    {
        get => _targetFilter;
        set
        {
            if (_targetFilter != value)
            {
                _targetFilter = value;
                OnPropertyChanged(nameof(TargetFilter));
            }
        }
    }

    // Statistics type property
    private StatisticsType _statisticsType = StatisticsType.Damage;
    public StatisticsType StatisticsType
    {
        get => _statisticsType;
        set
        {
            if (_statisticsType != value)
            {
                _statisticsType = value;
                OnPropertyChanged(nameof(StatisticsType));

                // 批量更新所有玩家的技能显示和统计数据
                foreach (var player in this)
                {
                    player.UpdateSkillsDisplay(value);

                    // 批量通知属性变化，减少单独的PropertyChanged调用
                    player.BatchNotifyPropertiesChanged(
                        nameof(player.PerSecondDisplayText),
                        nameof(player.TotalValueDisplayText),
                        nameof(player.PerSecondValueText),
                        nameof(player.DisplayPerSecondValue),
                        nameof(player.IsDamageOutputType),
                        nameof(player.IsDamageTakenType),
                        nameof(player.IsHealingType),
                        nameof(player.TotalSpRestored),
                        nameof(player.SpPerSec)
                    );
                }
            }
        }
    }

    private CreatureStats? _default;
    public CreatureStats? Default
    {
        get => _default;
        set
        {
            _default = value;
            OnPropertyChanged(nameof(Default));
        }
    }

    /// <summary>
    /// 所有目标死亡事件
    /// </summary>
    public event Action? AllTargetsDead;
    #endregion

    #region Methods
    protected override void RemoveItem(int index)
    {
        var player = this[index];

        // 从缓存中移除
        _playersByPlayerId.Remove(player.PlayerId);

        base.RemoveItem(index);
    }

    protected override void ClearItems()
    {
        IsHistory = false;

        // 保存当前默认玩家的信息
        string? savedName = _default?.Name;
        JobSeq savedJob = _default?.Job ?? JobSeq.PcMax;
        long savedPlayerId = _default?.PlayerId ?? 0;

        // 清理所有缓存
        _default = null;
        StartTime = null;
        EndTime = default;
        LastCombatTime = default;
        _playersByPlayerId.Clear();
        _summonedToSummoner.Clear();
        InvalidateTargetCache(); // 清理目标统计缓存
        // 注意：不清空玩家信息缓存，因为这些信息在重置后仍然有效
        // _creatureInfoById.Clear();
        base.ClearItems();

        // 重新创建默认玩家
        string playerName = !string.IsNullOrEmpty(savedName) ? savedName : string.Empty;
        Default = new CreatureStats(this, savedPlayerId, playerName, savedJob, true);

        // 将默认玩家添加到缓存中，避免重复创建
        // 只有当PlayerId不为0时才添加到缓存，避免ID为0时的冲突
        if (savedPlayerId != 0)
        {
            _playersByPlayerId[savedPlayerId] = Default;
        }

        Add(Default);

        Refresh();
    }

    /// <summary>
    /// 更新默认玩家信息
    /// </summary>
    public void UpdateDefaultPlayer(long playerId, string name, JobSeq job)
    {
        if (Default != null)
        {
            // 从旧的缓存中移除
            if (Default.PlayerId != 0)
            {
                _playersByPlayerId.Remove(Default.PlayerId);
            }

            // 更新玩家信息
            Default.PlayerId = playerId;
            Default.Name = name;
            Default.Job = job;

            // 添加到新的缓存
            if (playerId != 0)
            {
                // 检查是否已经存在相同ID的玩家，如果存在则不添加，避免重复
                if (!_playersByPlayerId.ContainsKey(playerId))
                {
                    _playersByPlayerId[playerId] = Default;
                }
                else
                {
                    // 如果已存在，说明可能有重复创建的问题，记录调试信息
                    System.Diagnostics.Debug.WriteLine($"[CombatStats] 警告：尝试更新默认玩家时发现ID {playerId} 已存在于缓存中");
                }
            }
        }
    }

    public void Add(InstantEffectNotification item)
    {
        if (item.Player is null || item.Player.job == 0) return;

        // 缓存玩家信息，用于后续创建 CreatureStats 对象
        _creatureInfoById.TryAdd(item.Player.Id, item.Player);

        // 建立召唤物关系映射
        if (item.Player.Summoned != 0)
        {
            // 通过Job判断是召唤师还是召唤兽
            if (item.Player.Job > JobSeq.PcMax)
            {
                // 这是召唤兽，Summoned字段指向召唤师
                _summonedToSummoner[item.Player.Id] = item.Player.Summoned;
                Debug.WriteLine($"[召唤物映射] 召唤兽: {item.Player.Name}({item.Player.Id}) -> 召唤师: ({item.Player.Summoned})");
            }
            else
            {
                // 这是召唤师，Summoned字段指向召唤兽
                _summonedToSummoner[item.Player.Summoned] = item.Player.Id;
                Debug.WriteLine($"[召唤物映射] 召唤师: {item.Player.Name}({item.Player.Id}) -> 召唤兽: ({item.Player.Summoned})");
            }
        }
    }

    /// <summary>
    /// 添加战斗事件
    /// </summary>
    public void Add(InstantEffectNotification2 instant)
    {
        // 更新全局时间
        EndTime = LastCombatTime = instant.Time;
        StartTime ??= instant.Time;

        // 检查死亡事件
        if (instant.EffectType == EffectTypeSeq.Dead)
        {
            if (!string.IsNullOrEmpty(instant.TargetName))
            {
                CheckTargetDeath(instant.TargetName);
            }
            return; // 死亡事件不需要进一步处理
        }

        // 根据事件类型分别处理
        switch (instant.ObjectType)
        {
            case ObjectTypeSeq.PlayerAttack:
                // 玩家攻击事件：处理攻击方数据
                ProcessCasterEvent(instant);
                // 如果目标是其他玩家，也需要处理被攻击方数据
                if (instant.TargetId != 0 && instant.TargetId != instant.CasterId)
                {
                    ProcessTargetEvent(instant);
                }
                break;

            case ObjectTypeSeq.PlayerAttacked:
                // 玩家被攻击事件：直接处理被攻击方数据，不需要转换
                ProcessPlayerAttackedEvent(instant);
                break;

            case ObjectTypeSeq.Other:
                // 第三人称事件：根据玩家ID判断处理方式
                ProcessCasterEvent(instant);
                ProcessTargetEvent(instant);
                break;
        }
    }

    /// <summary>
    /// 处理攻击方事件
    /// </summary>
    private void ProcessCasterEvent(InstantEffectNotification2 instant)
    {
        CreatureStats? casterPlayer;
        bool isSummonedAttack;

        // 检查是否为召唤物攻击
        if (_summonedToSummoner.TryGetValue(instant.CasterId, out var summonerId))
        {
            // 这是召唤物的攻击，将伤害合并到召唤师
            casterPlayer = GetOrCreatePlayer(summonerId, null);
            isSummonedAttack = true;

            // 调试输出：召唤兽攻击事件
            Debug.WriteLine($"[召唤兽攻击] 召唤兽: {instant.CasterName}({instant.CasterId}) -> 召唤师: {casterPlayer?.Name}({summonerId}), 技能: {instant.Name}, 目标: {instant.TargetName}, 伤害: {instant.Value}");
        }
        else
        {
            // 普通玩家攻击（包括召唤师自己的技能）
            casterPlayer = GetOrCreatePlayer(instant.CasterId, instant.CasterName);
            isSummonedAttack = false;
        }

        // 添加事件到施法者
        if (casterPlayer != null)
        {
            // 检查目标是否为已知玩家（队友），如果是队友则不记录为攻击目标
            bool isTargetKnownPlayer = IsKnownPlayer(instant.TargetId);

            // 只有当目标不是已知玩家时，才记录为攻击事件
            if (!isTargetKnownPlayer)
            {
                if (isSummonedAttack)
                {
                    // 直接处理召唤物攻击，在ProcessDamageOutput中通过EffectAlias前缀识别
                    // 临时修改EffectAlias来标记召唤物攻击
                    string? originalEffectAlias = instant.EffectAlias;
                    instant.EffectAlias = $"{originalEffectAlias}_SUMMONED";
                    casterPlayer.Add(instant);
                    // 恢复原始值
                    instant.EffectAlias = originalEffectAlias;
                }
                else
                {
                    casterPlayer.Add(instant);
                }
            }
            else
            {
                if (isSummonedAttack)
                {
                    Debug.WriteLine($"[召唤兽攻击] 目标是已知玩家，跳过统计: {instant.TargetName}({instant.TargetId})");
                }
            }
        }
        else
        {
            if (isSummonedAttack)
            {
                Debug.WriteLine($"[召唤兽攻击] 无法找到召唤师玩家对象: {summonerId}");
            }
        }
    }

    /// <summary>
    /// 处理PlayerAttacked事件（玩家被攻击）
    /// </summary>
    private void ProcessPlayerAttackedEvent(InstantEffectNotification2 instant)
    {
        // 对于PlayerAttacked事件，TargetId就是被攻击的玩家ID
        if (instant.TargetId != 0)
        {
            // 检查是否为召唤物被攻击
            if (_summonedToSummoner.TryGetValue(instant.TargetId, out var summonerId))
            {
                // 召唤物被攻击，将承伤合并到召唤师
                var summonerPlayer = GetOrCreatePlayer(summonerId, null);
                if (summonerPlayer != null)
                {
                    // 根据效果类型判断是治疗还是承伤
                    if (instant.EffectType == EffectTypeSeq.InstantHp || instant.EffectType == EffectTypeSeq.IntervalHp ||
                        instant.EffectType == EffectTypeSeq.InstantSp || instant.EffectType == EffectTypeSeq.IntervalSp)
                    {
                        // 治疗/内力恢复事件，需要交换角色：召唤师作为施法者，目标保持不变
                        var originalObjectType = instant.ObjectType;
                        var originalCasterId = instant.CasterId;
                        var originalCasterName = instant.CasterName;

                        instant.ObjectType = ObjectTypeSeq.PlayerAttack; // 改为主动攻击类型
                        instant.CasterId = summonerId; // 召唤师作为施法者
                        instant.CasterName = summonerPlayer.Name ?? instant.CasterName;

                        // 临时修改EffectAlias来标记召唤物技能
                        string? originalEffectAlias = instant.EffectAlias;
                        instant.EffectAlias = $"{originalEffectAlias}_SUMMONED";

                        summonerPlayer.Add(instant);

                        // 恢复所有原始值
                        instant.ObjectType = originalObjectType;
                        instant.CasterId = originalCasterId;
                        instant.CasterName = originalCasterName;
                        instant.EffectAlias = originalEffectAlias;
                    }
                    else
                    {
                        // 承伤事件，直接处理
                        string? originalEffectAlias = instant.EffectAlias;
                        instant.EffectAlias = $"{originalEffectAlias}_SUMMONED";
                        summonerPlayer.Add(instant);
                        // 恢复原始值
                        instant.EffectAlias = originalEffectAlias;
                    }
                }
            }
            else
            {
                // 普通玩家被攻击
                var targetPlayer = GetOrCreatePlayer(instant.TargetId, instant.TargetName);
                if (targetPlayer != null)
                {
                    targetPlayer.Add(instant);
                }
            }
        }
    }

    /// <summary>
    /// 处理被攻击方事件（用于PlayerAttack和Other类型事件）
    /// </summary>
    private void ProcessTargetEvent(InstantEffectNotification2 instant)
    {
        // 只有当目标不是施法者本人时才处理被攻击数据
        if (instant.TargetId != 0 && instant.TargetId != instant.CasterId)
        {
            CreatureStats? targetPlayer;
            bool isTargetSummoned = false;

            // 检查目标是否为召唤物，如果是则将事件合并到召唤师身上
            if (_summonedToSummoner.TryGetValue(instant.TargetId, out var summonerId))
            {
                // 目标是召唤物，将承伤合并到召唤师
                targetPlayer = GetOrCreatePlayer(summonerId, null);
                isTargetSummoned = true;
            }
            else
            {
                // 目标是普通玩家
                targetPlayer = GetOrCreatePlayer(instant.TargetId, instant.TargetName);
                isTargetSummoned = false;
            }

            if (targetPlayer != null)
            {
                // 直接处理被攻击事件，临时交换攻击者和被攻击者的角色
                var originalObjectType = instant.ObjectType;
                var originalCasterId = instant.CasterId;
                var originalCasterName = instant.CasterName;
                var originalTargetId = instant.TargetId;
                var originalTargetName = instant.TargetName;
                var originalEffectAlias = instant.EffectAlias;

                // 交换角色：被攻击方成为"施法者"，攻击方成为"目标"
                instant.ObjectType = ObjectTypeSeq.PlayerAttacked;
                instant.CasterId = isTargetSummoned ? summonerId : originalTargetId;
                instant.CasterName = isTargetSummoned ? (targetPlayer.Name ?? originalTargetName) : originalTargetName;
                instant.TargetId = originalCasterId;
                instant.TargetName = originalCasterName;

                // 如果目标是召唤物，标记为召唤物事件
                if (isTargetSummoned)
                {
                    instant.EffectAlias = $"{originalEffectAlias}_SUMMONED";
                }

                targetPlayer.Add(instant);

                // 恢复原始值
                instant.ObjectType = originalObjectType;
                instant.CasterId = originalCasterId;
                instant.CasterName = originalCasterName;
                instant.TargetId = originalTargetId;
                instant.TargetName = originalTargetName;
                instant.EffectAlias = originalEffectAlias;
            }
        }
    }

    /// <summary>
    /// 检查指定ID是否为某个玩家的召唤兽
    /// </summary>
    /// <param name="summonedId">可能的召唤兽ID</param>
    /// <param name="summonerId">召唤师ID</param>
    /// <returns>如果summonedId是summonerId的召唤兽返回true，否则返回false</returns>
    public bool IsSummonedBy(long summonedId, long summonerId)
    {
        return _summonedToSummoner.TryGetValue(summonedId, out var actualSummonerId) && actualSummonerId == summonerId;
    }

    /// <summary>
    /// 检查玩家ID是否为已知玩家（队友）
    /// </summary>
    /// <param name="playerId">玩家ID</param>
    /// <returns>如果是已知玩家返回true，否则返回false</returns>
    private bool IsKnownPlayer(long playerId)
    {
        if (playerId == 0) return false;

        // 检查是否在已知玩家列表中（只检查真正的玩家，不包括召唤物）
        return _creatureInfoById.TryGetValue(playerId, out var creature) && creature.Job <= JobSeq.PcMax;
    }

    /// <summary>
    /// 获取或创建玩家对象
    /// </summary>
    private CreatureStats? GetOrCreatePlayer(long playerId, string? playerName)
    {
        // 优先通过ID查找
        if (_playersByPlayerId.TryGetValue(playerId, out var player)) return player;

        // 特殊处理：如果默认玩家的ID为0且请求的ID不为0，检查是否应该更新默认玩家
        if (Default != null && Default.PlayerId == 0 && playerId != 0)
        {
            var creature = _creatureInfoById.GetValueOrDefault(playerId);
            if (creature != null && creature.Job > JobSeq.JobNone)
            {
                // 检查玩家名称是否匹配（如果有的话）
                string targetName = playerName ?? creature.Name;
                if (string.IsNullOrEmpty(Default.Name) ||
                    Default.Name.Equals(targetName, StringComparison.OrdinalIgnoreCase))
                {
                    // 更新默认玩家的信息而不是创建新玩家
                    Default.PlayerId = playerId;
                    Default.Name = targetName;
                    Default.Job = creature.Job;
                    _playersByPlayerId[playerId] = Default;
                    System.Diagnostics.Debug.WriteLine($"[CombatStats] 更新默认玩家信息：ID={playerId}, Name={targetName}");
                    return Default;
                }
            }
        }

        // 没找到，尝试创建新玩家
        var creatureInfo = _creatureInfoById.GetValueOrDefault(playerId);
        if (creatureInfo is null) return null;
        else
        {
            // 检查是否为召唤物，如果是召唤物则不创建独立玩家对象
            if (creatureInfo.Job > JobSeq.PcMax) return null;

            // 创建新玩家
            player = new CreatureStats(this, playerId, playerName ?? creatureInfo.Name, creatureInfo.Job);
        }

        // 添加到缓存
        _playersByPlayerId[playerId] = player;

        Add(player);

        // 新玩家加入时使缓存失效
        InvalidateTargetCache();

        return player;
    }


    #region Target Management
    /// <summary>
    /// 使缓存失效，在数据变化时调用
    /// </summary>
    internal void InvalidateTargetCache()
    {
        _targetCacheValid = false;
        _targetNamesCache = null;
        _targetDamageCache.Clear();
    }

    /// <summary>
    /// 更新目标统计缓存
    /// </summary>
    private void UpdateTargetCache()
    {
        if (_targetCacheValid) return;

        _targetNamesCache = [];
        _targetDamageCache.Clear();

        // 遍历所有玩家（队伍成员）的伤害技能
        foreach (var player in this.Where(p => p.Job <= JobSeq.PcMax)) // 只包含真正的玩家，不包括召唤物
        {
            foreach (var skill in player.DamageSkills.Where(s => !string.IsNullOrEmpty(s.TargetName) && s.TotalDamage > 0))
            {
                _targetNamesCache.Add(skill.TargetName);

                if (_targetDamageCache.TryGetValue(skill.TargetName, out var currentDamage))
                {
                    _targetDamageCache[skill.TargetName] = currentDamage + skill.TotalDamage;
                }
                else
                {
                    _targetDamageCache[skill.TargetName] = skill.TotalDamage;
                }
            }
        }

        _targetCacheValid = true;
    }

    /// <summary>
    /// 获取所有目标名称列表（包含所有队伍成员攻击的目标，排除增益目标）
    /// </summary>
    public IEnumerable<string> GetTargetNames()
    {
        UpdateTargetCache();
        return _targetNamesCache ?? Enumerable.Empty<string>();
    }

    /// <summary>
    /// 获取对指定目标的总伤害（所有队伍成员的总和）
    /// </summary>
    public long GetTotalDamageForTarget(string targetName)
    {
        UpdateTargetCache();
        return _targetDamageCache.GetValueOrDefault(targetName, 0);
    }

    /// <summary>
    /// 获取来自指定目标的总承受伤害
    /// </summary>
    public long GetTotalDamageTakenForTarget(string targetName)
    {
        if (Default == null) return 0;
        return Default.TakenDamageSkills.Where(s => s.TargetName == targetName).Sum(s => s.TotalDamage);
    }

    /// <summary>
    /// 获取对指定目标的总治疗量
    /// </summary>
    public long GetTotalHealingForTarget(string targetName)
    {
        if (Default == null) return 0;
        return Default.HealSkills.Where(s => s.TargetName == targetName).Sum(s => s.TotalDamage);
    }

    /// <summary>
    /// 获取对指定目标的技能统计列表（所有队伍成员的技能）
    /// </summary>
    public IEnumerable<SkillStats> GetSkillsForTarget(string targetName)
    {
        // 返回所有队伍成员对该目标的技能统计
        return this.Where(p => p.Job <= JobSeq.PcMax) // 只包含真正的玩家
            .SelectMany(p => p.DamageSkills)
            .Where(s => s.TargetName == targetName);
    }

    /// <summary>
    /// 检查目标死亡，如果所有正在统计的目标都死亡，则触发事件
    /// </summary>
    /// <param name="deadTarget">死亡的目标名称</param>
    public void CheckTargetDeath(string deadTarget)
    {
        if (Default == null) return;

        // 获取所有目标名称
        var allTargets = GetTargetNames().ToList();
        if (allTargets.Count == 0) return;

        // 检查死亡的目标是否在我们的目标列表中
        if (!allTargets.Contains(deadTarget))
            return; // 不是我们正在统计的目标

        // 标记该目标的所有技能为死亡状态（通过添加一个死亡标记）
        var targetSkills = Default.DamageSkills.Where(s => s.TargetName == deadTarget).ToList();
        foreach (var skill in targetSkills)
        {
            // 可以在这里添加死亡标记，或者从集合中移除
            // 暂时保留技能数据，只是不再统计新的伤害
        }

        System.Diagnostics.Debug.WriteLine($"目标 {deadTarget} 已死亡");

        // 获取所有有伤害的目标（正在统计的活跃目标）
        var aliveTargets = allTargets.Where(t => t != deadTarget && GetTotalDamageForTarget(t) > 0).ToList();

        // 如果所有有伤害的目标都死亡了，则触发事件
        if (aliveTargets.Count == 0)
        {
            AllTargetsDead?.Invoke();
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"目标 {deadTarget} 死亡，还有 {aliveTargets.Count} 个目标存活，继续统计");
        }
    }

    /// <summary>
    /// 根据当前目标过滤器计算过滤后的战斗时间
    /// </summary>
    /// <param name="player">玩家统计数据</param>
    /// <returns>过滤后的战斗时间（秒）</returns>
    public double GetFilteredSeconds(CreatureStats player)
    {
        // 如果没有目标过滤器，返回整体战斗时间
        if (string.IsNullOrEmpty(TargetFilter))
        {
            return player.StartTime.HasValue ? Math.Max(1.0, Math.Round((player.EndTime - player.StartTime.Value).TotalSeconds, 0)) : 0;
        }

        // 如果有目标过滤器，计算对该目标的战斗时间
        var targetSkills = player.DamageSkills.Where(x => x.TargetName == TargetFilter);
        if (!targetSkills.Any()) return 0;

        var startTimes = targetSkills.Where(s => s.StartTime.HasValue).Select(s => s.StartTime!.Value);
        var endTimes = targetSkills.Select(s => s.EndTime);

        if (!startTimes.Any()) return 0;

        var minStart = startTimes.Min();
        var maxEnd = endTimes.Max();

        return Math.Max(1.0, Math.Round((maxEnd - minStart).TotalSeconds, 0));
    }

    /// <summary>
    /// 根据当前目标过滤器计算过滤后的伤害总量
    /// </summary>
    /// <param name="skills">技能统计集合</param>
    /// <returns>过滤后的伤害总量</returns>
    public long GetFilteredDamage(ObservableCollection<SkillStats> skills)
    {
        // 如果没有目标过滤器，返回所有技能的总伤害
        if (string.IsNullOrEmpty(TargetFilter))
        {
            return skills.Sum(x => x.TotalDamage);
        }

        // 如果有目标过滤器，只计算对应目标的伤害
        return skills.Where(x => x.TargetName == TargetFilter).Sum(x => x.TotalDamage);
    }
    #endregion

    public void Refresh()
    {
        if (IsHistory) return;

        EndTime = DateTime.Now;
        OnPropertyChanged(nameof(View));
        OnPropertyChanged(nameof(TimeSpan));
    }

    /// <summary>
    /// Saves the current log to a JSON file.
    /// </summary>
    public void Save(int zone)
    {
        try
        {
            // 检查是否禁用战斗记录日志
            if (SettingHelper.Default.DisableBattleLog) return;
            if (IsHistory || !StartTime.HasValue || TimeSpan.TotalSeconds < 10) return;

            // 再次检查StartTime以确保安全
            if (!StartTime.HasValue) return;

            var logs = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            Directory.CreateDirectory(logs);
            // 使用新的序列化设置，包含完整事件转换器
            var settings = new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                Converters = new List<JsonConverter>
                {
                    new InstantEffectNotification2Converter(),
                    new CombatCollectionConverter()
                }
            };
            File.WriteAllText(Path.Combine(logs, $"act_{zone}_{StartTime.Value.Ticks}.json"), JsonConvert.SerializeObject(this, settings));
        }
        catch (Exception e)
        {
            Log.Error(e, "Save act log failed.");
        }
    }
    #endregion

    #region Interface
    public event PropertyChangedEventHandler? PropertyChanged;

    public void OnPropertyChanged(string name)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    #endregion
}
