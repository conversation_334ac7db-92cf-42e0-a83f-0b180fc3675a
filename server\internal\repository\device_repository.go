package repository

import (
	"time"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// DeviceRepository 设备历史数据访问层
type DeviceRepository struct {
	db *gorm.DB
}

// NewDeviceRepository 创建设备仓库
func NewDeviceRepository(db *gorm.DB) *DeviceRepository {
	return &DeviceRepository{db: db}
}

// CreateOrUpdate 创建或更新设备历史记录
func (r *DeviceRepository) CreateOrUpdate(uin int64, device, ip string) error {
	now := time.Now().Unix()

	// 查找现有记录
	var existingHistory model.DeviceHistory
	err := r.db.Where("uin = ? AND device = ? AND ip_address = ?", uin, device, ip).First(&existingHistory).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		deviceHistory := model.NewDeviceHistory(uin, device, ip)
		if err := r.db.Create(deviceHistory).Error; err != nil {
			logger.Error("创建设备历史记录失败: UIN=%d, Device=%s, IP=%s, Error=%v", uin, device, ip, err)
			return err
		}
		logger.Debug("创建新设备历史记录: UIN=%d, Device=%s, IP=%s", uin, device, ip)
	} else if err != nil {
		logger.Error("查询设备历史记录失败: UIN=%d, Error=%v", uin, err)
		return err
	} else {
		// 更新现有记录
		existingHistory.LoginCount++
		existingHistory.LoginTime = now
		existingHistory.LogoutTime = nil // 重置登出时间

		if err := r.db.Save(&existingHistory).Error; err != nil {
			logger.Error("更新设备历史记录失败: UIN=%d, Error=%v", uin, err)
			return err
		}
		logger.Debug("更新设备历史记录: UIN=%d, Device=%s, IP=%s, LoginCount=%d",
			uin, device, ip, existingHistory.LoginCount)
	}

	return nil
}

// UpdateLogoutTime 更新登出时间
func (r *DeviceRepository) UpdateLogoutTime(uin int64) error {
	now := time.Now().Unix()

	result := r.db.Model(&model.DeviceHistory{}).
		Where("uin = ? AND logout_time = 0", uin).
		Update("logout_time", now)

	if result.Error != nil {
		logger.Error("更新设备离线状态失败: UIN=%d, Error=%v", uin, result.Error)
		return result.Error
	}

	if result.RowsAffected > 0 {
		logger.Debug("已更新设备离线状态: UIN=%d, 影响记录数=%d, 登出时间=%d", uin, result.RowsAffected, now)
	}

	return nil
}

// GetDevicesByUinSince 获取用户在指定时间后的设备记录
func (r *DeviceRepository) GetDevicesByUinSince(uin int64, since time.Time) ([]*model.DeviceHistory, error) {
	var histories []*model.DeviceHistory
	sinceUnix := since.Unix()

	err := r.db.Where("uin = ? AND login_time >= ?", uin, sinceUnix).Find(&histories).Error
	if err != nil {
		logger.Error("查询用户设备历史失败: UIN=%d, Since=%v, Error=%v", uin, since, err)
		return nil, err
	}

	return histories, nil
}

// GetUinsByDeviceSince 获取设备在指定时间后的用户记录
func (r *DeviceRepository) GetUinsByDeviceSince(device string, since time.Time) ([]*model.DeviceHistory, error) {
	var histories []*model.DeviceHistory
	sinceUnix := since.Unix()

	err := r.db.Where("device = ? AND login_time >= ?", device, sinceUnix).
		Select("DISTINCT uin").Find(&histories).Error
	if err != nil {
		logger.Error("查询设备登录历史失败: Device=%s, Since=%v, Error=%v", device, since, err)
		return nil, err
	}

	return histories, nil
}

// GetUinsByIPSince 获取IP在指定时间后的用户记录
func (r *DeviceRepository) GetUinsByIPSince(ipAddress string, since time.Time) ([]*model.DeviceHistory, error) {
	var histories []*model.DeviceHistory
	sinceUnix := since.Unix()

	err := r.db.Where("ip_address = ? AND login_time >= ?", ipAddress, sinceUnix).
		Select("DISTINCT uin").Find(&histories).Error
	if err != nil {
		logger.Error("查询IP登录历史失败: IP=%s, Since=%v, Error=%v", ipAddress, since, err)
		return nil, err
	}

	return histories, nil
}

// CountLoginAttempts 统计用户在指定时间段内的登录次数
func (r *DeviceRepository) CountLoginAttempts(uin int64, since time.Time) (int64, error) {
	var count int64
	sinceUnix := since.Unix()

	err := r.db.Model(&model.DeviceHistory{}).
		Where("uin = ? AND login_time >= ?", uin, sinceUnix).
		Count(&count).Error
	if err != nil {
		logger.Error("查询用户登录次数失败: UIN=%d, Since=%v, Error=%v", uin, since, err)
		return 0, err
	}

	return count, nil
}

// GetLatestDeviceFingerprint 获取用户最新的设备指纹
func (r *DeviceRepository) GetLatestDeviceFingerprint(uin int64) (string, error) {
	var history model.DeviceHistory

	err := r.db.Where("uin = ?", uin).
		Order("login_time DESC").
		First(&history).Error

	if err == gorm.ErrRecordNotFound {
		return "", nil
	}
	if err != nil {
		logger.Error("获取最新设备指纹失败: UIN=%d, Error=%v", uin, err)
		return "", err
	}

	return history.Device, nil
}

// CleanupOldRecords 清理旧的设备历史记录
func (r *DeviceRepository) CleanupOldRecords(olderThan time.Time) (int64, error) {
	olderThanUnix := olderThan.Unix()

	result := r.db.Where("login_time < ?", olderThanUnix).Delete(&model.DeviceHistory{})
	if result.Error != nil {
		logger.Error("清理旧设备历史记录失败: OlderThan=%v, Error=%v", olderThan, result.Error)
		return 0, result.Error
	}

	if result.RowsAffected > 0 {
		logger.Info("已清理旧设备历史记录: 删除记录数=%d", result.RowsAffected)
	}

	return result.RowsAffected, nil
}

// GetDeviceStats 获取设备统计信息
func (r *DeviceRepository) GetDeviceStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总记录数
	var totalCount int64
	if err := r.db.Model(&model.DeviceHistory{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats["total_records"] = totalCount

	// 今日登录记录数
	today := time.Now().Truncate(24 * time.Hour).Unix()
	var todayCount int64
	if err := r.db.Model(&model.DeviceHistory{}).Where("login_time >= ?", today).Count(&todayCount).Error; err != nil {
		return nil, err
	}
	stats["today_logins"] = todayCount

	// 活跃设备数（最近7天）
	weekAgo := time.Now().AddDate(0, 0, -7).Unix()
	var activeDevices int64
	if err := r.db.Model(&model.DeviceHistory{}).
		Where("login_time >= ?", weekAgo).
		Distinct("device").
		Count(&activeDevices).Error; err != nil {
		return nil, err
	}
	stats["active_devices_7d"] = activeDevices

	return stats, nil
}

// BatchCreateOrUpdate 批量创建或更新设备历史记录
func (r *DeviceRepository) BatchCreateOrUpdate(records []struct {
	Uin    int64
	Device string
	IP     string
}) error {
	if len(records) == 0 {
		return nil
	}

	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, record := range records {
		if err := r.CreateOrUpdate(record.Uin, record.Device, record.IP); err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}
