package handler

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

// WebhookHandler Webhook处理器，用于接收PHP后台的推送通知
type WebhookHandler struct {
	announcementService *service.AnnouncementService
	authService         *service.AuthService
}

// NewWebhookHandler 创建Webhook处理器实例
func NewWebhookHandler(announcementService *service.AnnouncementService, authService *service.AuthService) *WebhookHandler {
	return &WebhookHandler{
		announcementService: announcementService,
		authService:         authService,
	}
}

// AnnouncementWebhookRequest 公告Webhook请求结构
type AnnouncementWebhookRequest struct {
	Type           string `json:"type"`            // 操作类型：published, updated, deleted
	AnnouncementID uint64 `json:"announcement_id"` // 公告ID
	Title          string `json:"title"`           // 公告标题
	TypeCode       int    `json:"type_code"`       // 公告类型
	Priority       int    `json:"priority"`        // 优先级
	TargetClient   string `json:"target_client"`   // 目标客户端
	Timestamp      int64  `json:"timestamp"`       // 时间戳
}

// AnnouncementWebhookResponse 公告Webhook响应结构
type AnnouncementWebhookResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

// HandleAnnouncementWebhook 处理公告Webhook请求
func (h *WebhookHandler) HandleAnnouncementWebhook(w http.ResponseWriter, r *http.Request) {
	logger.Info("=== 收到公告Webhook请求 ===")
	logger.Info("请求方法: %s", r.Method)
	logger.Info("请求路径: %s", r.URL.Path)
	logger.Info("请求来源: %s", r.RemoteAddr)
	logger.Info("Content-Type: %s", r.Header.Get("Content-Type"))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")

	// 只允许POST请求
	if r.Method != http.MethodPost {
		logger.Warn("不支持的请求方法: %s", r.Method)
		h.sendErrorResponse(w, http.StatusMethodNotAllowed, "只支持POST请求")
		return
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Error("读取请求体失败: %v", err)
		h.sendErrorResponse(w, http.StatusBadRequest, "读取请求体失败")
		return
	}
	defer r.Body.Close()

	logger.Info("请求体内容: %s", string(body))

	// 解析JSON
	var req AnnouncementWebhookRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Error("解析JSON失败: %v", err)
		h.sendErrorResponse(w, http.StatusBadRequest, "JSON格式错误")
		return
	}

	logger.Info("解析成功 - 类型: %s, 公告ID: %d, 标题: %s", req.Type, req.AnnouncementID, req.Title)

	// 验证必要字段
	if req.Type == "" {
		logger.Error("缺少必要字段: type")
		h.sendErrorResponse(w, http.StatusBadRequest, "缺少操作类型")
		return
	}

	// 处理不同类型的操作
	switch req.Type {
	case "published":
		err = h.handleAnnouncementPublished(&req)
	case "updated":
		err = h.handleAnnouncementUpdated(&req)
	case "deleted":
		err = h.handleAnnouncementDeleted(&req)
	default:
		logger.Warn("未知的操作类型: %s", req.Type)
		h.sendErrorResponse(w, http.StatusBadRequest, "未知的操作类型")
		return
	}

	if err != nil {
		logger.Error("处理公告操作失败: %v", err)
		h.sendErrorResponse(w, http.StatusInternalServerError, "处理失败: "+err.Error())
		return
	}

	// 返回成功响应
	logger.Info("公告Webhook处理成功")
	h.sendSuccessResponse(w, "处理成功")
}

// handleAnnouncementPublished 处理公告发布
func (h *WebhookHandler) handleAnnouncementPublished(req *AnnouncementWebhookRequest) error {
	logger.Info("处理公告发布: ID=%d, 标题=%s", req.AnnouncementID, req.Title)

	// 更新公告版本
	if err := h.announcementService.UpdateVersion(); err != nil {
		logger.Error("更新公告版本失败: %v", err)
		return fmt.Errorf("更新版本失败: %v", err)
	}

	// 推送给在线用户
	h.pushToOnlineUsers("ANNOUNCEMENT_PUBLISHED", map[string]interface{}{
		"announcement_id": req.AnnouncementID,
		"title":          req.Title,
		"type_code":      req.TypeCode,
		"priority":       req.Priority,
		"target_client":  req.TargetClient,
	})

	logger.Info("公告发布处理完成")
	return nil
}

// handleAnnouncementUpdated 处理公告更新
func (h *WebhookHandler) handleAnnouncementUpdated(req *AnnouncementWebhookRequest) error {
	logger.Info("处理公告更新: ID=%d, 标题=%s", req.AnnouncementID, req.Title)

	// 更新公告版本
	if err := h.announcementService.UpdateVersion(); err != nil {
		logger.Error("更新公告版本失败: %v", err)
		return fmt.Errorf("更新版本失败: %v", err)
	}

	// 推送给在线用户
	h.pushToOnlineUsers("ANNOUNCEMENT_UPDATED", map[string]interface{}{
		"announcement_id": req.AnnouncementID,
		"title":          req.Title,
		"type_code":      req.TypeCode,
		"priority":       req.Priority,
		"target_client":  req.TargetClient,
	})

	logger.Info("公告更新处理完成")
	return nil
}

// handleAnnouncementDeleted 处理公告删除
func (h *WebhookHandler) handleAnnouncementDeleted(req *AnnouncementWebhookRequest) error {
	logger.Info("处理公告删除: ID=%d", req.AnnouncementID)

	// 更新公告版本
	if err := h.announcementService.UpdateVersion(); err != nil {
		logger.Error("更新公告版本失败: %v", err)
		return fmt.Errorf("更新版本失败: %v", err)
	}

	// 推送给在线用户
	h.pushToOnlineUsers("ANNOUNCEMENT_DELETED", map[string]interface{}{
		"announcement_id": req.AnnouncementID,
	})

	logger.Info("公告删除处理完成")
	return nil
}

// pushToOnlineUsers 推送消息给在线用户
func (h *WebhookHandler) pushToOnlineUsers(eventType string, data map[string]interface{}) {
	onlineUsers := h.authService.GetOnlineUsers()
	pushCount := 0

	logger.Info("开始推送消息给在线用户，事件类型: %s, 在线用户数: %d", eventType, len(onlineUsers))

	for userID := range onlineUsers {
		// 这里可以实现具体的推送逻辑
		// 由于UDP是无连接的，可以在用户下次请求时返回更新通知
		logger.Debug("标记用户 %d 需要接收公告更新通知", userID)
		pushCount++
	}

	logger.Info("推送标记完成，影响用户数: %d", pushCount)
}

// sendSuccessResponse 发送成功响应
func (h *WebhookHandler) sendSuccessResponse(w http.ResponseWriter, message string) {
	response := AnnouncementWebhookResponse{
		Success: true,
		Message: message,
		Code:    200,
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// sendErrorResponse 发送错误响应
func (h *WebhookHandler) sendErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	response := AnnouncementWebhookResponse{
		Success: false,
		Message: message,
		Code:    statusCode,
	}

	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// HandleHealthCheck 健康检查接口
func (h *WebhookHandler) HandleHealthCheck(w http.ResponseWriter, r *http.Request) {
	logger.Debug("收到健康检查请求")

	w.Header().Set("Content-Type", "application/json")
	
	response := map[string]interface{}{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"service":   "announcement-webhook",
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
