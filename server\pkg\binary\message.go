package binary

import (
	"fmt"
	"time"
)

// Message 二进制协议消息
type Message struct {
	Header *MessageHeader
	Body   []byte
}

// NewMessage 创建新消息
func NewMessage(msgType uint8, flags uint8, body []byte) *Message {
	bodyLength := uint32(len(body))
	return &Message{
		Header: &MessageHeader{
			Magic:     ProtocolMagic,
			Version:   ProtocolVersion,
			MsgType:   msgType,
			Flags:     flags,
			Length:    HeaderSize + bodyLength,
			Timestamp: uint64(time.Now().UnixMilli()),
		},
		Body: body,
	}
}

// 服务端只需要创建响应消息的方法

// NewLoginResponseMessage 创建登录响应消息
func NewLoginResponseMessage(resp *LoginResponse) *Message {
	body := EncodeLoginResponse(resp)
	return NewMessage(MsgTypeLoginResponse, 0, body)
}

// NewLogoutResponseMessage 创建注销响应消息
func NewLogoutResponseMessage(resp *LogoutResponse) *Message {
	body := EncodeLogoutResponse(resp)
	return NewMessage(MsgTypeLogoutResponse, 0, body)
}

// NewLuckyDrawResponseMessage 创建签到抽奖响应消息
func NewLuckyDrawResponseMessage(resp *LuckyDrawResponse) *Message {
	body := EncodeLuckyDrawResponse(resp)
	return NewMessage(MsgTypeLuckyDrawResponse, 0, body)
}

// NewLuckyStatusResponseMessage 创建签到状态响应消息
func NewLuckyStatusResponseMessage(resp *LuckyStatusResponse) *Message {
	body := EncodeLuckyStatusResponse(resp)
	return NewMessage(MsgTypeLuckyStatusResponse, 0, body)
}

// NewCDKeyActivateResponseMessage 创建CDKEY激活响应消息
func NewCDKeyActivateResponseMessage(resp *CDKeyActivateResponse) *Message {
	body := EncodeCDKeyActivateResponse(resp)
	return NewMessage(MsgTypeCDKeyActivateResponse, 0, body)
}

// NewGetActivityInfoResponseMessage 创建获取活动信息响应消息
func NewGetActivityInfoResponseMessage(resp *GetActivityInfoResponse) *Message {
	body := EncodeGetActivityInfoResponse(resp)
	return NewMessage(MsgTypeGetActivityInfoResponse, 0, body)
}

// NewUpdateConfigResponseMessage 创建更新配置响应消息
func NewUpdateConfigResponseMessage(resp *UpdateConfigResponse) *Message {
	body := EncodeUpdateConfigResponse(resp)
	return NewMessage(MsgTypeUpdateConfigResponse, 0, body)
}

// NewAnnouncementConfigResponseMessage 创建公告配置响应消息
func NewAnnouncementConfigResponseMessage(resp *AnnouncementConfigResponse) *Message {
	body := EncodeAnnouncementConfigResponse(resp)
	return NewMessage(MsgTypeAnnouncementConfigResponse, 0, body)
}

// NewAnnouncementDetailResponseMessage 创建公告详情响应消息
func NewAnnouncementDetailResponseMessage(resp *AnnouncementDetailResponse) *Message {
	body := EncodeAnnouncementDetailResponse(resp)
	return NewMessage(MsgTypeAnnouncementDetailResponse, 0, body)
}

// GetLoginRequest 从消息中解析登录请求
func (m *Message) GetLoginRequest() (*LoginRequest, error) {
	if m.Header.MsgType != MsgTypeLogin {
		return nil, fmt.Errorf("message type is not login request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeLoginRequest(m.Body)
}

// GetHeartbeatRequest 从消息中解析心跳请求
func (m *Message) GetHeartbeatRequest() (*HeartbeatRequest, error) {
	if m.Header.MsgType != MsgTypeHeartbeat {
		return nil, fmt.Errorf("message type is not heartbeat request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeHeartbeatRequest(m.Body)
}

// GetLogoutRequest 从消息中解析注销请求
func (m *Message) GetLogoutRequest() (*LogoutRequest, error) {
	if m.Header.MsgType != MsgTypeLogout {
		return nil, fmt.Errorf("message type is not logout request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeLogoutRequest(m.Body)
}

// GetLuckyDrawRequest 从消息中解析签到抽奖请求
func (m *Message) GetLuckyDrawRequest() (*LuckyDrawRequest, error) {
	if m.Header.MsgType != MsgTypeLuckyDraw {
		return nil, fmt.Errorf("message type is not lucky draw request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeLuckyDrawRequest(m.Body)
}

// GetLuckyStatusRequest 从消息中解析签到状态请求
func (m *Message) GetLuckyStatusRequest() (*LuckyStatusRequest, error) {
	if m.Header.MsgType != MsgTypeLuckyStatus {
		return nil, fmt.Errorf("message type is not lucky status request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeLuckyStatusRequest(m.Body)
}

// GetCDKeyActivateRequest 从消息中解析CDKEY激活请求
func (m *Message) GetCDKeyActivateRequest() (*CDKeyActivateRequest, error) {
	if m.Header.MsgType != MsgTypeCDKeyActivate {
		return nil, fmt.Errorf("message type is not cdkey activate request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeCDKeyActivateRequest(m.Body)
}

// GetUpdateConfigRequest 从消息中解析更新配置请求
func (m *Message) GetUpdateConfigRequest() (*UpdateConfigRequest, error) {
	if m.Header.MsgType != MsgTypeUpdateConfig {
		return nil, fmt.Errorf("message type is not update config request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeUpdateConfigRequest(m.Body)
}

// GetAnnouncementConfigRequest 从消息中解析公告配置请求
func (m *Message) GetAnnouncementConfigRequest() (*AnnouncementConfigRequest, error) {
	if m.Header.MsgType != MsgTypeAnnouncementConfig {
		return nil, fmt.Errorf("message type is not announcement config request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeAnnouncementConfigRequest(m.Body)
}

// GetAnnouncementDetailRequest 从消息中解析公告详情请求
func (m *Message) GetAnnouncementDetailRequest() (*AnnouncementDetailRequest, error) {
	if m.Header.MsgType != MsgTypeAnnouncementDetail {
		return nil, fmt.Errorf("message type is not announcement detail request: got 0x%02X", m.Header.MsgType)
	}
	return DecodeAnnouncementDetailRequest(m.Body)
}

// Clone 克隆消息
func (m *Message) Clone() *Message {
	bodyClone := make([]byte, len(m.Body))
	copy(bodyClone, m.Body)

	return &Message{
		Header: &MessageHeader{
			Magic:     m.Header.Magic,
			Version:   m.Header.Version,
			MsgType:   m.Header.MsgType,
			Flags:     m.Header.Flags,
			Length:    m.Header.Length,
			Timestamp: m.Header.Timestamp,
		},
		Body: bodyClone,
	}
}

// Encode 编码消息为字节流
func (m *Message) Encode() ([]byte, error) {
	// 验证消息大小
	totalLength := HeaderSize + uint32(len(m.Body))
	if totalLength > MaxMessageSize {
		return nil, fmt.Errorf("message too large: %d bytes (max: %d)", totalLength, MaxMessageSize)
	}

	// 更新消息头长度
	m.Header.Length = totalLength

	// 编码消息头
	result := m.Header.Encode()

	// 添加消息体
	result = append(result, m.Body...)

	return result, nil
}

// EncodeWithEncryption 编码消息并加密
func (m *Message) EncodeWithEncryption() ([]byte, error) {
	// 先编码消息
	data, err := m.Encode()
	if err != nil {
		return nil, err
	}

	// 加密消息体
	encryptedData, err := EncryptMessageBody(data)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt message: %w", err)
	}

	return encryptedData, nil
}

// EncodeWithCompressionAndEncryption 编码消息并压缩和加密
func (m *Message) EncodeWithCompressionAndEncryption() ([]byte, error) {
	// 先编码消息
	data, err := m.Encode()
	if err != nil {
		return nil, err
	}

	// 压缩消息体
	compressedData, err := CompressMessageBody(data)
	if err != nil {
		return nil, fmt.Errorf("failed to compress message: %w", err)
	}

	// 加密消息体
	encryptedData, err := EncryptMessageBody(compressedData)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt message: %w", err)
	}

	return encryptedData, nil
}

// DecodeMessage 从字节流解码消息
func DecodeMessage(data []byte) (*Message, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("insufficient data for message header: need %d bytes, got %d", HeaderSize, len(data))
	}

	// 解码消息头
	header, err := DecodeHeader(data)
	if err != nil {
		return nil, fmt.Errorf("failed to decode header: %w", err)
	}

	// 验证数据长度
	if len(data) < int(header.Length) {
		return nil, fmt.Errorf("insufficient data for complete message: need %d bytes, got %d", header.Length, len(data))
	}

	// 验证消息大小
	if header.Length > MaxMessageSize {
		return nil, fmt.Errorf("message too large: %d bytes (max: %d)", header.Length, MaxMessageSize)
	}

	// 提取消息体
	bodyLength := int(header.Length) - HeaderSize
	body := make([]byte, bodyLength)
	if bodyLength > 0 {
		copy(body, data[HeaderSize:HeaderSize+bodyLength])
	}

	message := &Message{
		Header: header,
		Body:   body,
	}

	return message, nil
}

// DecodeMessageWithDecryption 解码并解密消息
func DecodeMessageWithDecryption(data []byte) (*Message, error) {
	// 先解密数据
	decryptedData, err := DecryptMessageBody(data)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt message: %w", err)
	}

	// 解码消息
	return DecodeMessage(decryptedData)
}

// String 返回消息的字符串表示
func (m *Message) String() string {
	return fmt.Sprintf("Message{%s, BodyLength:%d}", m.Header.String(), len(m.Body))
}

// Validate 验证消息的有效性
func (m *Message) Validate() error {
	if m.Header == nil {
		return fmt.Errorf("message header is nil")
	}

	if err := m.Header.Validate(); err != nil {
		return fmt.Errorf("invalid header: %w", err)
	}

	// 验证消息体长度
	expectedBodyLength := int(m.Header.Length) - HeaderSize
	if len(m.Body) != expectedBodyLength {
		return fmt.Errorf("body length mismatch: expected %d, got %d", expectedBodyLength, len(m.Body))
	}

	return nil
}
