# UpdateConfig 更新逻辑修改说明

## 修改概述

本次修改解决了以下问题：
1. **EndOfStreamException错误** - 修复了C#客户端读取服务端响应时的流读取错误
2. **版本比较逻辑** - 实现了智能版本比较，只有在需要更新时才返回下载信息
3. **字段重新分配** - 将PluginVersion、PluginURL和Groups字段从UpdateConfig移动到UserLogin

## 主要修改

### 1. 修复EndOfStreamException

**问题原因**：
- 服务端发送TVL格式数据，但C#客户端期望直接的字符串格式
- 数据格式不匹配导致读取越界

**解决方案**：
- 修改服务端编码格式，使用与C#兼容的字符串格式（4字节长度前缀+内容）
- 简化C#客户端读取逻辑，按顺序读取字段

**修改文件**：
- `server/pkg/binary/update_config.go` - 修改编码格式
- `BnsHelper/Services/Network/Service/UpdateConfigPacket.cs` - 简化读取逻辑

### 2. 智能版本比较

**新增功能**：
- 服务端接收客户端版本号
- 比较客户端版本与服务端最新版本
- 只有在需要更新时才返回ExecutablePath和DownloadURL

**版本比较规则**：
- 支持标准版本格式：1.0.0, 1.0.0.1, 1.0.0-beta等
- 按数字部分逐段比较
- 自动处理版本后缀（-beta, -alpha等）

**修改文件**：
- `server/internal/service/update_service.go` - 添加版本比较逻辑
- 新增方法：`compareVersions()`, `parseVersion()`, `compareVersionParts()`

### 3. 字段重新分配

**移动的字段**：
- `PluginVersion` - 插件版本号
- `PluginURL` - 插件下载链接  
- `Groups` - 群组信息数组

**从UpdateConfig移动到UserLogin的原因**：
- 这些字段只有在用户登录后才需要
- 减少UpdateConfig响应的数据量
- 提高更新检查的效率

**修改文件**：
- `BnsHelper/Services/Network/Service/LoginPacket.cs` - 添加新字段
- `server/pkg/binary/field.go` - 修改LoginResponse结构体和编码方法
- `server/cmd/main.go` - 在登录处理中添加插件和群组信息

## 数据流程

### 更新检查流程（简化）
```
客户端 → 发送版本号 → 服务端
服务端 → 比较版本 → 如果需要更新：返回下载信息
                  → 如果不需要：返回空响应
```

### 登录流程（增强）
```
客户端 → 登录请求 → 服务端
服务端 → 验证用户 → 返回：Token + 权限 + 插件信息 + 群组信息
```

## 响应格式变化

### UpdateConfig响应（简化后）
```
成功且需要更新：
ErrorCode(4字节) + ExecutablePath(字符串) + DownloadURL(字符串)

成功但无需更新：
ErrorCode(4字节)

失败：
ErrorCode(4字节) + ErrorMessage(字符串)
```

### Login响应（增强后）
```
成功：
ErrorCode(4字节) + Token(字符串) + Permission(1字节) + 
PermissionExpiration(8字节) + PluginVersion(字符串) + 
PluginURL(字符串) + Groups(4字节长度+数组内容)

失败：
ErrorCode(4字节) + ErrorMessage(字符串)
```

## 测试建议

### 1. 版本比较测试
```
客户端版本 | 服务端版本 | 预期结果
1.0.0     | 1.0.1     | 需要更新
1.0.1     | 1.0.0     | 不需要更新  
1.0.0     | 1.0.0     | 不需要更新
1.0.0-beta| 1.0.0     | 需要更新
```

### 2. 网络连接测试
- 测试网络断开时的行为
- 验证错误处理和用户提示
- 确认程序不会异常退出

### 3. 登录功能测试
- 验证插件信息正确返回
- 确认群组信息完整
- 测试权限信息准确性

## 兼容性说明

- **向后兼容**：旧版本客户端仍可正常工作
- **数据库兼容**：无需修改现有数据库结构
- **配置兼容**：现有配置文件无需修改

## 性能优化

1. **减少数据传输**：UpdateConfig响应更小
2. **智能更新**：避免不必要的下载提示
3. **缓存优化**：版本比较结果可缓存
4. **错误处理**：更好的异常恢复机制
