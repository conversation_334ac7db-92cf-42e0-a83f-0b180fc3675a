package config

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
	"udp-server/server/pkg/logger"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Config 配置管理器
type Config struct {
	viper    *viper.Viper
	mu       sync.RWMutex
	watchers []ConfigWatcher
}

// ConfigWatcher 配置变更观察者接口
type ConfigWatcher interface {
	OnConfigChange()
}

// NewConfig 创建配置管理器
func NewConfig() *Config {
	v := viper.New()
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath("config")
	v.AddConfigPath(".")

	// 读取环境变量
	v.AutomaticEnv()

	return &Config{
		viper:    v,
		watchers: make([]ConfigWatcher, 0),
	}
}

// Load 加载配置
func (c *Config) Load() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if err := c.viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			return c.createDefaultConfig()
		}
		return fmt.Errorf("error reading config file: %v", err)
	}

	// 设置配置热重载
	c.viper.WatchConfig()
	c.viper.OnConfigChange(func(e fsnotify.Event) {
		logger.Info("Config file changed: %s", e.Name)
		c.notifyWatchers()
	})

	return nil
}

// Get 获取配置值
func (c *Config) Get(key string) interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.viper.Get(key)
}

// GetString 获取字符串配置
func (c *Config) GetString(key string) string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.viper.GetString(key)
}

// GetInt 获取整数配置
func (c *Config) GetInt(key string) int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.viper.GetInt(key)
}

// GetDuration 获取时间配置
func (c *Config) GetDuration(key string) time.Duration {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.viper.GetDuration(key)
}

// GetBool 获取布尔配置
func (c *Config) GetBool(key string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.viper.GetBool(key)
}

// AddWatcher 添加配置变更观察者
func (c *Config) AddWatcher(watcher ConfigWatcher) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.watchers = append(c.watchers, watcher)
}

// notifyWatchers 通知所有观察者
func (c *Config) notifyWatchers() {
	c.mu.RLock()
	defer c.mu.RUnlock()
	for _, watcher := range c.watchers {
		watcher.OnConfigChange()
	}
}

// createDefaultConfig 创建默认配置
func (c *Config) createDefaultConfig() error {
	// 确保配置目录存在
	if err := os.MkdirAll("config", 0755); err != nil {
		return fmt.Errorf("error creating config directory: %v", err)
	}

	// 设置默认值
	c.viper.SetDefault("database.driver", "mysql")
	c.viper.SetDefault("database.host", "localhost")
	c.viper.SetDefault("database.port", 3306)
	c.viper.SetDefault("database.username", "root")
	c.viper.SetDefault("database.password", "root")
	c.viper.SetDefault("database.dbname", "bns")
	c.viper.SetDefault("database.charset", "utf8mb4")
	c.viper.SetDefault("database.parseTime", true)
	c.viper.SetDefault("database.loc", "Local")
	c.viper.SetDefault("database.maxIdleConns", 10)
	c.viper.SetDefault("database.maxOpenConns", 100)
	c.viper.SetDefault("database.connMaxLifetime", 3600)

	c.viper.SetDefault("server.host", "0.0.0.0")
	c.viper.SetDefault("server.port", 8080)

	c.viper.SetDefault("redis.host", "localhost")
	c.viper.SetDefault("redis.port", 6379)
	c.viper.SetDefault("redis.password", "")
	c.viper.SetDefault("redis.db", 0)

	// 日志配置默认值
	c.viper.SetDefault("log.level", "INFO")
	c.viper.SetDefault("log.console", true)
	c.viper.SetDefault("log.file", true)
	c.viper.SetDefault("log.file_path", "logs/server.log")

	// 保存配置文件
	if err := c.viper.WriteConfigAs(filepath.Join("config", "config.yaml")); err != nil {
		return fmt.Errorf("error writing config file: %v", err)
	}

	return nil
}
