<?php
/**
 * 测试公告发布功能
 * 模拟在管理后台发布公告
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "=== 测试公告发布功能 ===\n\n";

// 1. 测试Redis连接
echo "1. 连接Redis...\n";
try {
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    $redis->select(4);
    echo "✓ Redis连接成功\n";
} catch (Exception $e) {
    echo "✗ Redis连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 模拟公告数据
$announcement = [
    'id' => 88888,
    'title' => '测试公告 - ' . date('Y-m-d H:i:s'),
    'content' => '这是一个测试公告，用于验证Go服务端是否能正确接收推送消息。',
    'type' => 1,
    'priority' => 1,
    'target_client' => 'all',
    'status' => 1,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "\n2. 准备发布公告...\n";
echo "公告信息:\n";
echo "  ID: " . $announcement['id'] . "\n";
echo "  标题: " . $announcement['title'] . "\n";
echo "  类型: " . $announcement['type'] . "\n";
echo "  优先级: " . $announcement['priority'] . "\n";
echo "  目标客户端: " . $announcement['target_client'] . "\n";

// 3. 构建推送消息（模拟AnnouncementPushService::publishAnnouncementUpdate）
$pushMessage = [
    'type' => 'ANNOUNCEMENT_PUBLISHED',
    'announcement_id' => $announcement['id'],
    'title' => $announcement['title'],
    'type_code' => $announcement['type'],
    'priority' => $announcement['priority'],
    'target_client' => $announcement['target_client'],
    'timestamp' => time(),
];

echo "\n3. 发布推送消息...\n";
echo "推送消息内容:\n";
echo json_encode($pushMessage, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";

// 4. 发布消息到Redis
try {
    $result = $redis->publish('announcement_update', json_encode($pushMessage));
    
    echo "\n4. 发布结果:\n";
    if ($result > 0) {
        echo "✓ 消息发布成功！\n";
        echo "  订阅者数量: $result\n";
        echo "  这表示Go服务端正在监听并应该收到消息\n";
        
        // 等待一下，让Go服务端处理消息
        echo "\n等待Go服务端处理消息...\n";
        sleep(2);
        
        echo "✓ 如果Go服务端正常工作，应该已经处理了这个公告发布消息\n";
        echo "  请检查Go服务端日志或客户端是否收到公告推送\n";
        
    } else {
        echo "⚠ 消息发布成功，但没有订阅者\n";
        echo "  这表示Go服务端可能没有运行或没有订阅此频道\n";
    }
    
} catch (Exception $e) {
    echo "✗ 消息发布失败: " . $e->getMessage() . "\n";
}

// 5. 测试多种消息类型
echo "\n5. 测试其他消息类型...\n";

// 测试公告删除消息
$deleteMessage = [
    'type' => 'ANNOUNCEMENT_DELETED',
    'announcement_id' => 99999,
    'timestamp' => time(),
];

echo "发布删除消息...\n";
$result = $redis->publish('announcement_update', json_encode($deleteMessage));
echo "删除消息订阅者数量: $result\n";

// 测试版本更新消息
$versionMessage = [
    'type' => 'ANNOUNCEMENT_VERSION_UPDATE',
    'version' => time(),
    'timestamp' => time(),
];

echo "发布版本更新消息...\n";
$result = $redis->publish('announcement_update', json_encode($versionMessage));
echo "版本更新消息订阅者数量: $result\n";

$redis->close();

echo "\n=== 测试完成 ===\n";
echo "\n总结:\n";
echo "1. 如果所有消息的订阅者数量都是1，说明Go服务端正在监听\n";
echo "2. 如果Go服务端没有日志输出，可能是日志级别或输出配置问题\n";
echo "3. 建议检查Go服务端的日志文件: server/logs/server.log\n";
echo "4. 或者在客户端测试是否能收到公告推送\n";
?>
