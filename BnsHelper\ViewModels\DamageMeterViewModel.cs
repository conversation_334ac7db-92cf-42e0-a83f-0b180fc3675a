﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Threading;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.Services.Network;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.Preview.Data.Models.Sequence;

namespace Xylia.BnsHelper.ViewModels;
internal partial class DamageMeterViewModel : ObservableObject, IDisposable
{
    #region Fields
    internal static nint gHwnd;
    internal static EventHandler? OnRefresh;

    private readonly DispatcherTimer Timer;
    private readonly PluginSession Service;
    private readonly DispatcherTimer _delayedRefreshTimer;
    private bool _refreshPending = false;

    [ObservableProperty] CombatCollection _players = [];

    // 保存真实的当前用户信息，用于重置时恢复
    private string? _realPlayerName;
    private JobSeq _realPlayerJob = JobSeq.PcMax;

    // Target selection
    private TargetOption? _selectedTarget;
    public TargetOption? SelectedTarget
    {
        get => _selectedTarget;
        set
        {
            if (SetProperty(ref _selectedTarget, value))
            {
                OnPropertyChanged(nameof(CurrentTargetName));
            }
        }
    }

    public string CurrentTargetName
    {
        get
        {
            if (SelectedTarget == null || SelectedTarget.IsAllTargets)
            {
                return GetDisplayNameForAllTargets();
            }

            return SelectedTarget.OriginalName ?? SelectedTarget.Name;
        }
    }

    /// <summary>
    /// 获取当前选中目标的实时战斗时间（用于UI绑定）
    /// 当目标名称超过10个字符时返回0（不显示秒数），但默认状态（全部目标）除外
    /// </summary>
    public double CurrentTargetSeconds
    {
        get
        {
            if (Players.Default == null) return 0;

            // 当没有选择目标时（默认状态）或选择了"全部目标"时，显示整体战斗时间
            if (SelectedTarget == null || SelectedTarget.IsAllTargets)
            {
                // 使用整体战斗时间，不受长度限制
                return Players.StartTime.HasValue ?
                    Math.Round((Players.EndTime - Players.StartTime.Value).TotalSeconds, 0) : 0;
            }

            // 对于单个目标，当目标名称超过10个字符时不显示秒数
            if (CurrentTargetName.Length > 10) return 0;

            if (!SelectedTarget.IsNoTarget && !string.IsNullOrEmpty(SelectedTarget.OriginalName))
            {
                var targetSkills = Players.GetSkillsForTarget(SelectedTarget.OriginalName).ToList();
                if (targetSkills.Any())
                {
                    var startTime = targetSkills.Where(s => s.StartTime.HasValue).Min(s => s.StartTime);
                    var endTime = targetSkills.Max(s => s.EndTime);
                    return startTime.HasValue ? Math.Round((endTime - startTime.Value).TotalSeconds, 0) : 0;
                }
            }

            return 0;
        }
    }

    /// <summary>
    /// 获取有效目标名称列表（按伤害排序）
    /// </summary>
    private string[] GetValidTargetNames()
    {
        return Players.GetTargetNames()
            .Where(t => Players.GetTotalDamageForTarget(t) > 0)
            .OrderByDescending(t => Players.GetTotalDamageForTarget(t))
            .ToArray();
    }

    /// <summary>
    /// 获取所有目标的显示名称
    /// </summary>
    private string GetDisplayNameForAllTargets()
    {
        var targetNames = GetValidTargetNames();
        if (targetNames.Length > 0)
        {
            return string.Join(", ", targetNames);
        }

        // 如果正在战斗中但还没有目标，显示更友好的提示
        if (Status == StatusType.Work && Players.Default != null && Players.Default.Count > 0)
        {
            return "战斗中";
        }

        return "无目标";
    }

    /// <summary>
    /// 目标选项数据类
    /// </summary>
    public partial class TargetOption : ObservableObject
    {
        [ObservableProperty] private string _name = "";
        [ObservableProperty] private string? _originalName;
        [ObservableProperty] private long _totalDamage;
        [ObservableProperty] private double _damageRate;
        [ObservableProperty] private double _seconds;

        /// <summary>
        /// 标识是否为"全部目标"选项
        /// </summary>
        public bool IsAllTargets { get; set; }

        /// <summary>
        /// 标识是否为"无目标"选项
        /// </summary>
        public bool IsNoTarget { get; set; }
    }

    /// <summary>
    /// 创建全部目标选项
    /// </summary>
    private TargetOption CreateAllTargetsOption(string[] targetNames)
    {
        double totalSeconds = Players.Default?.Seconds ?? 0;
        string allTargetsName = $"{string.Join(", ", targetNames)} ({totalSeconds:F0}秒)";

        return new TargetOption
        {
            Name = allTargetsName,
            TotalDamage = Players.TotalDamage,
            DamageRate = 1.0,
            Seconds = totalSeconds,
            IsAllTargets = true
        };
    }

    /// <summary>
    /// 创建单个目标选项
    /// </summary>
    private TargetOption CreateTargetOption(string targetName)
    {
        if (Players.Default == null) return new TargetOption { Name = "无目标", IsNoTarget = true };

        var targetDamage = Players.GetTotalDamageForTarget(targetName);
        var targetSkills = Players.GetSkillsForTarget(targetName).ToList();

        // 计算目标的战斗时间
        var startTime = targetSkills.Where(s => s.StartTime.HasValue).Min(s => s.StartTime);
        var endTime = targetSkills.Max(s => s.EndTime);
        var seconds = startTime.HasValue ? Math.Round((endTime - startTime.Value).TotalSeconds, 0) : 0;

        return new TargetOption
        {
            Name = $"{targetName} ({seconds:F0}秒)",
            OriginalName = targetName,
            TotalDamage = targetDamage,
            DamageRate = targetDamage > 0 ? (double)targetDamage / Players.TotalDamage : 0,
            Seconds = seconds,
        };
    }

    public IEnumerable<TargetOption> TargetOptions
    {
        get
        {
            var targetNames = GetValidTargetNames();
            if (targetNames.Length == 0)
            {
                return [new TargetOption { Name = "无目标", IsNoTarget = true }];
            }

            // 添加"全部目标"选项
            List<TargetOption> options = [CreateAllTargetsOption(targetNames), .. targetNames.Select(CreateTargetOption)];
            return options;
        }
    }
    #endregion

    #region Properties
    // Status
    public enum StatusType
    {
        Wait,
        Work,
        Pause,
        PauseAuto
    }

    StatusType _status;
    public StatusType Status
    {
        get => _status;
        set
        {
            if (_status == value) return;
            switch (value)
            {
                case StatusType.Wait:
                    Timer.Stop(); // 停止Timer，避免自动暂停逻辑触发
                    Reset();
                    break;
                case StatusType.Work:
                {
                    // 自动暂停的自动重置
                    if (_status == StatusType.Pause || _status == StatusType.PauseAuto)
                    {
                        Reset();
                    }

                    // 读图过程中会出现多线程异常，因此此时不要发送消息
                    if (!Timer.IsEnabled) Timer.Start();
                    break;
                }
                case StatusType.Pause:
                {
                    Timer.Stop();
                    break;
                }
                case StatusType.PauseAuto:
                {
                    Timer.Stop();
                    break;
                }
            }

            SetProperty(ref _status, value);
            OnPropertyChanged(nameof(IsWork));
            OnPropertyChanged(nameof(IsPause));
        }
    }

    public bool IsWork => Status == StatusType.Work || Status == StatusType.Wait;
    public bool IsPause => Status == StatusType.Pause || Status == StatusType.PauseAuto;

    // Statistics Type
    StatisticsType _statisticsType = StatisticsType.Damage;
    public StatisticsType StatisticsType
    {
        get => _statisticsType;
        set
        {
            if (SetProperty(ref _statisticsType, value))
            {
                OnPropertyChanged(nameof(StatisticsTypeDisplayName));

                // 更新Players集合的统计类型
                Players.StatisticsType = value;

                // 触发界面刷新
                OnPropertyChanged(nameof(Players));
            }
        }
    }

    public string StatisticsTypeDisplayName => StatisticsType switch
    {
        StatisticsType.Damage => "伤害输出",
        StatisticsType.DamageTaken => "承受伤害",
        StatisticsType.Healing => "治疗统计",
        _ => "伤害输出"
    };

    int _page;
    public int Page
    {
        get => _page;
        set => SetProperty(ref _page, value);
    }

    // History
    int _zone = 0;
    public int Zone
    {
        get => _zone;
        set
        {
            var oldZone = _zone;
            Status = StatusType.Wait;
            SetProperty(ref _zone, value);

            // 清除所有倒计时器
            BossTimerService.Instance.ClearAllTimers();
        }
    }

    int _currentChannel = 0; // 之前的频道号
    public int CurrentChannel
    {
        get => _currentChannel;
        set
        {
            // 切换频道时清除统计数据（排除初始化时的设置）
            var oldChannel = _currentChannel;
            if (oldChannel != 0 && value != 0 && oldChannel != value)
            {
                Status = StatusType.Wait;
                Debug.WriteLine($"[DamageMeter] 切换频道 {oldChannel} -> {value}，清除统计数据");
            }
            SetProperty(ref _currentChannel, value);
        }
    }

    public bool GroupMode
    {
        get => SettingHelper.Default.GroupMode;
        set
        {
            SettingHelper.Default.GroupMode = value;
            OnPropertyChanged(nameof(History));
        }
    }

    public IEnumerable History
    {
        get
        {
            var data = new List<HistoryData>();
            var logs = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");

            // 检查logs目录是否存在
            if (Directory.Exists(logs))
            {
                foreach (var file in new DirectoryInfo(logs).GetFiles("act*.json"))
                {
                    try
                    {
                        data.Add(HistoryData.Load(file));
                    }
                    catch
                    {
                        Debug.Fail(file.FullName);
                    }
                }
            }

            // 返回按时间或区域分组的历史数据
            return HistoryGroup.GroupBy<object>(data, o =>
            {
                if (GroupMode) return o.Zone;

                var date = (DateTime.Now - o.Time).TotalDays;
                if (date <= 1) return HistoryType.Today;
                else if (date <= 7) return HistoryType.Week;
                else return HistoryType.Other;
            });
        }
    }

    bool _isHitVisible = true;
    public bool IsHitTestVisible
    {
        get => _isHitVisible;
        set => SetProperty(ref _isHitVisible, value);
    }
    #endregion

    #region Methods
    public DamageMeterViewModel()
    {
        Players.Clear();
        Service = new PluginSession(OnReceived);

        // 刷新定时器
        Timer = new DispatcherTimer(new TimeSpan(TimeSpan.TicksPerMillisecond * 500), DispatcherPriority.Render, Refresh, Application.Current.Dispatcher);
        _delayedRefreshTimer = new DispatcherTimer(new TimeSpan(TimeSpan.TicksPerMillisecond * 500), DispatcherPriority.Render, OnDelayedRefresh, Application.Current.Dispatcher);

        // Subscribe to target changes
        Players.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(Players.Default))
            {
                // 使用延迟刷新而不是立即刷新
                ScheduleDelayedRefresh();
            }
        };

        // Subscribe to boss timer updates
        BossTimerService.Instance.TimersUpdated += (s, e) =>
        {
            // 使用延迟刷新而不是立即刷新
            ScheduleDelayedRefresh();
        };

        // Subscribe to all targets dead event
        Players.AllTargetsDead += OnAllTargetsDead;
    }

    public void Initialize()
    {
        Service.Register(gHwnd = WindowHelper.GetGameWindow());
    }

    public void Reset()
    {
        Players.Save(_zone);

        // 保存当前默认玩家的ID，避免重复创建
        long currentPlayerId = Players.Default?.PlayerId ?? 0;

        Players.Clear();

        // 如果有真实的用户信息，使用 UpdateDefaultPlayer 方法恢复它
        if (!string.IsNullOrEmpty(_realPlayerName))
        {
            // 使用正确的玩家ID，避免重复创建
            Players.UpdateDefaultPlayer(currentPlayerId, _realPlayerName, _realPlayerJob);
        }

        // Reset target selection
        SelectedTarget = null;

        // 注意：不清除BOSS倒计时器，因为它们独立于战斗数据
        // BOSS倒计时器应该持续到自然过期或手动清除

        OnPropertyChanged(nameof(History));
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
    }

    /// <summary>
    /// 处理所有目标死亡事件
    /// </summary>
    private void OnAllTargetsDead()
    {
        // 所有正在统计的目标都死亡了，停止记录
        Status = StatusType.PauseAuto;
        Timer.Stop();

        Debug.WriteLine($"所有目标都已死亡，自动停止伤害统计");

        // 更新目标选项显示
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
    }

    private void Refresh(object? sender, EventArgs e)
    {
        // auto encounter
        if (Players.LastCombatTime.Ticks != 0 &&
            (DateTime.Now - Players.LastCombatTime).TotalSeconds > SettingHelper.Default.AutoResetEncounter)
        {
            Timer.Stop();
            Status = StatusType.PauseAuto;
        }

        // 使用延迟刷新机制，减少UI更新频率
        ScheduleDelayedRefresh();
        OnRefresh?.Invoke(sender, e);
    }

    /// <summary>
    /// 安排延迟刷新，避免频繁的UI更新
    /// </summary>
    private void ScheduleDelayedRefresh()
    {
        if (!_refreshPending)
        {
            _refreshPending = true;
            _delayedRefreshTimer.Stop();
            _delayedRefreshTimer.Start();
        }
    }

    /// <summary>
    /// 延迟刷新事件处理
    /// </summary>
    private void OnDelayedRefresh(object? sender, EventArgs e)
    {
        _delayedRefreshTimer.Stop();
        _refreshPending = false;

        // 更新当前选中目标的实时数据
        UpdateSelectedTargetData();

        // 更新技能显示（延迟更新，避免频繁刷新）
        if (Players.Default != null)
        {
            Players.Default.UpdateSkillsDisplay(StatisticsType);
        }

        // 批量更新UI，减少重绘次数
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
        OnPropertyChanged(nameof(CurrentTargetSeconds));
        OnPropertyChanged(nameof(BossTimers));

        // 通知Players刷新
        Players.OnPropertyChanged(nameof(Players.Default));
    }

    /// <summary>
    /// 更新当前选中目标的实时数据
    /// </summary>
    private void UpdateSelectedTargetData()
    {
        if (SelectedTarget == null || Players.Default == null) return;

        if (SelectedTarget.IsAllTargets)
        {
            // 更新全部目标的战斗时间 - 使用整体战斗时间
            SelectedTarget.Seconds = Players.StartTime.HasValue ?
                Math.Round((Players.EndTime - Players.StartTime.Value).TotalSeconds, 0) : 0;
        }
        else if (!SelectedTarget.IsNoTarget && !string.IsNullOrEmpty(SelectedTarget.OriginalName))
        {
            // 更新单个目标的战斗时间
            var targetSkills = Players.GetSkillsForTarget(SelectedTarget.OriginalName).ToList();
            if (targetSkills.Any())
            {
                var startTime = targetSkills.Where(s => s.StartTime.HasValue).Min(s => s.StartTime);
                var endTime = targetSkills.Max(s => s.EndTime);
                var seconds = startTime.HasValue ? Math.Round((endTime - startTime.Value).TotalSeconds, 0) : 0;
                SelectedTarget.Seconds = seconds;
            }
        }
    }

    public void Dispose()
    {
        User32.SendMessage(gHwnd, User32.WindowMessage.WM_APP, AppMessage.UnRegister, nint.Zero);
        OnRefresh = null;

        // 停止并清理主定时器
        Timer?.Stop();

        // 取消事件订阅
        Players.AllTargetsDead -= OnAllTargetsDead;

        Service.Dispose();
        BossTimerService.Instance.Dispose();

        // 清理延迟刷新定时器
        if (_delayedRefreshTimer != null)
        {
            _delayedRefreshTimer.Stop();
            _delayedRefreshTimer.Tick -= OnDelayedRefresh;
        }
    }

    [RelayCommand]
    void SwitchStauts()
    {
        Status = Status switch
        {
            StatusType.Work => StatusType.Pause,
            StatusType.PauseAuto => StatusType.Pause, // 自动暂停状态切换到手动暂停，保持数据不清除
            _ => StatusType.Wait,
        };
    }

    [RelayCommand]
    void SetStatisticsType(string type)
    {
        if (Enum.TryParse<StatisticsType>(type, out var statisticsType))
        {
            StatisticsType = statisticsType;
        }
    }

    [RelayCommand]
    void ResetData()
    {
        Status = StatusType.Wait;
    }

    [RelayCommand]
    void SelectTarget(TargetOption? target)
    {
        SelectedTarget = target;

        // Update the target filter in Players collection
        if (target != null && !target.IsAllTargets && !target.IsNoTarget)
        {
            Players.TargetFilter = target.OriginalName;
        }
        else
        {
            // 对于"全部目标"或"无目标"，清除过滤器
            Players.TargetFilter = null;
        }

        // Refresh the view to apply the filter
        Players.OnPropertyChanged(nameof(Players.View));

        // 通知相关属性更新
        OnPropertyChanged(nameof(TargetOptions));
    }

    private void OnReceived(object? sender, IPacket packet)
    {
        switch (packet)
        {
            case EnterWorld i:
            {
                Zone = i.ZoneId;

                if (i.Player != null && Players.Default != null)
                {
                    // 保存真实的用户信息
                    _realPlayerName = i.Player.Name;
                    _realPlayerJob = i.Player.Job;

                    // 更新默认玩家信息，并同步更新缓存
                    Players.UpdateDefaultPlayer(i.Player.Id, i.Player.Name, i.Player.Job);

                    // 获取服务器ID用于BOSS倒计时器
                    _currentServerId = i.Player.world;
                    Debug.WriteLine($"[BossTimer] 更新服务器ID: {_currentServerId} ({i.Player.World})");
                }
                break;
            }
            case EnterChannel i:
            {
                // 设置待确认频道，等待后续消息确认是否切换成功
                _pendingChannel = i.Channel;
                Debug.WriteLine($"[BossTimer] 尝试进入频道: {i.Channel}");
                break;
            }
            case KeyInput i when i.Key == User32.VK.VK_F1: SwitchStauts(); break;
            case InstantNotification i: Parse(i.Text); break;
            case InstantEffectNotification i: Players.Add(i); break;
            case InstantEffectNotification2 i: Parse(i); break;
        }
    }

    private void Parse(string? message)
    {
        if (string.IsNullOrEmpty(message)) return;

        // BOSS倒计时器检测 - 在指定区域内才处理
        if (IsBossTimerZone(Zone)) CheckBossTimerMessages(message);
    }

    private void Parse(InstantEffectNotification2 instant)
    {
        // 检查是否暂停状态
        if (Status == StatusType.Pause) return;
        Status = StatusType.Work;

        // 让 CombatCollection 处理所有逻辑
        Application.Current.Dispatcher.Invoke(() => Players.Add(instant));
    }
    #endregion

    #region BossTimer
    public ObservableCollection<BossTimer> BossTimers => BossTimerService.Instance.ActiveTimers;
    private readonly int[] BossTimerZone = [2000, 2300, 2440, 3010, 4000, 4250, 4302, 4400, 3086, 5200, 5295, 5500];

    // Current Server ID for Boss Timer
    int _currentServerId = 0;
    int _pendingChannel = 0; // 待确认的频道号，用于处理频道切换失败的情况

    /// <summary>
    /// 检查是否为BOSS倒计时器支持的区域
    /// </summary>
    /// <param name="zoneId">区域ID</param>
    /// <returns>是否支持BOSS倒计时器</returns>
    private bool IsBossTimerZone(int zoneId) => BossTimerZone.Contains(zoneId);

    /// <summary>
    /// 检查BOSS倒计时器相关消息
    /// </summary>
    /// <param name="message">消息内容</param>
    private void CheckBossTimerMessages(string message)
    {
        try
        {
            // 检查频道切换失败消息
            if (message.Contains("切换频道失败") || message.Contains("无法切换频道"))
            {
                // 快捷键按太快的时候也会提示，需要排除
                if (message.Contains("9 秒后重试")) return;

                _pendingChannel = 0;
                Debug.WriteLine($"[BossTimer] 检测到频道切换失败消息: {message}");
                return;
            }
            else if (_pendingChannel > 0)
            {
                CurrentChannel = _pendingChannel;
                _pendingChannel = 0;
            }

            // 正则表达式：消灭了<arg p="2:npc.name2"/><eul/>，消耗了<arg p="3:integer"/> 点洪门庇护
            var bossKillRegex = new Regex(@"消灭了(.+?)，消耗了(\d+)\s*点洪门庇护", RegexOptions.Compiled);
            var bossKillMatch = bossKillRegex.Match(message);
            if (bossKillMatch.Success)
            {
                var bossName = bossKillMatch.Groups[1].Value.Trim();
                var isMutant = bossName.Contains("变异体");

                // 使用新的重载方法，根据服务器ID和区域ID自动确定类型
                BossTimerService.Instance.AddOrUpdateTimer(CurrentChannel, _currentServerId, Zone, isMutant);
                Debug.WriteLine($"[BossTimer] 检测到BOSS击杀: {bossName}, 频道: {CurrentChannel}, 服务器: {_currentServerId}, 区域: {Zone}, 变异体: {isMutant}");
            }

            // 检查不祥力量消息：<image imagesetpath="00027918.Portrait_Alert"/>不祥的力量开始笼罩。
            if (message.Contains("不祥的力量开始笼罩"))
            {
                BossTimerService.Instance.AddOrUpdateTimer(CurrentChannel, BossTimerType.ZNCS_MutantAlarm);
                Debug.WriteLine($"[BossTimer] 检测到不祥力量: 频道: {CurrentChannel}");
            }

            // 调试：记录所有在BOSS区域的消息，帮助调试（仅在Debug模式下）
            Debug.WriteLine($"[BossTimer] 区域{Zone}频道{CurrentChannel}消息: {message}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[BossTimer] 消息处理异常: {ex.Message}");
        }
    }


    [RelayCommand]
    void ClearSingleBossTimer(BossTimer? timer)
    {
        BossTimerService.Instance.RemoveTimer(timer.Channel);
        Debug.WriteLine($"[BossTimer] 手动清除频道{timer.Channel}的倒计时器");
    }

    [RelayCommand]
    void ClearAllBossTimers()
    {
        BossTimerService.Instance.ClearAllTimers();
        Debug.WriteLine("[BossTimer] 手动清除所有倒计时器");
    }

    #endregion
}

public enum StatisticsType
{
    Damage,      // 伤害输出
    DamageTaken, // 承受伤害
    Healing      // 治疗
}
