﻿using HandyControl.Interactivity;
using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Diagnostics;
using System.IO;
using System.Web;
using System.Windows;
using Xylia.BnsHelper.Common;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.BnsHelper.Services.Network.Service;
using Xylia.BnsHelper.ViewModels;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Views;
public partial class UserLogin2
{
    #region Constructor
    //const string Source = "https://xui.ptlogin2.qq.com/cgi-bin/xlogin?proxy_url=http://game.qq.com/comm-htdocs/milo/proxy.html&appid=21000501&s_url=https://bns.qq.com/comm-htdocs/login/logincallback.htm";
    const string Source = "https://xui.ptlogin2.qq.com/cgi-bin/xlogin?pt_disable_pwd=1&appid=715030901&daid=73&s_url=https%3A%2F%2Fqun.qq.com/";

    public UserLogin2()
    {
        InitializeComponent();
        CheckWebView2Runtime();

        // 获取必要参数
        var query = HttpUtility.ParseQueryString(new Uri(Source).Query);
        callback = HttpUtility.UrlDecode(query["s_url"]);
    }
    #endregion

    #region Methods
    private void CheckWebView2Runtime()
    {
        try
        {
            // 尝试获取WebView2运行时版本
            var version = CoreWebView2Environment.GetAvailableBrowserVersionString();
            if (string.IsNullOrEmpty(version))
            {
                ShowWebView2RuntimeError();
                return;
            }

            // 运行时可用，设置WebView2源
            wv.Source = new Uri(Source);
        }
        catch (Exception)
        {
            // 检测失败，显示错误
            ShowWebView2RuntimeError();
        }
    }

    private void ShowWebView2RuntimeError()
    {
        wv.Visibility = Visibility.Collapsed;
        ShowWebView2Error(null);
    }

    private void CoreWebView2InitializationCompleted(object? sender, CoreWebView2InitializationCompletedEventArgs e)
    {
        if (!e.IsSuccess)
        {
            // WebView2运行时初始化失败，显示错误信息
            ShowWebView2Error(e.InitializationException);
            return;
        }

        wv.CoreWebView2.Settings.AreDevToolsEnabled = wv.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = false;
        wv.CoreWebView2.Settings.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 bnszs/0.1";
        wv.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

        wv.CoreWebView2.ContextMenuRequested += (s, e) => e.Handled = true;
        wv.CoreWebView2.NewWindowRequested += CoreWebView2_NewWindowRequested;
        wv.CoreWebView2.SourceChanged += CoreWebView2_SourceChanged;
        wv.CoreWebView2.WebResourceResponseReceived += CoreWebView2_WebResourceResponseReceived;
    }

    private void CoreWebView2_NewWindowRequested(object? sender, CoreWebView2NewWindowRequestedEventArgs e)
    {
        e.Handled = true;
        Process.Start("explorer.exe", e.Uri.ToString());
    }

    private void CoreWebView2_SourceChanged(object? sender, CoreWebView2SourceChangedEventArgs e)
    {
        var source = wv.CoreWebView2.Source;
        if (source.StartsWith("https://xui.ptlogin2.qq.com/cgi-bin/xlogin")) uin = 0;
        else if (source == callback)
        {
            // 验证回调页面
        }
    }

    private async void CoreWebView2_WebResourceResponseReceived(object? sender, CoreWebView2WebResourceResponseReceivedEventArgs e)
    {
        var uri = e.Request.Uri;
        if (uri.StartsWith("https://ptlogin2.qun.qq.com/check_sig"))
        {
            var query = HttpUtility.ParseQueryString(new Uri(uri).Query);
            uin = query["uin"].To<long>();
            var ptsigx = query["ptsigx"].To<string>();
            var aid = query["aid"].To<long>();

            wv.Visibility = Visibility.Collapsed;
            ShowLoading();
        }
        else if (uri.Contains("/get_group_list"))
        {
            var content = await e.Response.GetContentAsync();
            var text = new StreamReader(content).ReadToEnd();
            var token = JsonConvert.DeserializeObject<JToken>(text)!;

            void CheckInGroup()
            {
                Groups.Clear();
                token["join"]?.ForEach(o => Groups.Add(o.Value<long>("gc")!));
                token["manage"]?.ForEach(o => Groups.Add(o.Value<long>("gc")!));

                // 检查是否加入了白名单群
                var whitegroup = AppExtensions.FindProperty<long[]>("Groups");
                var flag = Groups.Any(o => whitegroup.Contains(o));
                if (!flag) throw new AppException("请先加入剑灵小助手交流群\n群列表可访问 www.bnszs.com 查看");
            }

            try
            {
                CheckInGroup();
                await CheckUin();
            }
            catch (Exception ex)
            {
                ShowError(ex.Message);
            }
        }
    }

    private void ShowWebView2Error(Exception? exception)
    {
        // Hide WebView2 control
        wv.Visibility = Visibility.Collapsed;

        // Hide loading icon and status text, show error container
        LoadingIcon.Visibility = Visibility.Collapsed;
        StatusText.Visibility = Visibility.Collapsed;
        ErrorContainer.Visibility = Visibility.Visible;

        // Update error text
        ErrorText.Text = "浏览器组件初始化失败\n请下载 Microsoft Edge WebView2 组件";

        // Update button text for WebView2 error
        RetryButton.Content = "下载";
        CancelButton.Content = "取消";

        // Show button container
        ButtonContainer.Visibility = Visibility.Visible;

        // Keep overlay visible to show error
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void ShowError(string errorMessage)
    {
        // Hide loading icon and status text, show error container
        LoadingIcon.Visibility = Visibility.Collapsed;
        StatusText.Visibility = Visibility.Collapsed;
        ErrorContainer.Visibility = Visibility.Visible;

        // Update error text
        ErrorText.Text = errorMessage;

        // Reset button text for normal errors
        RetryButton.Content = "重试";
        CancelButton.Content = "取消";

        // Show button container
        ButtonContainer.Visibility = Visibility.Visible;

        // Keep overlay visible to show error
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void ShowLoading()
    {
        // Show loading icon and status text, hide error elements
        LoadingIcon.Visibility = Visibility.Visible;
        StatusText.Visibility = Visibility.Visible;
        ErrorContainer.Visibility = Visibility.Collapsed;
        ButtonContainer.Visibility = Visibility.Collapsed;

        // Reset status text
        StatusText.Text = "登录中";

        // Show overlay
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void RetryButton_Click(object sender, RoutedEventArgs e)
    {
        // Check if this is a WebView2 error (button text is "下载")
        if (RetryButton.Content.ToString() == "下载")
        {
            // Fallback to explorer
            new OpenLinkCommand().Execute("https://developer.microsoft.com/zh-cn/microsoft-edge/webview2/consumer/");
        }

        // Normal retry logic
        // Hide error state and show browser again
        LoadingOverlay.Visibility = Visibility.Collapsed;
        wv.Visibility = Visibility.Visible;
        wv.Source = new Uri(Source);
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        // Close the login window
        DialogResult = false;
        Close();
    }

    private async Task CheckUin()
    {
        // 只有快捷登录可以获取到用户QQ
        if (uin == 0)
        {
            LoadingOverlay.Visibility = Visibility.Collapsed;  //隐藏加载提示
            wv.Visibility = Visibility.Visible;  //重新显示浏览器
            wv.Source = new Uri(Source);
            MessageBox.Show(StringHelper.Get("UseLogin_Error"), StringHelper.Get("ApplicationName"), icon: MessageBoxImage.Stop);
            return;
        }

        try
        {
            // Generate device fingerprint locally
            var deviceFingerprint = DeviceHelper.GenerateDeviceFingerprint();

            // Create session
            var session = new BnszsSession();
            session.SendPacket(new LoginPacket { Uin = uin.ToString(), DeviceFingerprint = deviceFingerprint }, MessageTypes.Login);

            // Wait for login response specifically, ignoring heartbeat packets
            var response = await session.WaitForResponseWithRetry(MessageTypes.LoginResponse, 3, 3000);
            if (response is LoginPacket login)
            {
                if (login.ErrorCode != 0) throw new AppException(login.ErrorMessage);

                // 验证成功完成登录流程
                var user = new User(session, uin);
                user.UpdatePermissionInfo(login.Permission, login.PermissionExpiration);

                MainWindowViewModel.Instance.User = user;
                DialogResult = true;
            }
        }
        catch (Exception ex)
        {
            ShowError(ex.Message);
            return;
        }
        finally
        {
            // Hide loading animation only on success
            if (DialogResult == true)
            {
                LoadingOverlay.Visibility = Visibility.Collapsed;
            }
        }
    }
    #endregion

    #region Fields
    private string? callback;
    private long uin;
    internal string? whitegroup;

    /// <summary>缓存本次登录的群号列表，因为cdkey可能存在白名单限制</summary>
    internal static HashSet<long> Groups = [];
    #endregion
}
