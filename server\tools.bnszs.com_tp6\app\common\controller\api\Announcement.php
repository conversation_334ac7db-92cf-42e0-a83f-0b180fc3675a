<?php
namespace app\common\controller\api;

use think\App;
use think\Request;
use think\Response;
use app\common\controller\BaseController;
use app\common\model\Announcement as AnnouncementModel;

class Announcement extends BaseController
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    /**
     * 获取公告列表（支持缓存机制）
     */
    public function list() {
        try {
            // 获取请求参数
            $clientType = $this->request->param('client_type', 'all');
            $clientVersion = $this->request->param('client_version', '');
            $ifModifiedSince = $this->request->header('If-Modified-Since');
            $clientCacheVersion = $this->request->param('cache_version', 0);
            
            // 获取当前版本和最后修改时间
            $currentVersion = AnnouncementModel::getCurrentVersion();
            $lastModified = AnnouncementModel::getLastModified();
            $lastModifiedTimestamp = strtotime($lastModified);
            
            // 检查客户端缓存是否有效
            $cacheValid = false;
            
            // 方式1：通过版本号检查
            if ($clientCacheVersion && $clientCacheVersion >= $currentVersion) {
                $cacheValid = true;
            }
            
            // 方式2：通过HTTP If-Modified-Since头检查
            if ($ifModifiedSince && strtotime($ifModifiedSince) >= $lastModifiedTimestamp) {
                $cacheValid = true;
            }
            
            // 如果缓存有效，返回304 Not Modified
            if ($cacheValid) {
                return Response::create('', 304)
                    ->header([
                        'Last-Modified' => gmdate('D, d M Y H:i:s', $lastModifiedTimestamp) . ' GMT',
                        'Cache-Control' => 'max-age=300', // 5分钟缓存
                        'ETag' => '"' . $currentVersion . '"'
                    ]);
            }
            
            // 获取有效的公告列表
            $announcements = AnnouncementModel::getActiveAnnouncements($clientType, $clientVersion);
            
            // 增加查看次数（异步处理，不影响响应速度）
            $this->incrementViewCount($announcements);
            
            // 构建响应数据
            $responseData = [
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'version' => $currentVersion,
                    'last_modified' => $lastModified,
                    'announcements' => $announcements,
                    'count' => count($announcements)
                ]
            ];
            
            // 设置缓存相关的HTTP头
            return json($responseData)
                ->header([
                    'Last-Modified' => gmdate('D, d M Y H:i:s', $lastModifiedTimestamp) . ' GMT',
                    'Cache-Control' => 'max-age=300', // 5分钟缓存
                    'ETag' => '"' . $currentVersion . '"',
                    'Vary' => 'Accept-Encoding'
                ]);
                
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '获取公告失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 检查公告更新（轻量级接口）
     */
    public function checkUpdate() {
        try {
            $clientCacheVersion = $this->request->param('cache_version', 0);
            $currentVersion = AnnouncementModel::getCurrentVersion();
            $lastModified = AnnouncementModel::getLastModified();
            
            $hasUpdate = $currentVersion > $clientCacheVersion;
            
            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'has_update' => $hasUpdate,
                    'current_version' => $currentVersion,
                    'last_modified' => $lastModified,
                    'cache_version' => $clientCacheVersion
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '检查更新失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取公告详情
     */
    public function detail() {
        try {
            $id = $this->request->param('id');
            
            if (!$id) {
                return json([
                    'code' => 1,
                    'msg' => '参数错误',
                    'data' => []
                ]);
            }
            
            $announcement = AnnouncementModel::find($id);
            
            if (!$announcement) {
                return json([
                    'code' => 1,
                    'msg' => '公告不存在',
                    'data' => []
                ]);
            }
            
            // 检查公告是否有效
            if (!$announcement->isActive()) {
                return json([
                    'code' => 1,
                    'msg' => '公告已失效',
                    'data' => []
                ]);
            }
            
            // 增加查看次数
            $announcement->incrementViewCount();
            
            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'id' => $announcement->id,
                    'title' => $announcement->title,
                    'content' => $announcement->content,
                    'type' => $announcement->type,
                    'priority' => $announcement->priority,
                    'start_time' => $announcement->start_time,
                    'end_time' => $announcement->end_time,
                    'target_client' => $announcement->target_client,
                    'version_requirement' => $announcement->version_requirement,
                    'created_at' => $announcement->created_at,
                    'updated_at' => $announcement->updated_at
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '获取公告详情失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取公告统计信息（供客户端显示）
     */
    public function stats() {
        try {
            $clientType = $this->request->param('client_type', 'all');
            
            // 获取各类型公告数量
            $stats = [
                'total' => 0,
                'normal' => 0,
                'important' => 0,
                'urgent' => 0
            ];
            
            $announcements = AnnouncementModel::getActiveAnnouncements($clientType);
            $stats['total'] = count($announcements);
            
            foreach ($announcements as $announcement) {
                switch ($announcement['type']) {
                    case 1:
                        $stats['normal']++;
                        break;
                    case 2:
                        $stats['important']++;
                        break;
                    case 3:
                        $stats['urgent']++;
                        break;
                }
            }
            
            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '获取统计信息失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 异步增加查看次数
     */
    private function incrementViewCount($announcements) {
        // 这里可以使用队列或异步处理来避免影响API响应速度
        // 简单实现：直接更新数据库
        try {
            foreach ($announcements as $announcement) {
                AnnouncementModel::where('id', $announcement['id'])
                    ->inc('view_count', 1)
                    ->update();
            }
        } catch (\Exception $e) {
            // 记录日志但不影响主流程
            error_log('Failed to increment view count: ' . $e->getMessage());
        }
    }
    
    /**
     * 健康检查接口
     */
    public function health() {
        try {
            $currentVersion = AnnouncementModel::getCurrentVersion();
            $lastModified = AnnouncementModel::getLastModified();
            $activeCount = count(AnnouncementModel::getActiveAnnouncements());
            
            return json([
                'code' => 0,
                'msg' => 'healthy',
                'data' => [
                    'service' => 'announcement',
                    'status' => 'ok',
                    'version' => $currentVersion,
                    'last_modified' => $lastModified,
                    'active_announcements' => $activeCount,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => 'unhealthy',
                'data' => [
                    'service' => 'announcement',
                    'status' => 'error',
                    'error' => $e->getMessage(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        }
    }
}
