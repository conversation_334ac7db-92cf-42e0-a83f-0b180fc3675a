# UpdateConfig 字段顺序修复和Checksum支持

## 问题分析

### 1. 字段顺序不匹配
**问题**：C#客户端期望的字段顺序与服务端发送的顺序不一致
- **C#客户端期望**：`DownloadURL` → `ExecutablePath` → `CheckSum`
- **服务端原发送**：`ExecutablePath` → `DownloadURL`（缺少CheckSum）

### 2. 缺少Checksum字段
**问题**：服务端没有发送文件校验和，客户端无法验证下载文件的完整性

## 解决方案

### 1. 修复字段顺序
修改服务端编码逻辑，确保字段按C#客户端期望的顺序发送：

```go
// 字段顺序：DownloadURL → ExecutablePath → Checksum
if resp.DownloadURL != "" || resp.ExecutablePath != "" {
    // 1. DownloadURL（必须先发送）
    // 2. ExecutablePath（第二个发送）  
    // 3. Checksum（第三个发送）
}
```

### 2. 添加Checksum支持

#### 数据库层面
- 为 `bns_update_config` 表添加 `checksum` 字段（VARCHAR(64)）
- 为 `bns_update_config` 表添加 `changelog_url` 字段（VARCHAR(500)）

#### 模型层面
- Go模型：`UpdateConfig` 结构体添加 `Checksum` 字段
- PHP模型：`UpdateConfig` 类添加 `checksum` 字段定义

#### 协议层面
- 二进制协议：`UpdateConfigResponse` 添加 `Checksum` 字段
- 编码逻辑：按正确顺序发送三个字段

## 数据流程

### 更新检查流程（修复后）
```
客户端发送：AppType + Version
服务端处理：版本比较
如果需要更新：
  服务端发送：ErrorCode(0) + DownloadURL + ExecutablePath + Checksum
如果不需要更新：
  服务端发送：ErrorCode(0)
如果出错：
  服务端发送：ErrorCode(非0) + ErrorMessage
```

### C#客户端读取逻辑
```csharp
ErrorCode = archive.Read<uint>();
if (ErrorCode != 0) {
    ErrorMessage = archive.ReadString();
    return;
}

// 如果需要更新会返回三个字段
if (archive.Position < archive.Length) {
    DownloadURL = archive.ReadString();      // 第1个字段
    ExecutablePath = archive.ReadString();   // 第2个字段
    var sum = archive.ReadString();          // 第3个字段
    
    if (!string.IsNullOrEmpty(sum)) {
        CheckSum = new CheckSum() { 
            Value = sum, 
            HashingAlgorithm = "SHA256" 
        };
    }
}
```

## 数据库迁移

### SQL脚本
```sql
-- 添加checksum字段
ALTER TABLE `bns_update_config` 
ADD COLUMN `checksum` VARCHAR(64) NULL COMMENT '文件校验和（SHA256）' AFTER `url`;

-- 添加changelog_url字段
ALTER TABLE `bns_update_config` 
ADD COLUMN `changelog_url` VARCHAR(500) NULL COMMENT '更新日志链接' AFTER `checksum`;
```

### 字段说明
- `checksum`：存储文件的SHA256校验和，用于验证下载文件的完整性
- `changelog_url`：存储更新日志的链接地址

## 测试验证

### 1. 字段顺序测试
- 确认服务端按 `DownloadURL` → `ExecutablePath` → `Checksum` 顺序发送
- 验证C#客户端能正确读取所有字段
- 测试空字段的处理（发送空字符串而不是跳过）

### 2. Checksum功能测试
- 在数据库中设置正确的SHA256校验和
- 验证客户端能接收到校验和信息
- 测试文件下载后的完整性验证

### 3. 兼容性测试
- 测试没有checksum的旧记录
- 验证空checksum的处理
- 确认不影响现有功能

## 配置示例

### 数据库记录示例
```sql
INSERT INTO `bns_update_config` (
    `name`, `version`, `executable_path`, `url`, 
    `checksum`, `changelog_url`, `is_active`
) VALUES (
    'BnsHelper', 
    '3.1.4', 
    '剑灵小助手.exe', 
    'https://example.com/download/3.1.4.zip',
    'a1b2c3d4e5f6...', -- SHA256校验和
    'https://example.com/changelog/3.1.4',
    1
);
```

### PHP管理界面
在更新配置管理页面添加：
- Checksum输入框（可选）
- Changelog URL输入框（可选）

## 注意事项

1. **字段顺序**：必须严格按照C#客户端期望的顺序发送
2. **空字段处理**：即使字段为空也要发送空字符串，不能跳过
3. **校验和格式**：使用SHA256格式，64位十六进制字符串
4. **向后兼容**：旧版本客户端仍能正常工作
5. **错误处理**：确保网络错误不会导致程序崩溃

## 性能影响

- **数据库**：新增两个字段，对查询性能影响微小
- **网络传输**：增加校验和字段，数据量增加约64字节
- **客户端处理**：增加校验和验证逻辑，CPU开销很小
