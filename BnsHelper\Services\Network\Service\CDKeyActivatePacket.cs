using System.Text;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;
internal class CDKeyActivatePacket : BasePacket
{
    #region Request Fields
    /// <summary>
    /// 要激活的CDkey
    /// </summary>
    public string CDKey { get; set; } = string.Empty;

    /// <summary>
    /// 是否已验证群成员身份（第一次提交false，第二次提交true）
    /// </summary>
    public bool IsVerified { get; set; } = false;
    #endregion

    #region Response Fields
    /// <summary>
    /// 需要验证的QQ群号（如果需要群验证）
    /// </summary>
    public long? Group { get; set; } = null;

    /// <summary>
    /// 更新后的权限级别（激活成功时）
    /// </summary>
    public byte? Permission { get; set; } = null;

    /// <summary>
    /// 更新后的权限过期时间（激活成功时）：-1=永久，0=无权限，>0=具体时间戳
    /// </summary>
    public long? PermissionExpiration { get; set; } = null;
    #endregion

    #region Methods
    public override DataArchiveWriter Create()
    {
        var writer = base.Create();
        writer.WriteString(CDKey, Encoding.UTF8);
        writer.Write(IsVerified);
        return writer;
    }

    protected override void ReadResponse(DataArchive reader)
    {
        // 如果有数据，需要判断是群验证响应还是激活成功响应
        if (reader.Position < reader.Length)
        {
            // 先尝试读取第一个字段
            var firstValue = reader.Read<long>();
            if (reader.Position == reader.Length)
            {
                // 只有一个long值，这是群验证响应
                Group = firstValue;
            }
            // 如果还有更多数据，说明这是激活成功响应（包含Permission和PermissionExpiration）
            else
            {
                // 第一个long是PermissionExpiration，需要重新读取
                reader.Position -= 8; // 回退8字节

                // 读取Permission（1字节）
                Permission = reader.Read<byte>();
                PermissionExpiration = reader.Read<long>();
            }
        }
    }
    #endregion
}
