<?php

namespace app\manage\model;

use think\Model;
use think\facade\Db;

/**
 * 更新配置模型
 */
class UpdateConfig extends Model
{
    protected $table = 'update_config';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'name'            => 'string',
        'version'         => 'string',
        'executable_path' => 'string',
        'url'             => 'string',
        'checksum'        => 'string',
        'plugin_version'  => 'string',
        'plugin_url'      => 'string',
        'beta'            => 'int',
        'is_active'       => 'int',
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    /**
     * 获取所有更新配置
     */
    public static function getAll()
    {
        return self::order('created_at', 'desc')->select();
    }

    /**
     * 根据应用名称获取配置（默认获取正式版）
     */
    public static function getByAppName($appName, $beta = 0)
    {
        return self::where('name', $appName)
                   ->where('beta', $beta)
                   ->where('is_active', 1)
                   ->find();
    }

    /**
     * 根据应用名称获取所有版本配置
     */
    public static function getAllVersionsByAppName($appName)
    {
        return self::where('name', $appName)
                   ->where('is_active', 1)
                   ->order('beta', 'asc')
                   ->select();
    }

    /**
     * 创建或更新配置
     */
    public static function createOrUpdate($data)
    {
        // 检查是否已存在相同应用和版本类型的配置
        $beta = isset($data['beta']) ? $data['beta'] : 0;
        $existing = self::where('name', $data['name'])
                       ->where('beta', $beta)
                       ->find();

        if ($existing) {
            // 更新现有配置
            $existing->save($data);
            return $existing;
        } else {
            // 创建新配置
            $data['beta'] = $beta;
            return self::create($data);
        }
    }

    /**
     * 删除配置
     */
    public static function deleteConfig($id)
    {
        return self::destroy($id);
    }

    /**
     * 启用/禁用配置
     */
    public static function toggleActive($id)
    {
        $config = self::find($id);
        if ($config) {
            $config->is_active = $config->is_active ? 0 : 1;
            return $config->save();
        }
        return false;
    }

    /**
     * 获取应用列表
     */
    public static function getAppList()
    {
        return self::distinct(true)->column('name');
    }
}
