<?php

namespace app\manage\model;

use think\Model;
use think\facade\Db;
use think\facade\Cache;
use app\common\service\RedisService;

/**
 * 白名单群组模型
 */
class UpdateGroup extends Model
{
    protected $table = 'bns_whitelist_group';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'group_id'    => 'string',
        'is_active'   => 'int',
        'description' => 'string',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    /**
     * 获取所有活跃群组
     */
    public static function getActiveGroups()
    {
        return self::where('is_active', 1)
                   ->order('group_id', 'asc')
                   ->select();
    }

    /**
     * 批量创建群组
     */
    public static function batchCreate($groupIds, $description = '')
    {
        $data = [];
        foreach ($groupIds as $groupId) {
            $data[] = [
                'group_id'    => trim($groupId),
                'description' => $description,
                'is_active'   => 1,
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ];
        }

        $result = self::insertAll($data);

        // 清除Groups缓存
        if ($result) {
            self::clearGroupsCache();
        }

        return $result;
    }

    /**
     * 删除所有群组
     */
    public static function deleteAll()
    {
        $result = self::where('id', '>', 0)->delete();

        // 清除Groups缓存
        if ($result) {
            self::clearGroupsCache();
        }

        return $result;
    }

    /**
     * 启用/禁用群组
     */
    public static function toggleActive($id)
    {
        $group = self::find($id);
        if ($group) {
            $group->is_active = $group->is_active ? 0 : 1;
            $result = $group->save();

            // 清除Groups缓存
            if ($result) {
                self::clearGroupsCache();
            }

            return $result;
        }
        return false;
    }

    /**
     * 获取群组统计信息
     */
    public static function getStats($appName = null)
    {
        $query = self::field('name, group_type, count(*) as count')
                     ->where('is_active', 1)
                     ->group('name, group_type');
        
        if ($appName) {
            $query->where('name', $appName);
        }
        
        return $query->select();
    }

    /**
     * 清除Groups缓存
     */
    private static function clearGroupsCache()
    {
        $groupsCacheKey = "active_groups";

        try {
            // 删除Redis缓存
            RedisService::delete($groupsCacheKey);
            // 删除PHP本地缓存
            Cache::delete($groupsCacheKey);
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            trace('Failed to clear groups cache: ' . $e->getMessage(), 'error');
        }
    }
}
