using System.Diagnostics;
using System.IO;
using System.Text.Json;
using Xylia.BnsHelper.Views;

namespace Xylia.BnsHelper.Services;
internal class UserAgreementService : IService
{
    private readonly string? _agreementFilePath;
    private readonly string _currentVersion = "1.0.1"; // 协议版本

    public UserAgreementService()
    {
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Xylia");
        Directory.CreateDirectory(appDataPath);
        _agreementFilePath = Path.Combine(appDataPath, "agreement.json");

        // 检查用户是否已同意当前版本的协议
        if (!HasUserAgreed())
        {
            // 使用自定义的用户协议窗口，带5秒等待时间
            var agreed = AgreementWindow.ShowAgreement();
            if (agreed)
            {
                SaveUserAgreement();
            }
            else
            {
                Environment.Exit(0);
            }
        }
    }

    public bool HasUserAgreed()
    {
        try
        {
            if (!File.Exists(_agreementFilePath)) return false;

            var json = File.ReadAllText(_agreementFilePath);
            var agreementData = JsonSerializer.Deserialize<UserAgreementData>(json);
            if (agreementData == null) return false;

            // 检查版本是否匹配
            bool hasAgreed = agreementData.Version == _currentVersion && agreementData.IsAgreed;

            Debug.WriteLine($"[INFO] 用户协议检查: 版本={agreementData.Version}, 当前版本={_currentVersion}, 已同意={agreementData.IsAgreed}, 结果={hasAgreed}");
            return hasAgreed;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 检查用户协议失败: {ex.Message}");
            return false;
        }
    }

    public void SaveUserAgreement()
    {
        try
        {
            var agreementData = new UserAgreementData
            {
                Version = _currentVersion,
                IsAgreed = true,
                AgreedTime = DateTime.Now
            };

            var json = JsonSerializer.Serialize(agreementData, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            File.WriteAllText(_agreementFilePath, json);
            Debug.WriteLine($"[INFO] 用户协议已记录: 版本={_currentVersion}, 时间={agreementData.AgreedTime}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 记录用户协议失败: {ex.Message}");
        }
    }

    public void Register()
    {
       
    }

    /// <summary>
    /// 用户协议数据结构（用于序列化）
    /// </summary>
    private class UserAgreementData
    {
        public string Version { get; set; } = string.Empty;
        public bool IsAgreed { get; set; }
        public DateTime AgreedTime { get; set; }
    }
}
