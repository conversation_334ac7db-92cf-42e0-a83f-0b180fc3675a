using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using CommunityToolkit.Mvvm.Input;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services;

namespace Xylia.BnsHelper.ViewModels;

/// <summary>
/// 公告窗口视图模型
/// </summary>
public partial class AnnouncementViewModel : INotifyPropertyChanged
{
    private readonly AnnouncementService _announcementService;

    public AnnouncementViewModel()
    {
        _announcementService = AnnouncementService.Instance;
        
        // 监听公告变化
        _announcementService.Announcements.CollectionChanged += (s, e) => 
        {
            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnread));
        };

        // 监听单个公告的已读状态变化
        foreach (var announcement in _announcementService.Announcements)
        {
            announcement.PropertyChanged += Announcement_PropertyChanged;
        }
    }

    /// <summary>
    /// 公告列表
    /// </summary>
    public ObservableCollection<Announcement> Announcements => _announcementService.Announcements;

    /// <summary>
    /// 未读公告数量
    /// </summary>
    public int UnreadCount => _announcementService.UnreadCount;

    /// <summary>
    /// 是否有未读公告
    /// </summary>
    public bool HasUnread => UnreadCount > 0;

    /// <summary>
    /// 标记为已读命令
    /// </summary>
    [RelayCommand]
    private void MarkAsRead(string announcementId)
    {
        if (!string.IsNullOrEmpty(announcementId))
        {
            _announcementService.MarkAsRead(announcementId);
        }
    }

    /// <summary>
    /// 全部标记为已读命令
    /// </summary>
    [RelayCommand]
    private void MarkAllAsRead()
    {
        _announcementService.MarkAllAsRead();
    }

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        try
        {
            await _announcementService.RefreshAnnouncementsAsync();

            // 重新绑定事件
            foreach (var announcement in _announcementService.Announcements)
            {
                announcement.PropertyChanged -= Announcement_PropertyChanged;
                announcement.PropertyChanged += Announcement_PropertyChanged;
            }
        }
        catch (Exception ex)
        {
            // 可以在这里显示错误提示
            System.Diagnostics.Debug.WriteLine($"刷新公告失败: {ex.Message}");
        }
    }

    private void Announcement_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(Announcement.IsRead))
        {
            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnread));
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
