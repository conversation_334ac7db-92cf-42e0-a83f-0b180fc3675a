package handler

import (
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// UpdateHandler 更新配置处理器
type UpdateHandler struct {
	updateService *service.UpdateService
}

// NewUpdateHandler 创建更新配置处理器
func NewUpdateHandler(updateService *service.UpdateService) *UpdateHandler {
	return &UpdateHandler{
		updateService: updateService,
	}
}

// HandleUpdateConfig 处理更新配置请求
func (h *UpdateHandler) HandleUpdateConfig(token string, appType uint8, version, clientIP string) ([]byte, error) {
	// 根据appType确定应用名称
	var appName string
	switch appType {
	case 0x1:
		appName = "bns-helper"
	default:
		logger.Warn("Unknown app type %d in update config request from %s", appType, clientIP)
		return h.createErrorResponse(binary.ErrorCodeInvalidFormat, "未知的应用类型")
	}

	// 获取更新配置
	config, err := h.updateService.GetUpdateConfig(appName, version)
	if err != nil {
		logger.Error("Failed to get update config for app %s: %v", appName, err)
		return h.createErrorResponse(binary.ErrorCodeServerError, "获取更新配置信息失败")
	}
	
	if config == nil {
		logger.Warn("Update configuration not found for app: %s", appName)
		return h.createErrorResponse(binary.ErrorCodeInvalidFormat, "获取更新配置信息失败")
	}
	
	// 转换为二进制协议响应格式
	response := &binary.UpdateConfigResponse{
		ExecutablePath: config.ExecutablePath,
		DownloadURL:    config.DownloadURL,
		Checksum:       config.Checksum,
		Groups:         config.Groups,
		ErrorCode:      binary.ErrorCodeSuccess,
	}
	
	// 创建响应消息
	responseMsg := binary.NewUpdateConfigResponseMessage(response)

	// 编码消息并应用压缩和加密
	responseData, err := responseMsg.EncodeWithCompressionAndEncryption()
	if err != nil {
		logger.Error("Failed to encode update config response: %v", err)
		return h.createErrorResponse(binary.ErrorCodeServerError, "Failed to encode response")
	}
	
	logger.Info("Update config response sent for app %s to %s", appName, clientIP)
	return responseData, nil
}

// createErrorResponse 创建错误响应
func (h *UpdateHandler) createErrorResponse(errorCode uint32, errorMessage string) ([]byte, error) {
	response := &binary.UpdateConfigResponse{
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
	}
	
	responseMsg := binary.NewUpdateConfigResponseMessage(response)
	return responseMsg.EncodeWithCompressionAndEncryption()
}
