<?php
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;

echo "=== 检查update_config表数据 ===\n";

try {
    $configs = Db::table('update_config')->select();
    
    if (empty($configs)) {
        echo "表中没有数据\n";
    } else {
        echo "找到 " . count($configs) . " 条记录:\n\n";
        
        foreach ($configs as $config) {
            echo "ID: " . $config['id'] . "\n";
            echo "Name: " . $config['name'] . "\n";
            echo "Version: " . $config['version'] . "\n";
            echo "PluginVersion: " . ($config['plugin_version'] ?? 'NULL') . "\n";
            echo "PluginURL: " . ($config['plugin_url'] ?? 'NULL') . "\n";
            echo "IsActive: " . $config['is_active'] . "\n";
            echo "ExecutablePath: " . $config['executable_path'] . "\n";
            echo "URL: " . $config['url'] . "\n";
            echo "---\n";
        }
    }
    
    echo "\n=== 检查bns_whitelist_group表数据 ===\n";
    
    $groups = Db::table('bns_whitelist_group')->where('is_active', 1)->select();
    
    if (empty($groups)) {
        echo "白名单群组表中没有数据\n";
    } else {
        echo "找到 " . count($groups) . " 个活跃群组:\n\n";
        
        foreach ($groups as $group) {
            echo "ID: " . $group['id'] . "\n";
            echo "GroupID: " . $group['group_id'] . "\n";
            echo "IsActive: " . $group['is_active'] . "\n";
            echo "Description: " . ($group['description'] ?? 'NULL') . "\n";
            echo "---\n";
        }
    }
    
} catch (Exception $e) {
    echo "查询失败: " . $e->getMessage() . "\n";
}
?>
