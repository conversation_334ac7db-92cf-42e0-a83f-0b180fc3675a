<?php

namespace app\service\controller;

use think\App;
use think\Request;
use think\Exception;
use think\facade\Cache;
use app\common\controller\ApiBase as Base;
use app\common\service\RedisService;
use app\manage\model\User as User;
use app\manage\model\UserWhiteList as UserWhiteList;


class Update extends Base 
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    private function groupsToString(array $group_data): string {
        $allGroups = [];
        foreach ($group_data as $ids) {
            $allGroups = array_merge($allGroups, $ids);
        }
        return implode(';', $allGroups) . ';';
    }

    public function Main() {
        $this->request_log();
        $app = $this->request->get('app');
        $version = $this->request->get('version');
        $download = !empty($this->request->get('download'));
        $time = time();
        $realm = 'bnszs_api';

        try {
            if($app === "bns-helper") {
                if (version_compare($version, '3.1.0', '<')) {
                    abort(400, '请到助手官网重新下载最新版本');
                }

                // 从Redis缓存获取更新配置（Go服务端写入的缓存）
                $cacheKey = "update_config:{$app}";

                try {
                    $cachedConfig = RedisService::get($cacheKey);

                    if ($cachedConfig) {
                        // 解析JSON数据
                        $response = json_decode($cachedConfig, true);

                        if ($response && json_last_error() === JSON_ERROR_NONE) {
                            // 如果缓存中没有groups字段，使用默认群组数据作为后备
                            if (empty($response['groups'])) {
                                $group_data = [
                                    'zhushou' => ['1053141182','548810086','563768233','618986361', '131576586','1047130415','513561455','1054914017','874484842'],
                                    '12yue'   => ['822788266','187859675','594742972','938053157','594742972','297744401','568355882','672059443','317079287','34824317','1097068019'],
                                    'pangxie' => ["692230935","7684524","951522389","667801772","489257607","557692134","763876189","936598570","668534730","1032100694","3337091"],
                                    'zhubo'   => ['543487386','643231117']
                                ];
                                $response['groups'] = $this->groupsToString($group_data);
                            }

                            return json($response);
                        }
                    }
                } catch (\Exception $e) {
                    $this->log('Redis cache error: ' . $e->getMessage());
                }

                // 缓存不可用时，使用原有的硬编码配置作为后备
                $this->log('Using fallback config for app: ' . $app);

                $group_data = [
                    'zhushou' => ['1053141182','548810086','563768233','618986361', '131576586','1047130415','513561455','1054914017','874484842'],
                    '12yue'   => ['822788266','187859675','594742972','938053157','594742972','297744401','568355882','672059443','317079287','34824317','1097068019'],
                    'pangxie' => ["692230935","7684524","951522389","667801772","489257607","557692134","763876189","936598570","668534730","1032100694","3337091"],
                    'zhubo'   => ['543487386','643231117']
                ];

                $groupsString = $this->groupsToString($group_data);
                $response = [
                    "CurrentVersion" => "3.1.4",
                    "ExecutablePath" => "剑灵小助手.exe",
                    "DownloadURL"    => "https://gitee.com/XyliaUp/tools/releases/download/release/3.1.4.zip",
                    "PluginVersion"  => "0.1.2506.1",
                    "PluginUrl"      => "https://gitee.com/XyliaUp/tools/releases/download/release/libiconv2017_cl64.dll",
                    "groups"  =>  $groupsString
                ];
                return json($response);


                
                // 要求输入信息
                if (!isset($_SERVER['PHP_AUTH_DIGEST']) || !$this->check($app)) {
                    // 是否可以传递服务器时间给用户
                    header('HTTP/1.0 401 Unauthorized');
                    header('WWW-Authenticate: Digest realm="'.$realm.'",qop="auth", nonce="'.uniqid().'", opaque="'.md5($realm).'"');
                    header('Date: ' . time()); 
                    exit;
                }

                // 验证请求
                $data = $this->http_digest_parse($_SERVER['PHP_AUTH_DIGEST']);
                $name = $data['username'];
                $clientIP = $this->request->ip();

                // 检查单IP的并发访问限制
                $ipLimitKey = "ip_limit:{$clientIP}";
                $currentCount = RedisService::get($ipLimitKey) ?: 0;
                if ($currentCount > 5) {
                    abort(403, '当前IP访问用户过多，请稍后再试');
                }
                RedisService::inc($ipLimitKey);
                RedisService::expire($ipLimitKey, 300);

                // 验证Token数据
                $user = User::LoginByToken($name, $realm, $data);
                $response = [
                    "CurrentVersion" => "*******",
                    "ExecutablePath" => "剑灵小助手.exe",
                    "ChangelogURL"	 => "https://tools.bnszs.com/manage/changelog",
                    "DownloadURL"    => "https://gitee.com/XyliaUp/tools/releases/download/v3.2/*******.zip",
                    "PluginVersion"  => "0.1.2505.3",
                    "PluginUrl"      => "",
                    "UserCount"	     => RedisService::hLen('user'),
                ];
            } elseif($app === "bns-preview-tools") {
                if (!$download) {
                    // 请求时版本号是必须的
                    if (version_compare($version, '1', '<')) {
                        abort(400);
                    }
                    if ($time > 1750291200 && $time < 1750311000) {
                        return response('服务器维护中 (8:00 - 13:30)', 400);
                    }
                }

                // normal mode
                $response = [   
                    "Time"     => $time,
                    "NoticeId" => 2,
                    "Notice"   => "",
                    "Signature"     => md5($time),
                    "CurrentVersion"=> "3.2.5.9301",
                    "DownloadURL"   => "https://gitee.com/XyliaUp/tools/releases/download/v3.2/3.2.5.9301.zip",
                    "ExecutablePath" => "Preview.UI.exe",
                    "User"  => UserWhiteList::Get('preview'),
                ];

                if ($download) {
                    header('location:' . $response["DownloadURL"]);
                    return;
                }
            } else {
                abort(400);
            }

            return json($response);
        }
        catch(\think\exception\HttpException $e) {
            $this->log($e->getStatusCode() .' '. $e->getMessage());
            return response($e->getMessage(), $e->getStatusCode());
        }
        catch(\Exception $e) {
            $this->log('500 '. $e->getMessage());
            return response($e->getMessage(), 500);
        }
    }



    function http_digest_parse($txt) {
        $needed_parts = array('nonce'=>1, 'nc'=>1, 'cnonce'=>1, 'qop'=>1, 'username'=>1, 'uri'=>1, 'response'=>1);
        $data = array();

        preg_match_all('@(\w+)=(?:([\'"])([^$2]+)$2|([^\s,]+))@', $txt, $matches, PREG_SET_ORDER);
        foreach ($matches as $m) {
            $data[$m[1]] = $m[3] ? trim($m[3],"\",'") : trim($m[4],"\",'"); 
            unset($needed_parts[$m[1]]);
        }
        return $needed_parts ? false : $data;
    }
}
