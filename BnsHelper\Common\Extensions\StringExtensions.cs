﻿using System.Security.Cryptography;
using System.Text;

namespace Xylia.BnsHelper.Common.Extensions;
internal static class StringExtensions
{
    internal static string? Hash(this string source, Encoding? encoding = null)
    {
        ArgumentNullException.ThrowIfNull(source);
        encoding ??= Encoding.UTF8;

        var data = MD5.HashData(encoding.GetBytes(source));
        var str = new StringBuilder();

        for (int i = 0; i < data.Length; i++)
        {
            str.Append(data[i].ToString("x2"));
        }

        return str.ToString();
    }

    internal static string MD5Sign(this string source, string secretKey)
    {
        ArgumentNullException.ThrowIfNull(source);
        ArgumentNullException.ThrowIfNull(secretKey);

        var combined = secretKey + "|" + source;
        return combined.Hash(Encoding.UTF8);
    }

    // 验证MD5签名
    internal static bool VerifyMD5Sign(this string source, string signature, string secretKey)
    {
        ArgumentNullException.ThrowIfNull(source);
        ArgumentNullException.ThrowIfNull(signature);
        ArgumentNullException.ThrowIfNull(secretKey);

        try
        {
            // 重新计算签名并比较
            var expectedSignature = source.MD5Sign(secretKey);
            return string.Equals(signature, expectedSignature, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }
}
