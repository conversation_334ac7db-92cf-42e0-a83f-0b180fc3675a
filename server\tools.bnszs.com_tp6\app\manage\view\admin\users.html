{layout name="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf">
				<strong class="am-text-primary am-text-lg">用户管理</strong> / <small>User Management</small>
			</div>
		</div>
		<hr>

		<!-- 统计卡片 -->
		<div class="am-g">
			<div class="am-u-sm-6 am-u-md-2">
				<div class="stats-card">
					<div class="stats-number">{$stats.total}</div>
					<div class="stats-label">总用户数</div>
				</div>
			</div>
			<div class="am-u-sm-6 am-u-md-2">
				<div class="stats-card">
					<div class="stats-number status-normal">{$stats.normal}</div>
					<div class="stats-label">正常用户</div>
				</div>
			</div>
			<div class="am-u-sm-6 am-u-md-2">
				<div class="stats-card">
					<div class="stats-number status-banned">{$stats.banned}</div>
					<div class="stats-label">异常用户</div>
				</div>
			</div>
			<div class="am-u-sm-6 am-u-md-2">
				<div class="stats-card">
					<div class="stats-number status-premium">{$stats.premium}</div>
					<div class="stats-label">高级用户</div>
				</div>
			</div>
		</div>
		<!-- 搜索和筛选 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">搜索和筛选</div>
			<div class="am-panel-bd">
				<form class="am-form am-form-inline" id="search-form">
					<div class="am-form-group">
						<input type="text" class="am-form-field" id="search_value" placeholder="UID/QQ号/用户名"
							   value="{$current_search}" style="width: 200px;">
					</div>

					<div class="am-form-group">
						<select class="am-form-field" id="status_filter" style="width: 120px;">
							<option value="">全部状态</option>
							<option value="0" {if condition="$current_status === '0'"}selected{/if}>正常</option>
							<option value="1" {if condition="$current_status === '1'"}selected{/if}>临时封禁</option>
							<option value="2" {if condition="$current_status === '2'"}selected{/if}>永久封禁</option>
							<option value="3" {if condition="$current_status === '3'"}selected{/if}>审核状态</option>
						</select>
					</div>

					<div class="am-form-group">
						<select class="am-form-field" id="permission_filter" style="width: 120px;">
							<option value="">全部权限</option>
							<option value="0" {if condition="$current_permission === '0'"}selected{/if}>普通用户</option>
							<option value="1" {if condition="$current_permission === '1'"}selected{/if}>高级用户</option>
							<option value="2" {if condition="$current_permission === '2'"}selected{/if}>会员用户</option>
						</select>
					</div>

					<button type="button" class="am-btn am-btn-primary" onclick="search();">
						<i class="am-icon-search"></i> 搜索
					</button>

					<button type="button" class="am-btn am-btn-default" onclick="clearSearch();">
						<i class="am-icon-refresh"></i> 重置
					</button>

					<div class="am-fr">
						<a href="/manage/admin/online" class="am-btn am-btn-primary">
							<i class="am-icon-line-chart"></i> 在线统计
						</a>
						<button type="button" class="am-btn am-btn-success" data-bs-toggle="modal" data-bs-target="#register">
							<i class="am-icon-plus"></i> 注册账号
						</button>
						<button type="button" class="am-btn am-btn-warning" onclick="showBatchModal();">
							<i class="am-icon-cogs"></i> 批量操作
						</button>
					</div>
				</form>
			</div>
		</div>
		<!-- 用户列表 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">
				用户列表
				<small>（共 {$data->total()} 个用户，当前第 {$data->currentPage()} 页）</small>
				<div class="am-fr">
					<label class="am-checkbox-inline">
						<input type="checkbox" id="select-all"> 全选
					</label>
				</div>
			</div>
			<div class="am-panel-bd">
				<div class="am-scrollable-horizontal">
					<table class="am-table am-table-bd am-table-striped admin-content-table">
						<thead>
							<tr>
								<th width="40"><input type="checkbox" id="select-all-header"></th>
								<th width="80">UID</th>
								<th width="120">QQ号</th>
								<th width="150">用户名</th>
								<th width="100">状态</th>
								<th width="120">封禁来源</th>
								<th width="100">权限</th>
								<th width="150">权限过期时间</th>
								<th width="120">最后登录</th>
								<th width="200">操作</th>
							</tr>
						</thead>
						<tbody>
							{volist name='data' id='item'}
							<tr>
								<td><input type="checkbox" class="user-checkbox" value="{$item.uid}"></td>
								<td><strong>{$item.uid}</strong></td>
								<td>{$item.uin}</td>
								<td>
									<span class="user-name">{$item.name}</span>
									{if condition="$item.beta"}
									<span class="am-badge am-badge-secondary am-badge-xs">测试</span>
									{/if}
								</td>
								<td>
									{if condition="$item.status == 0"}
									<span class="am-badge am-badge-success">正常</span>
									{elseif condition="$item.status == 1"}
									<span class="am-badge am-badge-warning">临时封禁</span>
									{elseif condition="$item.status == 2"}
									<span class="am-badge am-badge-danger">永久封禁</span>
									{elseif condition="$item.status == 3"}
									<span class="am-badge am-badge-secondary">审核状态</span>
									{else}
									<span class="am-badge">未知({$item.status})</span>
									{/if}
								</td>
									<td>
										{if condition="$item.status > 0"}
											{if condition="isset($item.ban_source)"}
												{if condition="$item.ban_source == 'device'"}
													<span class="am-badge am-badge-warning am-badge-xs">设备传播</span>
												{elseif condition="$item.ban_source == 'ip'"}
													<span class="am-badge am-badge-danger am-badge-xs">IP传播</span>
												{elseif condition="$item.ban_source == 'manual'"}
													<span class="am-badge am-badge-secondary am-badge-xs">手动封禁</span>
												{else}
													<span class="am-badge am-badge-default am-badge-xs">{$item.ban_source}</span>
												{/if}
												{if condition="isset($item.ban_reason)"}
													<br><small class="am-text-muted">{$item.ban_reason}</small>
												{/if}
											{else}
												<span class="am-text-muted">-</span>
											{/if}
										{else}
											<span class="am-text-muted">-</span>
										{/if}
									</td>
								<td>
									{if condition="$item.permission == 0"}
									<span class="am-badge">普通</span>
									{elseif condition="$item.permission == 1"}
									<span class="am-badge am-badge-primary">高级</span>
									{elseif condition="$item.permission == 2"}
									<span class="am-badge am-badge-success">会员</span>
									{else}
									<span class="am-badge">未知({$item.permission})</span>
									{/if}
								</td>
								<td>
									<?php
									$expiration = \app\manage\model\User::GetExpiration($item['uid'],'client');
									if ($expiration == -1) {
										echo '<span class="am-text-success">永久</span>';
									} else if ($expiration == 0) {
										echo '<span class="am-text-danger">无权限</span>';
									} else {
										echo date("Y-m-d H:i", $expiration);
									}
									?>
								</td>
								<td>
									<?php
									if ($item->login_time) {
										echo date("Y-m-d H:i", $item->login_time);
									} else {
										echo '<span class="am-text-muted">从未登录</span>';
									}
									?>
								</td>
								<td>
									<div class="am-btn-toolbar">
										<div class="am-btn-group am-btn-group-xs">
											<button class="am-btn am-btn-default am-btn-xs" onclick="editUser({$item.uid})">
												<i class="am-icon-edit"></i>
											</button>
											<button class="am-btn am-btn-primary am-btn-xs" onclick="preCdkey({$item.uid},'{$item.name}')">
												<i class="am-icon-key"></i>
											</button>
											<button class="am-btn am-btn-secondary am-btn-xs" onclick="clearUserCache({$item.uid})">
												<i class="am-icon-trash"></i>
											</button>
										</div>
									</div>
								</td>
							</tr>
							{/volist}
						</tbody>
					</table>
				</div>

				<!-- 分页 -->
				<div class="am-margin-top pagination-wrapper">
					<div class="am-u-sm-12 am-u-md-6">
						<div class="am-datatable-info">
							显示第 {$data->firstItem()} 到 {$data->lastItem()} 条记录，共 {$data->total()} 条
						</div>
					</div>
					<div class="am-u-sm-12 am-u-md-6">
						<div class="am-datatable-paging am-fr">
							{$data->appends(request()->param())->render()}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- 注册用户模态框 -->
<div class="modal fade" id="register">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">注册用户账号</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<form class="am-form">
					<div class="am-form-group">
						<label for="register-uin">QQ号 *</label>
						<input type="text" id="register-uin" class="am-form-field" placeholder="请输入QQ号" required>
						<small class="am-text-xs">将作为用户的登录账号</small>
					</div>
					<div class="am-form-group">
						<label for="register-name">用户名</label>
						<input type="text" id="register-name" class="am-form-field" placeholder="请输入用户名（可选）">
						<small class="am-text-xs">留空则使用QQ号作为用户名</small>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="am-btn am-btn-default" data-bs-dismiss="modal">
					<i class="am-icon-close"></i> 取消
				</button>
				<button type="button" class="am-btn am-btn-primary" onclick="register();">
					<i class="am-icon-save"></i> 注册
				</button>
			</div>
		</div>
	</div>
</div>
<!-- CDKEY绑定模态框 -->
<div class="modal fade" id="cdkey">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">绑定CDKEY</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<div class="am-alert am-alert-secondary">
					<p><strong>用户信息：</strong></p>
					<p>UID: <span id="cdkey-uid"></span></p>
					<p>用户名: <span id="cdkey-name"></span></p>
				</div>
				<form class="am-form">
					<div class="am-form-group">
						<label for="cdkey-data">CDKEY *</label>
						<input type="text" id="cdkey-data" class="am-form-field" placeholder="请输入CDKEY" required>
						<small class="am-text-xs">请确保CDKEY有效且未被使用</small>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="am-btn am-btn-default" data-bs-dismiss="modal">
					<i class="am-icon-close"></i> 取消
				</button>
				<button type="button" class="am-btn am-btn-primary" onclick="bindCdkey();">
					<i class="am-icon-key"></i> 绑定
				</button>
			</div>
		</div>
	</div>
</div>

<!-- 用户编辑模态框 -->
<div class="modal fade" id="editUser">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">编辑用户</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<div class="am-alert am-alert-secondary">
					<p><strong>用户信息：</strong></p>
					<p>UID: <span id="edit-uid"></span></p>
					<p>QQ号: <span id="edit-uin"></span></p>
					<p>用户名: <span id="edit-name"></span></p>
				</div>
				<form class="am-form">
					<div class="am-form-group">
						<label for="edit-status">用户状态</label>
						<select id="edit-status" class="am-form-field">
							<option value="0">正常</option>
							<option value="1">临时封禁</option>
							<option value="2">永久封禁</option>
							<option value="3">审核状态</option>
						</select>
					</div>
					<div class="am-form-group">
						<label for="edit-permission">权限级别</label>
						<select id="edit-permission" class="am-form-field">
							<option value="0">普通用户</option>
							<option value="1">高级用户</option>
							<option value="2">会员用户</option>
						</select>
					</div>
					<div class="am-form-group">
						<label for="edit-reason">操作原因</label>
						<textarea id="edit-reason" class="am-form-field" rows="3" placeholder="请输入操作原因（可选）"></textarea>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="am-btn am-btn-default" data-bs-dismiss="modal">
					<i class="am-icon-close"></i> 取消
				</button>
				<button type="button" class="am-btn am-btn-primary" onclick="updateUser();">
					<i class="am-icon-save"></i> 保存
				</button>
			</div>
		</div>
	</div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchOperation">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">批量操作</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<div class="am-alert am-alert-warning">
					<p><strong>注意：</strong>批量操作将影响所有选中的用户，请谨慎操作！</p>
					<p>已选中 <span id="selected-count">0</span> 个用户</p>
				</div>
				<form class="am-form">
					<div class="am-form-group">
						<label for="batch-operation">操作类型</label>
						<select id="batch-operation" class="am-form-field">
							<option value="">请选择操作</option>
							<option value="status">修改状态</option>
						</select>
					</div>
					<div class="am-form-group" id="batch-status-group" style="display: none;">
						<label for="batch-status">新状态</label>
						<select id="batch-status" class="am-form-field">
							<option value="0">正常</option>
							<option value="1">临时封禁</option>
							<option value="2">永久封禁</option>
							<option value="3">审核状态</option>
						</select>
					</div>
					<div class="am-form-group">
						<label for="batch-reason">操作原因</label>
						<textarea id="batch-reason" class="am-form-field" rows="3" placeholder="请输入操作原因"></textarea>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="am-btn am-btn-default" data-bs-dismiss="modal">
					<i class="am-icon-close"></i> 取消
				</button>
				<button type="button" class="am-btn am-btn-danger" onclick="executeBatchOperation();">
					<i class="am-icon-cogs"></i> 执行操作
				</button>
			</div>
		</div>
	</div>
</div>

<style>
.stats-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stats-number {
    font-size: 2em;
    font-weight: bold;
    color: #0e90d2;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9em;
}

.status-normal {
    color: #5cb85c;
}

.status-banned {
    color: #d9534f;
}

.status-premium {
    color: #f0ad4e;
}

.user-name {
    font-weight: bold;
}

.am-scrollable-horizontal {
    overflow-x: auto;
}

.admin-content-table {
    min-width: 1000px;
}

.admin-content-table th,
.admin-content-table td {
    white-space: nowrap;
    vertical-align: middle;
}

.am-btn-toolbar .am-btn-group {
    margin-right: 5px;
}

.am-form-inline .am-form-group {
    margin-right: 15px;
    margin-bottom: 10px;
}

.am-panel-hd .am-fr {
    float: right;
}

.selected-row {
    background-color: #f5f5f5;
}

/* 分页样式优化 */
.pagination-wrapper {
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid #ddd;
}

.am-datatable-info {
    color: #666;
    line-height: 34px;
}

.am-datatable-paging .pagination {
    margin: 0;
    display: inline-block;
}

.pagination li {
    display: inline-block;
    margin: 0 2px;
}

.pagination li a,
.pagination li span {
    display: inline-block;
    padding: 6px 12px;
    line-height: 1.42857143;
    color: #337ab7;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
}

.pagination li a:hover {
    color: #23527c;
    background-color: #eee;
    border-color: #ddd;
}

.pagination li.active span {
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.pagination li.disabled span {
    color: #777;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed;
}

/* 筛选栏样式优化 */
.filter-controls {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.filter-controls .am-form-group {
    margin-right: 15px;
    margin-bottom: 10px;
}

.filter-controls .am-form-field {
    border-radius: 4px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-controls .am-form-field:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 状态标签样式 */
.am-badge-xs {
    font-size: 0.75em;
    padding: 0.25em 0.4em;
}

/* 表格样式优化 */
.admin-content-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.admin-content-table td {
    vertical-align: middle;
}

.admin-content-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* 操作按钮样式 */
.action-buttons .am-btn {
    margin-right: 5px;
    margin-bottom: 2px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .filter-controls .am-form-group {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }

    .filter-controls .am-form-field {
        width: 100%;
    }

    .pagination-wrapper .am-u-sm-12 {
        text-align: center;
        margin-bottom: 10px;
    }

    .am-datatable-paging {
        text-align: center !important;
    }

    .admin-content-table {
        font-size: 12px;
    }

    .admin-content-table th,
    .admin-content-table td {
        padding: 8px 4px;
    }
}
</style>

<script>
$(document).ready(function() {
    // 初始化
    initializeEventHandlers();
    updateSelectedCount();
});

// 初始化事件处理器
function initializeEventHandlers() {
    // 搜索框回车事件
    $('#search_value').bind('keypress', function(event) {
        if (event.keyCode == "13") {
            event.preventDefault();
            search();
        }
    });

    // 全选功能
    $('#select-all, #select-all-header').change(function() {
        var isChecked = $(this).prop('checked');
        $('.user-checkbox').prop('checked', isChecked);
        updateSelectedCount();
        updateRowHighlight();
    });

    // 单个复选框变化
    $(document).on('change', '.user-checkbox', function() {
        updateSelectedCount();
        updateRowHighlight();

        // 更新全选状态
        var totalCheckboxes = $('.user-checkbox').length;
        var checkedCheckboxes = $('.user-checkbox:checked').length;
        $('#select-all, #select-all-header').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // 批量操作类型变化
    $('#batch-operation').change(function() {
        var operation = $(this).val();
        if (operation === 'status') {
            $('#batch-status-group').show();
        } else {
            $('#batch-status-group').hide();
        }
    });
}

// 搜索功能
function search() {
    var searchValue = $("#search_value").val();
    var statusFilter = $("#status_filter").val();
    var permissionFilter = $("#permission_filter").val();

    var params = [];
    if (searchValue) params.push("search=" + encodeURIComponent(searchValue));
    if (statusFilter !== '') params.push("status=" + statusFilter);
    if (permissionFilter !== '') params.push("permission=" + permissionFilter);

    var url = window.location.pathname;
    if (params.length > 0) {
        url += "?" + params.join("&");
    }

    window.location.href = url;
}

// 清除搜索
function clearSearch() {
    $("#search_value").val('');
    $("#status_filter").val('');
    $("#permission_filter").val('');
    window.location.href = window.location.pathname;
}

// 注册用户
function register() {
    var uin = $('#register-uin').val().trim();
    var name = $('#register-name').val().trim();

    if (!uin) {
        layer.msg('请输入QQ号', {icon: 2});
        return;
    }

    if (!/^\d{5,11}$/.test(uin)) {
        layer.msg('QQ号格式不正确', {icon: 2});
        return;
    }

    layer.confirm('确定要注册此用户吗？', {icon: 3, title: '确认注册'}, function(index) {
        $.ajax({
            type: "POST",
            dataType: "json",
            url: window.location.href,
            data: {
                mode: "register",
                uin: uin,
                name: name || uin
            },
            success: function (result) {
                if (result.code == 1) {
                    layer.msg('注册成功', {icon: 1});
                    $('#register').modal('hide');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    layer.msg('注册失败: ' + result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，注册失败', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 准备CDKEY绑定
function preCdkey(uid, name) {
    $('#cdkey-uid').text(uid);
    $('#cdkey-name').text(name);
    $('#cdkey-data').val('');
    $('#cdkey').modal('show');
}

// 绑定CDKEY
function bindCdkey() {
    var uid = $('#cdkey-uid').text();
    var cdkey = $('#cdkey-data').val().trim();

    if (!cdkey) {
        layer.msg('请输入CDKEY', {icon: 2});
        return;
    }

    layer.confirm('确定要绑定此CDKEY吗？', {icon: 3, title: '确认绑定'}, function(index) {
        $.ajax({
            type: "POST",
            dataType: "json",
            url: window.location.href,
            data: {
                mode: 'cdkey',
                uid: uid,
                data: cdkey
            },
            success: function (result) {
                if (result.code == 1) {
                    layer.msg('绑定成功', {icon: 1});
                    $('#cdkey').modal('hide');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    layer.msg('绑定失败: ' + result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，绑定失败', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 清除用户缓存
function clearUserCache(uid) {
    layer.confirm('确定要清除此用户的所有缓存吗？', {icon: 3, title: '确认清除'}, function(index) {
        $.ajax({
            type: "POST",
            dataType: "json",
            url: window.location.href,
            data: {
                mode: 'clearCache',
                uid: uid
            },
            success: function (result) {
                if (result.code == 1) {
                    layer.msg('缓存清除成功', {icon: 1});
                } else {
                    layer.msg('清除失败: ' + result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，清除失败', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 编辑用户
function editUser(uid) {
    // 从表格中获取用户信息
    var row = $('input[value="' + uid + '"]').closest('tr');
    var uin = row.find('td:eq(2)').text();
    var name = row.find('.user-name').text();
    var statusBadge = row.find('td:eq(4) .am-badge');
    var permissionBadge = row.find('td:eq(5) .am-badge');

    // 解析当前状态
    var currentStatus = 0;
    if (statusBadge.hasClass('am-badge-warning')) currentStatus = 1;
    else if (statusBadge.hasClass('am-badge-danger')) currentStatus = 2;
    else if (statusBadge.hasClass('am-badge-secondary')) currentStatus = 3;

    // 解析当前权限
    var currentPermission = 0;
    if (permissionBadge.hasClass('am-badge-primary')) currentPermission = 1;
    else if (permissionBadge.hasClass('am-badge-success')) currentPermission = 2;

    // 填充模态框
    $('#edit-uid').text(uid);
    $('#edit-uin').text(uin);
    $('#edit-name').text(name);
    $('#edit-status').val(currentStatus);
    $('#edit-permission').val(currentPermission);
    $('#edit-reason').val('');

    $('#editUser').modal('show');
}

// 更新用户信息
function updateUser() {
    var uid = $('#edit-uid').text();
    var status = $('#edit-status').val();
    var permission = $('#edit-permission').val();
    var reason = $('#edit-reason').val();

    layer.confirm('确定要更新此用户的信息吗？', {icon: 3, title: '确认更新'}, function(index) {
        // 更新状态
        $.ajax({
            type: "POST",
            dataType: "json",
            url: window.location.href,
            data: {
                mode: 'updateStatus',
                uid: uid,
                status: status,
                reason: reason
            },
            success: function (result) {
                if (result.code == 1) {
                    // 更新权限
                    $.ajax({
                        type: "POST",
                        dataType: "json",
                        url: window.location.href,
                        data: {
                            mode: 'updatePermission',
                            uid: uid,
                            permission: permission,
                            reason: reason
                        },
                        success: function (result2) {
                            if (result2.code == 1) {
                                layer.msg('更新成功', {icon: 1});
                                $('#editUser').modal('hide');
                                setTimeout(function() {
                                    window.location.reload();
                                }, 1000);
                            } else {
                                layer.msg('权限更新失败: ' + result2.msg, {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg('网络错误，权限更新失败', {icon: 2});
                        }
                    });
                } else {
                    layer.msg('状态更新失败: ' + result.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，状态更新失败', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 显示批量操作模态框
function showBatchModal() {
    var selectedCount = $('.user-checkbox:checked').length;
    if (selectedCount === 0) {
        layer.msg('请先选择要操作的用户', {icon: 2});
        return;
    }

    $('#selected-count').text(selectedCount);
    $('#batch-operation').val('');
    $('#batch-status-group').hide();
    $('#batch-reason').val('');
    $('#batchOperation').modal('show');
}

// 执行批量操作
function executeBatchOperation() {
    var operation = $('#batch-operation').val();
    var reason = $('#batch-reason').val();

    if (!operation) {
        layer.msg('请选择操作类型', {icon: 2});
        return;
    }

    if (!reason.trim()) {
        layer.msg('请输入操作原因', {icon: 2});
        return;
    }

    var selectedUids = [];
    $('.user-checkbox:checked').each(function() {
        selectedUids.push($(this).val());
    });

    if (selectedUids.length === 0) {
        layer.msg('没有选中的用户', {icon: 2});
        return;
    }

    var confirmMsg = '确定要对 ' + selectedUids.length + ' 个用户执行批量操作吗？';

    layer.confirm(confirmMsg, {icon: 3, title: '确认批量操作'}, function(index) {
        if (operation === 'status') {
            var status = $('#batch-status').val();
            $.ajax({
                type: "POST",
                dataType: "json",
                url: window.location.href,
                data: {
                    mode: 'batchUpdateStatus',
                    uids: selectedUids,
                    status: status,
                    reason: reason
                },
                success: function (result) {
                    if (result.code == 1) {
                        layer.msg('批量操作成功', {icon: 1});
                        $('#batchOperation').modal('hide');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        layer.msg('批量操作失败: ' + result.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，批量操作失败', {icon: 2});
                }
            });
        }
        layer.close(index);
    });
}

// 更新选中数量
function updateSelectedCount() {
    var count = $('.user-checkbox:checked').length;
    $('#selected-count').text(count);
}

// 更新行高亮
function updateRowHighlight() {
    $('.user-checkbox').each(function() {
        var row = $(this).closest('tr');
        if ($(this).prop('checked')) {
            row.addClass('selected-row');
        } else {
            row.removeClass('selected-row');
        }
    });
}

// 获取URL参数的辅助函数
function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return decodeURIComponent(pair[1]);
        }
    }
    return "";
}

// 获取在线用户数
function loadOnlineCount() {
    $.ajax({
        url: '/api/online/current',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.code === 1) {
                $('#online-count').text(response.data.online_count || 0);
            } else {
                $('#online-count').text('0');
            }
        },
        error: function() {
            $('#online-count').text('-');
        }
    });
}

// 页面加载完成后获取在线用户数
$(document).ready(function() {
    loadOnlineCount();
    // 每30秒更新一次在线用户数
    setInterval(loadOnlineCount, 30000);
});
</script>

<style>
/* 统计卡片样式优化 */
.stats-card-link {
    display: block;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.stats-card-link:hover {
    text-decoration: none;
    color: inherit;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.status-online {
    color: #1E9FFF;
}
</style>